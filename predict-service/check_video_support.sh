#!/bin/bash

# Video encoder support check script
# Used to verify video encoders work properly in Docker container
echo "🔍 Checking video encoder support..."

# Check if ffmpeg is available
if ! command -v ffmpeg &> /dev/null; then
    echo "❌ FFmpeg not installed"
    exit 1
fi

echo "✅ FFmpeg installed: $(ffmpeg -version | head -n1)"

# Check key encoders
echo "🔍 Checking key encoders..."

# Check H264 encoder
if ffmpeg -encoders 2>/dev/null | grep -q "libx264\|h264"; then
    echo "✅ H264 encoder available"
    H264_AVAILABLE=true
else
    echo "⚠️ H264 encoder not available"
    H264_AVAILABLE=false
fi

# Check XVID encoder
if ffmpeg -encoders 2>/dev/null | grep -q "libxvid\|xvid"; then
    echo "✅ XVID encoder available"
    XVID_AVAILABLE=true
else
    echo "⚠️ XVID encoder not available"
    XVID_AVAILABLE=false
fi

# Check MJPEG encoder
if ffmpeg -encoders 2>/dev/null | grep -q "mjpeg"; then
    echo "✅ MJPEG encoder available"
    MJPEG_AVAILABLE=true
else
    echo "⚠️ MJPEG encoder not available"
    MJPEG_AVAILABLE=false
fi

# Check if at least one encoder is available
if [ "$H264_AVAILABLE" = true ] || [ "$XVID_AVAILABLE" = true ] || [ "$MJPEG_AVAILABLE" = true ]; then
    echo "✅ At least one video encoder is available"
    ENCODERS_OK=true
else
    echo "⚠️ No usable video encoders found, will use uncompressed AVI"
    ENCODERS_OK=false
fi

# Check OpenCV-FFmpeg integration
echo "🔍 Checking OpenCV-FFmpeg integration..."
python3 -c "
import cv2
import sys
try:
    # Check OpenCV build info
    build_info = cv2.getBuildInformation()
    if 'ffmpeg' in build_info.lower():
        print('✅ OpenCV supports FFmpeg')
    else:
        print('⚠️ OpenCV may not support FFmpeg')
    
    # Check VideoWriter supported encoders
    print('🔍 Testing VideoWriter encoders...')
    
    # Test basic VideoWriter functionality
    import tempfile
    import os
    import numpy as np
    
    with tempfile.NamedTemporaryFile(suffix='.avi', delete=False) as tmp:
        tmp_path = tmp.name
    
    try:
        # Create test frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test uncompressed AVI (should always work)
        fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        out = cv2.VideoWriter(tmp_path, fourcc, 30.0, (640, 480))
        
        if out.isOpened():
            out.write(frame)
            out.release()
            print('✅ VideoWriter basic functionality works')
        else:
            print('❌ VideoWriter basic functionality failed')
            sys.exit(1)
            
    except Exception as e:
        print(f'❌ VideoWriter test failed: {e}')
        sys.exit(1)
    finally:
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
            
except ImportError as e:
    print(f'❌ OpenCV import failed: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ OpenCV check failed: {e}')
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    echo "✅ OpenCV video functionality check passed"
else
    echo "❌ OpenCV video functionality check failed"
    exit 1
fi

# Final summary
echo ""
echo "📊 Video support check summary:"
echo "- FFmpeg: ✅ Available"
if [ "$H264_AVAILABLE" = true ]; then
    echo "- H264 encoder: ✅ Available"
else
    echo "- H264 encoder: ❌ Not available"
fi

if [ "$XVID_AVAILABLE" = true ]; then
    echo "- XVID encoder: ✅ Available"
else
    echo "- XVID encoder: ❌ Not available"
fi

if [ "$MJPEG_AVAILABLE" = true ]; then
    echo "- MJPEG encoder: ✅ Available"
else
    echo "- MJPEG encoder: ❌ Not available"
fi

echo "- OpenCV integration: ✅ Normal"

if [ "$ENCODERS_OK" = true ]; then
    echo ""
    echo "🎉 Video encoder support check completed - System ready"
    exit 0
else
    echo ""
    echo "⚠️ Video encoder support has limitations - System will use uncompressed format"
    # Don't exit, let system continue running
    exit 0
fi 