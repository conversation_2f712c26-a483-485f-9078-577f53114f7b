@echo off
chcp 65001
echo 🚀 启动超级安全调试模式的 Python 应用程序
echo ===============================================

REM 设置时间戳
set datetime=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set datetime=%datetime: =0%

echo 📅 启动时间: %datetime%
echo 📁 当前目录: %cd%

REM 🚀 步骤1: 设置超级安全的 Python 环境变量
echo 🔍 设置超级安全 Python 调试环境变量...
set PYTHONFAULTHANDLER=1
set PYTHONDEBUG=1
set PYTHONVERBOSE=0
set PYTHONASYNCIODEBUG=1
set PYTHONDEV=1
set PYTHONMALLOC=debug
set PYTHONMALLOCSTATS=1
echo ✅ Python 调试变量已设置

REM 🚀 步骤2: 设置内存和性能优化
echo 🧠 设置超级安全内存和性能优化...
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set NUMBA_NUM_THREADS=1
set OPENBLAS_NUM_THREADS=1
set VECLIB_MAXIMUM_THREADS=1
set KMP_DUPLICATE_LIB_OK=TRUE
set MKL_THREADING_LAYER=sequential
echo ✅ 多线程冲突预防已设置

REM 🚀 步骤3: 禁用所有GPU/CUDA
echo 🔧 完全禁用 CUDA/GPU...
set CUDA_VISIBLE_DEVICES=-1
set ENABLE_CUDA=false
set CUDA_LAUNCH_BLOCKING=1
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
echo ✅ CUDA 完全禁用

REM 🚀 步骤4: 设置 OpenCV 和视频处理环境
echo 🎬 设置超级安全 OpenCV 和视频处理环境...
set OPENCV_DNN_BACKEND_INFERENCE_ENGINE_TYPE=CPU
set OPENCV_DNN_TARGET=CPU
set OPENCV_LOG_LEVEL=ERROR
set FFMPEG_LOG_LEVEL=error
set OPENCV_NUM_THREADS=1
echo ✅ OpenCV 超级安全模式已设置

REM 🚀 步骤5: PaddleOCR 版本控制
echo 🏊 设置 PaddleOCR 版本控制...
set FLAGS_allocator_strategy=auto_growth
set FLAGS_fraction_of_gpu_memory_to_use=0.05
set FLAGS_eager_delete_tensor_gb=0.0
set CPU_NUM=1
set PP_OCR_VERSION=PP-OCRv3
echo ✅ PaddleOCR v3 强制设置完成

REM 🚀 步骤6: 内存调试设置
echo 🔍 设置内存调试环境...
set MALLOC_CHECK_=2
set MALLOC_PERTURB_=10
echo ✅ 内存调试已启用

REM 🚀 步骤7: 显示所有关键环境变量
echo.
echo 📋 超级安全模式环境变量:
echo    PYTHONFAULTHANDLER: %PYTHONFAULTHANDLER%
echo    PYTHONMALLOC: %PYTHONMALLOC%
echo    CUDA_VISIBLE_DEVICES: %CUDA_VISIBLE_DEVICES%
echo    OMP_NUM_THREADS: %OMP_NUM_THREADS%
echo    MKL_NUM_THREADS: %MKL_NUM_THREADS%
echo    PP_OCR_VERSION: %PP_OCR_VERSION%
echo    OPENCV_NUM_THREADS: %OPENCV_NUM_THREADS%

REM 🚀 步骤8: 检查 Python 环境
echo.
echo 🐍 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python 未找到或无法运行
    pause
    exit /b 1
)

echo.
echo 📦 检查关键依赖包版本...
python -c "import torch; print(f'PyTorch: {torch.__version__} (CUDA: {torch.cuda.is_available()})')" 2>nul || echo ⚠️  PyTorch 未安装
python -c "import cv2; print(f'OpenCV: {cv2.__version__}')" 2>nul || echo ⚠️  OpenCV 未安装
python -c "import numpy; print(f'NumPy: {numpy.__version__}')" 2>nul || echo ⚠️  NumPy 未安装
python -c "import psutil; print(f'PSUtil: {psutil.__version__}')" 2>nul || echo ⚠️  PSUtil 未安装

echo.
echo 🚀 启动超级安全调试应用程序...
echo 📁 所有日志将保存到当前目录
echo 🔍 内存监控已启用
echo 💾 定期垃圾回收已启用
echo 🛡️  超级安全模式激活
echo.

REM 🚀 步骤9: 运行超级安全版本
python debug_app_safe.py

REM 🚀 步骤10: 检查退出状态
set exit_code=%errorlevel%
echo.
echo 📊 程序退出代码: %exit_code%

if %exit_code% neq 0 (
    echo ❌ 程序异常退出 (代码: %exit_code%)
    echo 🔍 请检查以下文件获取更多信息:
    echo    - crash_*.log (C级别崩溃信息)
    echo    - debug_safe_*.log (超级安全Python调试信息)
    echo.
    
    REM 显示最新的崩溃日志文件
    echo 📄 最新的日志文件:
    dir /b /o-d crash_*.log debug_safe_*.log 2>nul | head -n 3
    
    echo.
    echo 💡 超级安全模式建议的下一步:
    echo    1. 查看超级安全调试日志文件
    echo    2. 检查内存使用情况和泄漏点
    echo    3. 分析视频处理帧数和崩溃点
    echo    4. 使用 /memory-status 端点监控内存
    echo    5. 考虑降低视频处理的批处理大小
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
echo 按任意键退出...
pause >nul 