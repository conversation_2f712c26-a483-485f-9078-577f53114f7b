#!/bin/bash

echo "========== Starting predict-service =========="
echo "Current directory: $(pwd)"

# 检查Python安装状态
echo "========== Python installation check =========="
which python3
which python
ls -la /usr/bin/python*

echo "Python3 version: $(python3 --version)"
if command -v python >/dev/null 2>&1; then
    echo "Python version: $(python --version)"
else
    echo "python command not found, using python3"
fi

# 检查CUDA安装
echo "========== CUDA installation check =========="
echo "NVCC version:"
nvcc --version
echo "CUDA_HOME: $CUDA_HOME"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

echo "CUDA version: $(nvidia-smi | grep 'CUDA Version' 2>/dev/null || echo 'nvidia-smi not available')"
echo "GPU info:"
nvidia-smi 2>/dev/null || echo "nvidia-smi not available"

echo "========== Environment variables =========="
env | sort

echo "========== Directory structure =========="
ls -la /app

echo "========== Checking Python packages =========="
python3 -m pip list

echo "========== Starting python3 app.py =========="
exec python3 -u app.py