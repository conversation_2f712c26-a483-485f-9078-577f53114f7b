#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频合成环境区分测试脚本
Video Synthesis Environment Differentiation Test
==============================================

测试本地开发环境使用OpenCV，生产环境使用FFmpeg的视频合成功能
"""

import os
import logging
import sys
import tempfile
import numpy as np
import cv2
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_environment_detection():
    """测试环境检测功能"""
    logger.info("=== 测试环境检测功能 ===")

    try:
        # 测试默认环境
        original_env = os.environ.get('ENVIRONMENT')

        # 测试开发环境
        os.environ['ENVIRONMENT'] = 'dev'
        from app import ApplicationState

        app_state_dev = ApplicationState()
        assert app_state_dev.environment == 'dev', f"开发环境检测失败: {app_state_dev.environment}"
        assert app_state_dev.video_synthesis_method == 'opencv', f"开发环境应使用OpenCV: {app_state_dev.video_synthesis_method}"

        logger.info(" 开发环境配置正确:")
        logger.info(f" 环境: {app_state_dev.environment}")
        logger.info(f" 视频合成方法: {app_state_dev.video_synthesis_method}")

        # 测试生产环境
        os.environ['ENVIRONMENT'] = 'prod'
        # 重新导入以获取新的环境变量
        import importlib
        import app
        importlib.reload(app)

        app_state_prod = app.ApplicationState()
        assert app_state_prod.environment == 'prod', f"生产环境检测失败: {app_state_prod.environment}"
        assert app_state_prod.video_synthesis_method == 'ffmpeg', f"生产环境应使用FFmpeg: {app_state_prod.video_synthesis_method}"

        logger.info(" 生产环境配置正确:")
        logger.info(f" 环境: {app_state_prod.environment}")
        logger.info(f" 视频合成方法: {app_state_prod.video_synthesis_method}")

        # 恢复原始环境变量
        if original_env:
            os.environ['ENVIRONMENT'] = original_env
        elif 'ENVIRONMENT' in os.environ:
            del os.environ['ENVIRONMENT']

        return True

    except Exception as e:
        logger.error(f" 环境检测测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_enhanced_video_processor_environment():
    """测试EnhancedVideoProcessor的环境区分"""
    logger.info("=== 测试 EnhancedVideoProcessor 环境区分 ===")

    try:
        from core.processors.enhanced_video_processor import EnhancedVideoProcessor

        # 测试开发环境
        logger.info(" 测试开发环境配置...")
        os.environ['ENVIRONMENT'] = 'dev'
        os.environ['VIDEO_SYNTHESIS_METHOD'] = 'opencv'

        # 创建模拟的处理器参数
        from core.config.config_manager import ConfigManager
        config = ConfigManager()
        mock_ocr_processor = None

        processor_dev = EnhancedVideoProcessor(config, mock_ocr_processor, enable_tracking=True)
        assert processor_dev.environment == 'dev', f"环境设置错误: {processor_dev.environment}"
        assert processor_dev.video_synthesis_method == 'opencv', f"视频合成方法错误: {processor_dev.video_synthesis_method}"

        logger.info(" 开发环境 EnhancedVideoProcessor 配置正确")
        logger.info(f" 环境: {processor_dev.environment}")
        logger.info(f" 视频合成方法: {processor_dev.video_synthesis_method}")

        # 测试生产环境
        logger.info(" 测试生产环境配置...")
        os.environ['ENVIRONMENT'] = 'prod'
        os.environ['VIDEO_SYNTHESIS_METHOD'] = 'ffmpeg'

        processor_prod = EnhancedVideoProcessor(config, mock_ocr_processor, enable_tracking=True)
        assert processor_prod.environment == 'prod', f"环境设置错误: {processor_prod.environment}"
        assert processor_prod.video_synthesis_method == 'ffmpeg', f"视频合成方法错误: {processor_prod.video_synthesis_method}"

        logger.info(" 生产环境 EnhancedVideoProcessor 配置正确")
        logger.info(f" 环境: {processor_prod.environment}")
        logger.info(f" 视频合成方法: {processor_prod.video_synthesis_method}")

        return True

    except Exception as e:
        logger.error(f" EnhancedVideoProcessor环境测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_opencv_video_synthesis():
    """测试OpenCV视频合成功能"""
    logger.info("=== 测试 OpenCV 视频合成功能 ===")

    try:
        # 创建测试帧
        frames = []
        for i in range(10):
            # 创建彩色测试帧
            frame = np.zeros((240, 320, 3), dtype=np.uint8)
            frame[:, :, 0] = (i * 25) % 256 # 蓝色通道变化
            frame[:, :, 1] = 128 # 绿色通道固定
            frame[:, :, 2] = 255 - (i * 25) % 256 # 红色通道反向变化

            # 添加帧数文字
            cv2.putText(frame, f"Frame {i+1}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            frames.append(frame)

        # 测试OpenCV视频写入
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            output_path = temp_file.name

        try:
            # 使用cv2.VideoWriter
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, 10.0, (320, 240))

            if not writer.isOpened():
                logger.error(" OpenCV VideoWriter 无法打开")
                return False

            for frame in frames:
                writer.write(frame)

            writer.release()

            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(" OpenCV视频合成成功")
                logger.info(f" 输出文件: {output_path}")
                logger.info(f" 文件大小: {os.path.getsize(output_path)} bytes")

                # 验证可以读取
                cap = cv2.VideoCapture(output_path)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()

                logger.info(f" 帧数验证: {frame_count}/10")

                success = frame_count == 10
                if success:
                    logger.info(" OpenCV视频合成完全成功")
                else:
                    logger.warning(" OpenCV视频合成部分成功（帧数不匹配）")

                return success
            else:
                logger.error(" OpenCV视频合成失败 - 无输出文件或文件为空")
                return False

        finally:
            # 清理临时文件
            if os.path.exists(output_path):
                os.unlink(output_path)

    except Exception as e:
        logger.error(f" OpenCV视频合成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_ffmpeg_availability():
    """测试FFmpeg可用性"""
    logger.info("=== 测试 FFmpeg 可用性 ===")

    try:
        import subprocess

        # 测试FFmpeg命令
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            # 提取FFmpeg版本信息
            version_line = result.stdout.split('\n')[0]
            logger.info(" FFmpeg 可用")
            logger.info(f" 版本: {version_line}")

            # 测试ffmpeg-python包
            try:
                import ffmpeg
                logger.info(" ffmpeg-python 包可用")
                ffmpeg_python_available = True
            except ImportError:
                logger.warning(" ffmpeg-python 包不可用")
                ffmpeg_python_available = False

            return True, ffmpeg_python_available
        else:
            logger.error(" FFmpeg 不可用")
            logger.error(f" 错误: {result.stderr}")
            return False, False

    except FileNotFoundError:
        logger.error(" FFmpeg 未安装或不在PATH中")
        return False, False
    except subprocess.TimeoutExpired:
        logger.error(" FFmpeg 命令超时")
        return False, False
    except Exception as e:
        logger.error(f" FFmpeg 测试失败: {e}")
        return False, False


def test_environment_based_video_synthesis():
    """测试基于环境的视频合成方法选择"""
    logger.info("=== 测试基于环境的视频合成方法选择 ===")

    try:
        from core.processors.enhanced_video_processor import EnhancedVideoProcessor

        # 创建测试帧
        test_frames = []
        for i in range(5):
            frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            test_frames.append(frame)

        # 模拟保存帧到临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存测试帧
            for i, frame in enumerate(test_frames):
                frame_path = os.path.join(temp_dir, f"frame_{i:06d}.jpg")
                cv2.imwrite(frame_path, frame)

            # 测试开发环境 - 应该使用OpenCV
            logger.info(" 测试开发环境视频合成...")
            os.environ['ENVIRONMENT'] = 'dev'

            processor_dev = EnhancedVideoProcessor(None, None, enable_tracking=False)
            processor_dev.temp_frames_dir = temp_dir
            processor_dev.frame_count = len(test_frames)

            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_output:
                dev_output_path = temp_output.name

            try:
                # 这应该调用OpenCV合成
                dev_success = processor_dev.synthesize_video(dev_output_path, 10.0)

                if dev_success and os.path.exists(dev_output_path) and os.path.getsize(dev_output_path) > 0:
                    logger.info(" 开发环境视频合成成功 (OpenCV)")
                    dev_test_success = True
                else:
                    logger.warning(" 开发环境视频合成失败")
                    dev_test_success = False

            finally:
                if os.path.exists(dev_output_path):
                    os.unlink(dev_output_path)

            # 测试生产环境 - 应该尝试使用FFmpeg
            logger.info(" 测试生产环境视频合成...")
            os.environ['ENVIRONMENT'] = 'prod'

            processor_prod = EnhancedVideoProcessor(None, None, enable_tracking=False)
            processor_prod.temp_frames_dir = temp_dir
            processor_prod.frame_count = len(test_frames)

            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_output:
                prod_output_path = temp_output.name

            try:
                # 这应该尝试FFmpeg合成，如果失败则回退到OpenCV
                prod_success = processor_prod.synthesize_video(prod_output_path, 10.0)

                if prod_success and os.path.exists(prod_output_path) and os.path.getsize(prod_output_path) > 0:
                    logger.info(" 生产环境视频合成成功 (FFmpeg或OpenCV备用)")
                    prod_test_success = True
                else:
                    logger.warning(" 生产环境视频合成失败")
                    prod_test_success = False

            finally:
                if os.path.exists(prod_output_path):
                    os.unlink(prod_output_path)

        # 总结结果
        if dev_test_success and prod_test_success:
            logger.info(" 环境基础视频合成测试完全成功")
            return True
        elif dev_test_success or prod_test_success:
            logger.info(" 环境基础视频合成测试部分成功")
            return True
        else:
            logger.error(" 环境基础视频合成测试失败")
            return False

    except Exception as e:
        logger.error(f" 环境基础视频合成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_config_manager_environment():
    """测试ConfigManager的环境区分"""
    logger.info("=== 测试 ConfigManager 环境区分 ===")

    try:
        from core.managers.config_manager import ConfigManager

        # 测试开发环境配置
        os.environ['ENVIRONMENT'] = 'dev'
        config_dev = ConfigManager()
        env_dev = config_dev.get_environment()

        logger.info(f" 开发环境配置: {env_dev}")

        # 测试生产环境配置
        os.environ['ENVIRONMENT'] = 'prod'
        config_prod = ConfigManager()
        env_prod = config_prod.get_environment()

        logger.info(f" 生产环境配置: {env_prod}")

        assert env_dev == 'dev', f"开发环境配置错误: {env_dev}"
        assert env_prod == 'prod', f"生产环境配置错误: {env_prod}"

        logger.info(" ConfigManager环境区分测试成功")
        return True

    except Exception as e:
        logger.error(f" ConfigManager环境测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info(" 开始视频合成环境区分测试")

    tests = [
        ("环境检测", test_environment_detection),
        ("ConfigManager环境区分", test_config_manager_environment),
        ("EnhancedVideoProcessor环境区分", test_enhanced_video_processor_environment),
        ("OpenCV视频合成", test_opencv_video_synthesis),
        ("FFmpeg可用性", lambda: test_ffmpeg_availability()[0]), # 只关心是否可用
        ("环境基础视频合成", test_environment_based_video_synthesis)
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*60}")

        try:
            success = test_func()
            results.append((test_name, success))

            if success:
                logger.info(f" 测试通过: {test_name}")
            else:
                logger.error(f" 测试失败: {test_name}")
        except Exception as e:
            logger.error(f" 测试异常: {test_name} - {e}")
            results.append((test_name, False))

    # 汇总结果
    logger.info(f"\n{'='*60}")
    logger.info("测试结果汇总")
    logger.info(f"{'='*60}")

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = " 通过" if success else " 失败"
        logger.info(f"{status} - {test_name}")

    logger.info(f"\n总结: {passed}/{total} 个测试通过")

    # 额外的环境总结
    logger.info(f"\n{'='*60}")
    logger.info("视频合成环境配置总结")
    logger.info(f"{'='*60}")

    # 检查FFmpeg
    ffmpeg_available, ffmpeg_python_available = test_ffmpeg_availability()

    logger.info(" 当前环境支持情况:")
    logger.info(f" • OpenCV: 可用")
    logger.info(f" • FFmpeg命令: {' 可用' if ffmpeg_available else ' 不可用'}")
    logger.info(f" • ffmpeg-python: {' 可用' if ffmpeg_python_available else ' 不可用'}")

    logger.info("\n 环境配置说明:")
    logger.info(" • 开发环境 (ENVIRONMENT=dev): 使用OpenCV进行视频合成")
    logger.info(" • 生产环境 (ENVIRONMENT=prod): 使用FFmpeg进行视频合成")
    logger.info(" • 备用机制: 主方法失败时自动尝试备用方法")

    if passed == total:
        logger.info("\n 所有测试都通过了！视频合成环境区分功能实现正确！")
        return True
    else:
        logger.error(f"\n {total-passed} 个测试失败，请检查实现")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)