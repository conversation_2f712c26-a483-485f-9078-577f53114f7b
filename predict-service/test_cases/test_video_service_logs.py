#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import uuid

def test_video_service_logs():
    """测试video_service的日志输出"""
    
    # 测试数据
    test_data = {
        'videoPath': 'test/video.mp4',
        'videoId': 'test_video_123',
        'taskId': 'test_task_456',
        'userId': 'test_user_789',
        'originalName': 'test_video',
        'currentVideoPathDir': '/tmp/test',
        'confidenceThreshold': 0.5,
        'gasLeakageMode': True,
        'request_uuid': str(uuid.uuid4())
    }
    
    print(f"发送测试请求到 /process-video 端点...")
    print(f"请求UUID: {test_data['request_uuid']}")
    
    try:
        response = requests.post(
            'http://localhost:9020/process-video',
            json=test_data,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 请求成功发送，检查服务器日志中是否有video_service的日志输出")
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保应用程序正在运行")
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
    except Exception as e:
        print(f"✗ 请求失败: {e}")

if __name__ == '__main__':
    test_video_service_logs()