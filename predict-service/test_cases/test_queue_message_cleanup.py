#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证queue_message字段清理后的功能

目的：
1. 验证移除冗余字段后，核心功能仍然正常
2. 确认保留的字段能够正常生成和序列化
3. 检查algorithmInfo字段的数据完整性
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.video_service import VideoService
from core.managers.config_manager import ConfigManager

def test_queue_message_structure():
    """测试queue_message的数据结构完整性"""
    print("\n=== 测试queue_message数据结构 ===")
    
    # 创建模拟的配置管理器
    mock_config = Mock(spec=ConfigManager)
    mock_config.get_video_config.return_value = {
        'max_workers': 4,
        'chunk_size': 1000,
        'enable_gpu': False
    }
    
    # 创建VideoService实例
    video_service = VideoService(config_manager=mock_config)
    
    # 模拟frame_result数据
    mock_frame_result = {
        'gas_mode': True,
        'model_info': {'model_name': 'yolov8n'},
        'detections': [
            {'confidence': 0.85, 'bbox': [100, 100, 200, 200]},
            {'confidence': 0.92, 'bbox': [300, 150, 400, 250]}
        ],
        'gas_tracking_results': {
            'tracked_objects': [{'id': 1, 'confidence': 0.88}],
            'camera_motion': {'motion_detected': True}
        }
    }
    
    # 模拟gas_analysis_summary数据
    mock_gas_analysis_summary = {
        'total_leakage_points': 3,
        'high_risk_areas': 1,
        'tracking_confidence': 0.87
    }
    
    # 模拟temporal_gps_summary数据
    mock_temporal_gps_summary = {
        'gps_analysis': {'total_points': 150, 'valid_coordinates': 148},
        'temporal_analysis': {'duration': 120, 'fps': 30},
        'watermark_analysis': {'quality_score': 0.95}
    }
    
    # 模拟progress数据
    mock_progress = {
        'current_frame': 100,
        'total_frames': 1000,
        'progress_percentage': 10.0,
        'processing_speed': 25.5
    }
    
    # 使用patch模拟内部方法
    with patch.object(video_service, '_generate_gas_analysis_summary', return_value=mock_gas_analysis_summary), \
         patch.object(video_service, '_generate_temporal_gps_summary', return_value=mock_temporal_gps_summary):
        
        # 调用_update_task_progress方法
        try:
            video_service._update_task_progress(
                task_id="test_task_123",
                progress=mock_progress,
                frame_result=mock_frame_result,
                gas_leakage_mode=True
            )
            print("✓ _update_task_progress方法执行成功")
        except Exception as e:
            print(f"✗ _update_task_progress方法执行失败: {e}")
            return False
    
    return True

def test_algorithm_info_field():
    """测试algorithmInfo字段的数据完整性"""
    print("\n=== 测试algorithmInfo字段 ===")
    
    # 测试数据
    test_cases = [
        {
            'name': '气体泄漏模式',
            'frame_result': {
                'gas_mode': True,
                'model_info': {'model_name': 'yolov8s'}
            },
            'expected': {
                'gas_tracker_enabled': True,
                'detection_model': 'yolov8s',
                'tracker_type': 'enhanced_gas_leakage_tracker',
                'frame_processing_method': 'sam_segmentation'
            }
        },
        {
            'name': '标准模式',
            'frame_result': {
                'gas_mode': False,
                'model_info': {'model_name': 'yolov8n'}
            },
            'expected': {
                'gas_tracker_enabled': False,
                'detection_model': 'yolov8n',
                'tracker_type': 'standard',
                'frame_processing_method': 'bounding_box'
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        frame_result = case['frame_result']
        expected = case['expected']
        
        # 构建algorithmInfo
        algorithm_info = {
            'gas_tracker_enabled': frame_result.get('gas_mode', False),
            'detection_model': frame_result.get('model_info', {}).get('model_name', 'yolov8'),
            'tracker_type': 'enhanced_gas_leakage_tracker' if frame_result.get('gas_mode') else 'standard',
            'frame_processing_method': 'sam_segmentation' if frame_result.get('gas_mode') else 'bounding_box'
        }
        
        # 验证数据
        try:
            json_str = json.dumps(algorithm_info)
            parsed = json.loads(json_str)
            
            # 检查每个字段
            for key, expected_value in expected.items():
                if parsed.get(key) != expected_value:
                    print(f"✗ 字段 {key} 不匹配: 期望 {expected_value}, 实际 {parsed.get(key)}")
                    return False
                else:
                    print(f"✓ 字段 {key}: {parsed.get(key)}")
                    
        except Exception as e:
            print(f"✗ JSON序列化失败: {e}")
            return False
    
    return True

def test_removed_fields_impact():
    """测试移除字段对系统的影响"""
    print("\n=== 测试移除字段的影响 ===")
    
    removed_fields = [
        'motionPatternDistribution',
        'shapeFeatures', 
        'irregularShapeDetected',
        'mainDispersionDirection',
        'byteTrackStatus',
        'videoGpsInfo',
        'videoTimeInfo', 
        'watermarkQuality'
    ]
    
    print("已移除的冗余字段:")
    for field in removed_fields:
        print(f"  - {field}")
    
    print("\n移除原因:")
    reasons = {
        'motionPatternDistribution': '只在gas_analysis_summary中有数据，但大部分情况下为空',
        'shapeFeatures': 'frame_result.detections中缺少shape_analysis字段',
        'irregularShapeDetected': '同上，shape_analysis字段不存在',
        'mainDispersionDirection': 'gas_tracking_result拼写错误且字段不存在',
        'byteTrackStatus': 'tracker_status字段在代码中未找到生成逻辑',
        'videoGpsInfo': '已通过temporalGpsSummary提供完整数据',
        'videoTimeInfo': '已通过temporalGpsSummary提供完整数据',
        'watermarkQuality': '已通过temporalGpsSummary提供完整数据'
    }
    
    for field, reason in reasons.items():
        print(f"  - {field}: {reason}")
    
    print("\n✓ 字段清理完成，保留了所有有实际数据来源的核心字段")
    return True

def main():
    """主测试函数"""
    print("开始测试queue_message字段清理...")
    
    tests = [
        test_queue_message_structure,
        test_algorithm_info_field,
        test_removed_fields_impact
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_func.__name__} 通过")
            else:
                print(f"✗ {test_func.__name__} 失败")
        except Exception as e:
            print(f"✗ {test_func.__name__} 异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！queue_message字段清理成功完成。")
    else:
        print("\n⚠️  部分测试失败，请检查相关代码。")

if __name__ == "__main__":
    main()