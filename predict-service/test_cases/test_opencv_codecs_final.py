#!/usr/bin/env python3
"""
OpenCV编码器最终测试 - 针对当前稳定版本优化
适用于：OpenCV 4.6.0.66 + PaddlePaddle 2.6.2
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

def test_opencv_info():
    """测试OpenCV基本信息"""
    print("=== OpenCV 基本信息 ===")
    print(f"OpenCV版本: {cv2.__version__}")
    print(f"构建信息:")
    build_info = cv2.getBuildInformation()

    # 查找关键信息
    for line in build_info.split('\n'):
        if any(keyword in line.lower() for keyword in ['ffmpeg', 'gstreamer', 'cuda', 'video']):
            print(f" {line.strip()}")

    print(f"\n支持的后端:")
    backends = [
        (cv2.CAP_FFMPEG, "FFmpeg"),
        (cv2.CAP_GSTREAMER, "GStreamer"),
        (cv2.CAP_OPENCV_MJPEG, "OpenCV MJPEG"),
    ]

    for backend_id, name in backends:
        try:
            cap = cv2.VideoCapture()
            if cap.open("test", backend_id):
                print(f" {name} 支持")
            else:
                print(f" {name} 不支持")
            cap.release()
        except:
            print(f" {name} 测试失败")

def create_test_frames(width=640, height=480, num_frames=30):
    """创建测试帧"""
    frames = []
    for i in range(num_frames):
        # 创建彩色测试图像
        frame = np.zeros((height, width, 3), dtype=np.uint8)

        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(255 * x / width),
                    int(255 * y / height),
                    int(255 * (i / num_frames))
                ]

        # 添加文本
        cv2.putText(frame, f"Frame {i+1}/{num_frames}",
                   (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # 添加移动的圆圈
        center_x = int(width/2 + 100 * np.sin(i * 0.2))
        center_y = int(height/2 + 50 * np.cos(i * 0.2))
        cv2.circle(frame, (center_x, center_y), 30, (0, 255, 255), -1)

        frames.append(frame)

    return frames

def test_codec_combinations():
    """测试各种编码器和容器组合"""
    print("\n=== 编码器兼容性测试 ===")

    # 扩展的编码器列表，按优先级排序
    codecs_to_test = [
        # H.264相关 - 最常用
        ('mp4v', '.mp4', "MPEG-4 Part 2"),
        ('XVID', '.avi', "Xvid MPEG-4"),
        ('MJPG', '.avi', "Motion JPEG"),

        # 其他格式
        ('DIVX', '.avi', "DivX MPEG-4"),
        ('X264', '.mp4', "x264 H.264"),
        ('H264', '.mp4', "H.264"),
        ('avc1', '.mp4', "AVC1 H.264"),

        # 备选格式
        ('FMP4', '.mp4', "FFmpeg MPEG-4"),
        ('MP42', '.avi', "Microsoft MPEG-4 v2"),
        ('MP43', '.avi', "Microsoft MPEG-4 v3"),
        ('WMV1', '.wmv', "Windows Media Video 7"),
        ('WMV2', '.wmv', "Windows Media Video 8"),

        # 无损/高质量
        ('LAGS', '.avi', "Lagarith Lossless"),
        ('HFYU', '.avi', "HuffYUV Lossless"),

        # 现代编码器
        ('VP80', '.webm', "VP8"),
        ('VP90', '.webm', "VP9"),
        ('AV01', '.mp4', "AV1"),
        ('HEVC', '.mp4', "H.265/HEVC"),
        ('H265', '.mp4', "H.265"),
    ]

    frames = create_test_frames(320, 240, 10) # 小尺寸快速测试
    successful_codecs = []

    for fourcc_str, ext, description in codecs_to_test:
        try:
            # 创建FourCC
            if len(fourcc_str) == 4:
                fourcc = cv2.VideoWriter_fourcc(*fourcc_str)
            else:
                continue

            filename = f"test_output_{fourcc_str}{ext}"

            # 测试写入
            writer = cv2.VideoWriter(filename, fourcc, 10.0, (320, 240))

            if not writer.isOpened():
                print(f" {fourcc_str:4s} ({description}): 无法打开写入器")
                continue

            # 写入几帧测试
            success_count = 0
            for i, frame in enumerate(frames[:5]): # 只测试前5帧
                if writer.write(frame):
                    success_count += 1
                else:
                    break

            writer.release()

            # 检查文件是否创建且有内容
            if os.path.exists(filename) and os.path.getsize(filename) > 1000:
                # 尝试读取验证
                cap = cv2.VideoCapture(filename)
                if cap.isOpened():
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        print(f" {fourcc_str:4s} ({description}): 成功 - {success_count}/5帧")
                        successful_codecs.append((fourcc_str, ext, description))
                    else:
                        print(f" {fourcc_str:4s} ({description}): 写入成功但读取失败")
                else:
                    print(f" {fourcc_str:4s} ({description}): 写入成功但无法打开")
                cap.release()
            else:
                print(f" {fourcc_str:4s} ({description}): 文件创建失败")

            # 清理测试文件
            if os.path.exists(filename):
                os.remove(filename)

        except Exception as e:
            print(f" {fourcc_str:4s} ({description}): 异常 - {str(e)}")

    return successful_codecs

def test_2x2_video_merge(successful_codecs):
    """测试2x2视频合成功能"""
    print("\n=== 2x2视频合成测试 ===")

    if not successful_codecs:
        print("没有可用的编码器，跳过合成测试")
        return

    # 使用第一个成功的编码器
    fourcc_str, ext, description = successful_codecs[0]
    print(f"使用编码器: {fourcc_str} ({description})")

    try:
        # 创建4个不同的视频源
        sources = []
        for i in range(4):
            frames = []
            for frame_idx in range(20):
                frame = np.zeros((240, 320, 3), dtype=np.uint8)

                # 不同颜色的背景
                colors = [(255,0,0), (0,255,0), (0,0,255), (255,255,0)]
                frame[:] = colors[i]

                # 添加标识
                cv2.putText(frame, f"Camera {i+1}", (50, 120),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(frame, f"Frame {frame_idx+1}", (50, 160),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                frames.append(frame)
            sources.append(frames)

        # 创建2x2合成视频
        fourcc = cv2.VideoWriter_fourcc(*fourcc_str)
        output_file = f"test_2x2_merge{ext}"
        writer = cv2.VideoWriter(output_file, fourcc, 10.0, (640, 480))

        if not writer.isOpened():
            print(f" 无法创建2x2合成视频写入器")
            return

        # 合成帧
        success_frames = 0
        for frame_idx in range(20):
            # 创建2x2布局
            top_row = np.hstack([sources[0][frame_idx], sources[1][frame_idx]])
            bottom_row = np.hstack([sources[2][frame_idx], sources[3][frame_idx]])
            merged_frame = np.vstack([top_row, bottom_row])

            if writer.write(merged_frame):
                success_frames += 1
            else:
                break

        writer.release()

        # 验证输出
        if os.path.exists(output_file) and os.path.getsize(output_file) > 5000:
            print(f" 2x2视频合成成功！")
            print(f" - 文件: {output_file}")
            print(f" - 大小: {os.path.getsize(output_file)} 字节")
            print(f" - 成功帧数: {success_frames}/20")

            # 验证可读取性
            cap = cv2.VideoCapture(output_file)
            if cap.isOpened():
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                print(f" - 视频信息: {width}x{height}, {frame_count}帧, {fps}fps")

                # 读取第一帧验证
                ret, first_frame = cap.read()
                if ret and first_frame is not None:
                    print(f" - 第一帧读取: 成功 {first_frame.shape}")
                else:
                    print(f" - 第一帧读取: 失败")

            cap.release()

            # 清理文件
            os.remove(output_file)
        else:
            print(f" 2x2视频合成失败")

    except Exception as e:
        print(f" 2x2视频合成异常: {str(e)}")

def create_video_utils_module(successful_codecs):
    """创建优化的视频处理工具模块"""
    if not successful_codecs:
        print("\n没有可用编码器，无法创建视频工具模块")
        return

    print(f"\n=== 创建视频处理工具模块 ===")

    # 选择最佳编码器
    best_codec = successful_codecs[0]
    fourcc_str, ext, description = best_codec

    video_utils_code = f'''#!/usr/bin/env python3
"""
视频处理工具模块 - 针对当前环境优化
OpenCV {cv2.__version__} + PaddlePaddle 2.6.2
最佳编码器: {fourcc_str} ({description})
"""

import cv2
import numpy as np
import os
import logging
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)

class VideoProcessor:
    """视频处理器 - 使用经过测试的编码器"""

    # 经过测试的编码器配置（按优先级排序）
    TESTED_CODECS = {codecs_repr}

    def __init__(self):
        self.default_codec = "{fourcc_str}"
        self.default_ext = "{ext}"

    def get_best_codec(self) -> Tuple[str, str]:
        """获取最佳可用编码器"""
        for fourcc_str, ext, desc in self.TESTED_CODECS:
            if self._test_codec(fourcc_str):
                return fourcc_str, ext

        # 回退到默认
        return self.default_codec, self.default_ext

    def _test_codec(self, fourcc_str: str) -> bool:
        """快速测试编码器是否可用"""
        try:
            fourcc = cv2.VideoWriter_fourcc(*fourcc_str)
            writer = cv2.VideoWriter("temp_test.tmp", fourcc, 10.0, (100, 100))
            available = writer.isOpened()
            writer.release()

            # 清理测试文件
            if os.path.exists("temp_test.tmp"):
                os.remove("temp_test.tmp")

            return available
        except:
            return False

    def create_2x2_video(self,
                        video_sources: List[List[np.ndarray]],
                        output_path: str,
                        fps: float = 10.0,
                        target_size: Tuple[int, int] = (320, 240)) -> bool:
        """
        创建2x2合成视频

        Args:
            video_sources: 4个视频源的帧列表
            output_path: 输出文件路径
            fps: 帧率
            target_size: 每个子视频的目标尺寸

        Returns:
            bool: 是否成功
        """
        if len(video_sources) != 4:
            logger.error("需要恰好4个视频源")
            return False

        try:
            # 获取最佳编码器
            fourcc_str, ext = self.get_best_codec()

            # 确保输出路径有正确的扩展名
            if not output_path.endswith(ext):
                output_path = os.path.splitext(output_path)[0] + ext

            fourcc = cv2.VideoWriter_fourcc(*fourcc_str)

            # 计算输出尺寸（2x2布局）
            output_width = target_size[0] * 2
            output_height = target_size[1] * 2

            writer = cv2.VideoWriter(output_path, fourcc, fps, (output_width, output_height))

            if not writer.isOpened():
                logger.error(f"无法创建视频写入器: {{fourcc_str}}")
                return False

            # 确定最小帧数
            min_frames = min(len(source) for source in video_sources)

            success_count = 0
            for frame_idx in range(min_frames):
                try:
                    # 调整每个源的帧大小
                    resized_frames = []
                    for source in video_sources:
                        frame = source[frame_idx]
                        if frame.shape[:2] != target_size[::-1]: # (height, width)
                            frame = cv2.resize(frame, target_size)
                        resized_frames.append(frame)

                    # 创建2x2布局
                    top_row = np.hstack([resized_frames[0], resized_frames[1]])
                    bottom_row = np.hstack([resized_frames[2], resized_frames[3]])
                    merged_frame = np.vstack([top_row, bottom_row])

                    if writer.write(merged_frame):
                        success_count += 1
                    else:
                        logger.warning(f"写入第{{frame_idx}}帧失败")
                        break

                except Exception as e:
                    logger.error(f"处理第{{frame_idx}}帧时出错: {{e}}")
                    break

            writer.release()

            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                logger.info(f"2x2视频创建成功: {{output_path}} ({{success_count}}/{{min_frames}}帧)")
                return True
            else:
                logger.error("输出文件创建失败或为空")
                return False

        except Exception as e:
            logger.error(f"创建2x2视频时出错: {{e}}")
            return False

    def get_video_info(self, video_path: str) -> dict:
        """获取视频信息"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {{"error": "无法打开视频文件"}}

            info = {{
                "width": int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                "height": int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                "fps": cap.get(cv2.CAP_PROP_FPS),
                "frame_count": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                "duration": cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0
            }}

            cap.release()
            return info

        except Exception as e:
            return {{"error": str(e)}}

# 全局实例
video_processor = VideoProcessor()

def create_2x2_video(video_sources, output_path, fps=10.0, target_size=(320, 240)):
    """便捷函数：创建2x2合成视频"""
    return video_processor.create_2x2_video(video_sources, output_path, fps, target_size)

def get_video_info(video_path):
    """便捷函数：获取视频信息"""
    return video_processor.get_video_info(video_path)

if __name__ == "__main__":
    # 测试代码
    print("视频处理工具模块测试")
    print(f"默认编码器: {{video_processor.default_codec}}")
    print(f"最佳编码器: {{video_processor.get_best_codec()}}")
'''

    # 格式化编码器列表
    codecs_repr = repr(successful_codecs)
    video_utils_code = video_utils_code.replace('{codecs_repr}', codecs_repr)

    # 写入文件
    utils_file = "video_utils_optimized.py"
    with open(utils_file, 'w', encoding='utf-8') as f:
        f.write(video_utils_code)

    print(f" 创建视频工具模块: {utils_file}")
    print(f" 默认编码器: {fourcc_str} ({description})")
    print(f" 可用编码器数量: {len(successful_codecs)}")

def main():
    """主测试函数"""
    print("OpenCV 编码器最终测试")
    print("=" * 50)

    # 测试OpenCV信息
    test_opencv_info()

    # 测试编码器
    successful_codecs = test_codec_combinations()

    if successful_codecs:
        print(f"\n 找到 {len(successful_codecs)} 个可用编码器:")
        for fourcc_str, ext, desc in successful_codecs:
            print(f" - {fourcc_str:4s} ({desc})")

        # 测试2x2合成
        test_2x2_video_merge(successful_codecs)

        # 创建优化的工具模块
        create_video_utils_module(successful_codecs)

    else:
        print("\n 没有找到可用的编码器")
        print("建议检查:")
        print(" 1. OpenCV编译时是否包含FFmpeg支持")
        print(" 2. 系统是否安装了必要的编解码器")
        print(" 3. 考虑使用ffmpeg-python作为备选方案")

    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()