#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 配置UUID日志
try:
    from utils.logging_utils import configure_root_logger_with_uuid, UUIDLogger
    uuid_filter = configure_root_logger_with_uuid()
    print("UUID日志系统配置成功")
except ImportError as e:
    print(f"UUID日志工具导入失败: {e}")
    uuid_filter = None

# 导入video_service
try:
    from services.video_service import VideoService
    from utils.logging_utils import UUIDLogger
    
    # 创建一个简单的配置对象
    class SimpleConfig:
        def __init__(self):
            self.gas_leakage_mode = True
            self.enable_video_output = True
            self.model_path = "./models/test.pt"
    
    config = SimpleConfig()
    
    # 创建VideoService实例（不传入label_service和ocr_processor以避免复杂初始化）
    print("正在创建VideoService实例...")
    try:
        video_service = VideoService(config, None, None)
        print("VideoService创建成功")
        
        # 测试enable_gas_leakage_mode方法
        print("\n测试enable_gas_leakage_mode方法:")
        result = video_service.enable_gas_leakage_mode(True)
        print(f"enable_gas_leakage_mode返回: {result}")
        
        # 测试check_break_flag方法
        print("\n测试check_break_flag方法:")
        result = video_service.check_break_flag("test_task_123")
        print(f"check_break_flag返回: {result}")
        
    except Exception as e:
        print(f"VideoService创建失败: {e}")
        import traceback
        traceback.print_exc()
        
except ImportError as e:
    print(f"导入VideoService失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成")