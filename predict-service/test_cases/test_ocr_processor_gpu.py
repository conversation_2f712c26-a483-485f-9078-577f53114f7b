import logging import cv2 import numpy as np from core.processors.ocr_processor import OCRProcessor # 配置日志 logging.basicConfig(level=logging.INFO) logger = logging.getLogger(__name__) def test_ocr_processor(): """测试 OCRProcessor 的 GPU 初始化和帧处理""" try: ocr = OCRProcessor() logger.info("OCRProcessor 初始化成功") # 创建一个模拟帧 frame = np.zeros((100, 300, 3), dtype=np.uint8) cv2.putText(frame, 'UTC: 12:34:56', (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) cv2.putText(frame, 'Lat: 31.65249', (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) cv2.putText(frame, 'Lon: -103.776149', (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) # 处理帧 result = ocr.process_frame(frame) logger.info(f"提取结果: {result}") # 检查结果 assert result['UTC'] is not None, "未能提取 UTC 时间" assert result['Lat'] is not None, "未能提取纬度" assert result['Lon'] is not None, "未能提取经度" logger.info("所有测试通过") except Exception as e: logger.error(f"测试失败: {str(e)}") raise if __name__ == '__main__': test_ocr_processor()