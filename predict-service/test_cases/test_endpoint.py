#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试端点脚本
"""

import requests
import json

def test_process_video():
    """测试 /process-video 端点"""
    url = "http://localhost:9020/process-video"

    # 模拟Java客户端发送的数据
    test_data = {
        "videoPath": "/test/video.mp4",
        "videoId": 123,
        "taskId": 456,
        "userId": 789,
        "originalName": "test.mp4",
        "confidenceThreshold": 0.5,
        "polyColor": [255, 0, 0],
        "polyThickness": 2,
        "currentVideoPathDir": "/output/path",
        "output": "/output/test",
        "label_config": {
            "label_levels": {
                "Critical": {
                    "color": "#FF0000",
                    "threshold": 0.9,
                    "notification_priority": 1
                },
                "Normal": {
                    "color": "#00FF00",
                    "threshold": 0.7,
                    "notification_priority": 2
                },
                "Others": {
                    "color": "#0000FF",
                    "threshold": 0.5,
                    "notification_priority": 3
                }
            }
        }
    }

    headers = {
        'Content-Type': 'application/json'
    }

    print(" 测试 /process-video 端点...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")

    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=10)

        print(f"\n 响应状态码: {response.status_code}")
        print(f" 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            response_data = response.json()
            print(f" 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        else:
            print(f" 错误响应 (状态码: {response.status_code}): {response.text}")
            # 尝试解析错误响应的JSON
            try:
                error_data = response.json()
                print(f" 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                pass

    except requests.exceptions.RequestException as e:
        print(f" 请求失败: {e}")

def test_health():
    """测试 /health 端点"""
    url = "http://localhost:9020/health"

    print("\n 测试 /health 端点...")

    try:
        response = requests.get(url, timeout=5)
        print(f" 健康检查状态码: {response.status_code}")

        if response.status_code == 200:
            health_data = response.json()
            print(f" 健康状态: {json.dumps(health_data, indent=2, ensure_ascii=False)}")
        else:
            print(f" 健康检查失败: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f" 健康检查请求失败: {e}")

if __name__ == "__main__":
    print(" 开始端点测试...")
    test_health()
    test_process_video()
    print(" 测试完成!")