#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试气体泄漏模式修复

功能：验证 enable_gas_leakage_mode 方法修复后不会错误地将 gas_leakage_mode 设置为 False
作者：Trae 编程助手
日期：2024
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.processors.video_processor import VideoProcessor
from services.video_service import VideoService
from core.managers.config_manager import ConfigManager

def test_gas_leakage_mode_fix():
    """
    测试气体泄漏模式修复
    
    Returns:
        bool: 测试是否通过
    """
    print("开始测试气体泄漏模式修复...")
    
    try:
        # 初始化配置管理器
        config = ConfigManager()
        
        # 初始化 VideoService
        video_service = VideoService(config=config, label_service=None)
        
        print(f"初始 gas_leakage_mode: {video_service.video_processor.gas_leakage_mode}")
        
        # 测试启用气体泄漏模式
        print("\n测试启用气体泄漏模式...")
        success = video_service.enable_gas_leakage_mode(True)
        print(f"启用操作结果: {success}")
        print(f"启用后 gas_leakage_mode: {video_service.video_processor.gas_leakage_mode}")
        
        if not success:
            print("❌ 启用气体泄漏模式失败")
            return False
            
        if not video_service.video_processor.gas_leakage_mode:
            print("❌ gas_leakage_mode 应该为 True 但实际为 False")
            return False
            
        # 测试禁用气体泄漏模式
        print("\n测试禁用气体泄漏模式...")
        success = video_service.enable_gas_leakage_mode(False)
        print(f"禁用操作结果: {success}")
        print(f"禁用后 gas_leakage_mode: {video_service.video_processor.gas_leakage_mode}")
        
        if not success:
            print("❌ 禁用气体泄漏模式失败")
            return False
            
        if video_service.video_processor.gas_leakage_mode:
            print("❌ gas_leakage_mode 应该为 False 但实际为 True")
            return False
            
        # 再次测试启用
        print("\n再次测试启用气体泄漏模式...")
        success = video_service.enable_gas_leakage_mode(True)
        print(f"再次启用操作结果: {success}")
        print(f"再次启用后 gas_leakage_mode: {video_service.video_processor.gas_leakage_mode}")
        
        if not success or not video_service.video_processor.gas_leakage_mode:
            print("❌ 再次启用气体泄漏模式失败")
            return False
            
        print("✅ 气体泄漏模式修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_video_processor_initialization():
    """
    测试 VideoProcessor 初始化时的 gas_leakage_mode 设置
    
    Returns:
        bool: 测试是否通过
    """
    print("\n开始测试 VideoProcessor 初始化...")
    
    try:
        # 测试默认初始化（应该启用气体泄漏模式）
        processor1 = VideoProcessor()
        print(f"默认初始化 gas_leakage_mode: {processor1.gas_leakage_mode}")
        
        if not processor1.gas_leakage_mode:
            print("❌ 默认初始化应该启用气体泄漏模式")
            return False
            
        # 测试显式启用
        processor2 = VideoProcessor(gas_leakage_mode=True)
        print(f"显式启用 gas_leakage_mode: {processor2.gas_leakage_mode}")
        
        if not processor2.gas_leakage_mode:
            print("❌ 显式启用失败")
            return False
            
        # 测试显式禁用
        processor3 = VideoProcessor(gas_leakage_mode=False)
        print(f"显式禁用 gas_leakage_mode: {processor3.gas_leakage_mode}")
        
        if processor3.gas_leakage_mode:
            print("❌ 显式禁用失败")
            return False
            
        print("✅ VideoProcessor 初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ VideoProcessor 初始化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("气体泄漏模式修复测试")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_video_processor_initialization()
    test2_passed = test_gas_leakage_mode_fix()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"VideoProcessor 初始化测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"气体泄漏模式修复测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！气体泄漏模式修复成功。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
    
    print("=" * 60)