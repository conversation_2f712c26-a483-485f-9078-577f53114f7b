#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 配置UUID日志
try:
    from utils.logging_utils import configure_root_logger_with_uuid, UUIDLogger
    uuid_filter = configure_root_logger_with_uuid()
    print("UUID日志系统配置成功")
except ImportError as e:
    print(f"UUID日志工具导入失败: {e}")
    uuid_filter = None

# 创建一个完整的配置对象，模拟ConfigManager
class MockConfigManager:
    """模拟ConfigManager的完整配置对象"""
    def __init__(self):
        # 基础配置
        self.gas_leakage_mode = True
        self.enable_video_output = True
        self.model_path = "../models/best.pt"
        self.output_dir = "../output"
        self.max_workers = 4
        
        # 存储配置
        self.storage_type = 'nas'
        self.nas_root_path = '../output'
        self.nas_mount_point = None
        self.nas_backup_enabled = False
        
        # MinIO配置（可选）
        self.minio_url = 'localhost:9010'
        self.access_key = 'minioadmin'
        self.secret_key = 'minioadmin'
        self.secure = False
        self.bucket_name = 'linked-all'
        
        # RabbitMQ配置 - 模拟但不实际连接
        self.rabbitmq = {
            'host': 'localhost',
            'port': '5672',
            'username': 'deploy',
            'password': 'deploy@1234',
            'virtual_host': 'transfer',
            'heartbeat': 60,
            'blocked_connection_timeout': 30,
            'socket_timeout': 10,
            'stack_timeout': 15,
            'connection_attempts': 3,
            'retry_delay': 2.0,
            'max_connections': 8,
            'min_connections': 2,
            'health_check_interval': 30
        }
        
        # 数据库配置（可选）
        self.database = None
        self.redis = None
        
        # 视频编码器配置
        self.video_codec = 'auto'
        self.video_quality = 'balanced'
    
    @property
    def rabbitmq_config(self):
        return self.rabbitmq
    
    @property
    def database_config(self):
        return self.database
    
    @property
    def redis_config(self):
        return self.redis

# 创建一个模拟的EventManager，避免实际连接RabbitMQ
class MockEventManager:
    """模拟EventManager，避免RabbitMQ连接"""
    def __init__(self, config):
        self.config = config
        self.publisher_connection = None
        self.consumer_connection = None
        print("MockEventManager初始化成功（跳过RabbitMQ连接）")
    
    def subscribe(self, event_type, callback):
        print(f"订阅事件: {event_type}")
    
    def publish(self, event_type, data):
        print(f"发布事件: {event_type}, 数据: {data}")

# 创建一个模拟的OCRProcessor
class MockOCRProcessor:
    """模拟OCRProcessor，避免实际OCR处理"""
    def __init__(self):
        print("MockOCRProcessor初始化成功")
    
    def process_frame(self, frame):
        return {"text": "mock_text", "confidence": 0.9}

# 测试video_service的日志输出
try:
    print("\n=== 开始测试VideoService日志输出 ===")
    
    # 创建配置对象
    config = MockConfigManager()
    print("配置对象创建成功")
    
    # 临时替换EventManager以避免RabbitMQ连接
    import core.managers.event_manager
    original_event_manager = core.managers.event_manager.EventManager
    core.managers.event_manager.EventManager = MockEventManager
    
    # 导入并创建VideoService
    from services.video_service import VideoService
    from utils.logging_utils import UUIDLogger
    
    print("正在创建VideoService实例...")
    
    # 创建VideoService实例（传入None作为label_service，MockOCRProcessor作为ocr_processor）
    try:
        mock_ocr = MockOCRProcessor()
        video_service = VideoService(config, None, mock_ocr)
        print("VideoService创建成功！")
        
        # 测试一些简单的方法，观察日志输出
        print("\n=== 测试VideoService方法 ===")
        
        # 测试enable_gas_leakage_mode方法
        print("\n1. 测试enable_gas_leakage_mode方法:")
        result = video_service.enable_gas_leakage_mode(True)
        print(f"enable_gas_leakage_mode返回: {result}")
        
        # 测试check_break_flag方法
        print("\n2. 测试check_break_flag方法:")
        result = video_service.check_break_flag("test_task_123")
        print(f"check_break_flag返回: {result}")
        
        # 测试update_break_flags方法
        print("\n3. 测试update_break_flags方法:")
        test_message = {'task_id': 'test_123', 'action': 'break'}
        video_service.update_break_flags(test_message)
        print("update_break_flags调用完成")
        
        print("\n=== VideoService日志测试完成 ===")
        print("如果你看到了上面的日志输出，说明VideoService的日志系统工作正常")
        
    except Exception as e:
        print(f"VideoService创建失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 恢复原始的EventManager
    core.managers.event_manager.EventManager = original_event_manager
        
except ImportError as e:
    print(f"导入VideoService失败: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成")