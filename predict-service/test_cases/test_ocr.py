#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append('..')

def test_ocr():
    try:
        print("Testing PaddleOCR 3.0...")

        from paddleocr import PaddleOCR
        import numpy as np
        import cv2

        # 初始化OCR
        print("Initializing OCR...")
        ocr = PaddleOCR(
            lang='en',
            device='cpu',
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )

        # 创建测试图像
        print("Creating test image...")
        img = np.ones((100, 400, 3), dtype=np.uint8) * 255
        cv2.putText(img, 'TEST 123', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)

        # 进行预测
        print("Running prediction...")
        result = ocr.predict(img)

        print(f"Result type: {type(result)}")
        if result:
            print(f"Result length: {len(result)}")
            ocr_result = result[0]
            print(f"First result type: {type(ocr_result)}")

            # 探索OCRResult的内容
            print(f"OCRResult keys: {list(ocr_result.keys()) if hasattr(ocr_result, 'keys') else 'No keys method'}")

            # 尝试访问不同的属性
            if hasattr(ocr_result, 'get'):
                print("OCRResult content:")
                for key in ['rec_texts', 'rec_scores', 'dt_polys', 'input_path']:
                    value = ocr_result.get(key, 'Not found')
                    print(f" {key}: {value}")

            # 打印OCRResult的字符串表示
            print(f"OCRResult str: {str(ocr_result)[:500]}...")

            # 使用print方法
            print("Using print method:")
            ocr_result.print()

        print("OCR test completed successfully!")
        return True

    except Exception as e:
        print(f"OCR test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ocr()