#!/usr/bin/env python3
"""
测试OpenCV编码器支持
专门用于验证H.264, MP4V等编码器是否可用
"""

import cv2
import numpy as np
import tempfile
import os
import sys

def test_opencv_codecs():
    """测试各种OpenCV编码器"""
    print(" 测试OpenCV编码器支持...")
    print(f"OpenCV版本: {cv2.__version__}")

    # 创建测试帧
    frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

    # 要测试的编码器列表（按优先级排序）
    codecs_to_test = [
        ('mp4v', '.mp4', 'MPEG-4 Part 2'),
        ('H264', '.mp4', 'H.264'),
        ('avc1', '.mp4', 'H.264 AVC1'),
        ('X264', '.mp4', 'x264'),
        ('MJPG', '.avi', 'Motion JPEG'),
        ('XVID', '.avi', 'Xvid'),
        ('FMP4', '.mp4', 'FFmpeg MP4'),
    ]

    working_codecs = []
    failed_codecs = []

    for codec_name, extension, description in codecs_to_test:
        print(f"\n 测试编码器: {codec_name} ({description})")

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=extension, delete=False) as temp_file:
                temp_path = temp_file.name

            # 创建FourCC
            if codec_name == 'mp4v':
                fourcc = cv2.VideoWriter_fourcc('m', 'p', '4', 'v')
            elif codec_name == 'avc1':
                fourcc = cv2.VideoWriter_fourcc('a', 'v', 'c', '1')
            elif codec_name == 'FMP4':
                fourcc = cv2.VideoWriter_fourcc('F', 'M', 'P', '4')
            else:
                fourcc = cv2.VideoWriter_fourcc(*codec_name)

            # 尝试创建VideoWriter
            writer = cv2.VideoWriter(temp_path, fourcc, 30.0, (640, 480))

            if writer.isOpened():
                # 写入测试帧
                success = writer.write(frame)
                writer.release()

                # 检查文件是否有效
                if success and os.path.exists(temp_path) and os.path.getsize(temp_path) > 100:
                    working_codecs.append((codec_name, description))
                    print(f" {codec_name} - 工作正常")
                else:
                    failed_codecs.append(f"{codec_name} - 写入失败")
                    print(f" {codec_name} - 写入失败")
            else:
                failed_codecs.append(f"{codec_name} - 无法打开")
                print(f" {codec_name} - 无法打开VideoWriter")

            # 清理临时文件
            try:
                os.unlink(temp_path)
            except:
                pass

        except Exception as e:
            failed_codecs.append(f"{codec_name} - 异常: {str(e)}")
            print(f" {codec_name} - 异常: {e}")

    # 显示测试结果
    print(f"\n 测试结果总结:")
    print(f" 工作正常的编码器 ({len(working_codecs)}):")
    for codec, desc in working_codecs:
        print(f" • {codec} - {desc}")

    if failed_codecs:
        print(f"\n 失败的编码器 ({len(failed_codecs)}):")
        for codec in failed_codecs:
            print(f" • {codec}")

    # 推荐使用的编码器
    if working_codecs:
        recommended = working_codecs[0][0]
        print(f"\n 推荐使用编码器: {recommended}")

        # 生成配置代码
        print(f"\n 使用示例:")
        print(f"fourcc = cv2.VideoWriter_fourcc(*'{recommended}')")
        print(f"writer = cv2.VideoWriter('output.mp4', fourcc, 30.0, (width, height))")

        return True
    else:
        print(f"\n 警告: 没有找到可用的编码器!")
        return False

def test_2x2_merge():
    """测试2x2视频合成"""
    print(f"\n 测试2x2视频合成...")

    try:
        # 创建4个不同颜色的测试帧
        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
        frames = [np.full((240, 320, 3), color, dtype=np.uint8) for color in colors]

        # 合成2x2布局
        top_row = np.hstack([frames[0], frames[1]])
        bottom_row = np.hstack([frames[2], frames[3]])
        merged_frame = np.vstack([top_row, bottom_row])

        # 使用最兼容的编码器测试
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_path = temp_file.name

        fourcc = cv2.VideoWriter_fourcc('m', 'p', '4', 'v')
        writer = cv2.VideoWriter(temp_path, fourcc, 30.0, (640, 480))

        if writer.isOpened():
            # 写入30帧测试
            for i in range(30):
                writer.write(merged_frame)
            writer.release()

            if os.path.exists(temp_path) and os.path.getsize(temp_path) > 1000:
                print(" 2x2视频合成功能正常")
                os.unlink(temp_path)
                return True
            else:
                print(" 2x2视频合成失败 - 文件无效")
                return False
        else:
            print(" 2x2视频合成失败 - 无法创建VideoWriter")
            return False

    except Exception as e:
        print(f" 2x2视频合成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print(" OpenCV编码器完整测试")
    print("=" * 60)

    # 测试编码器
    codec_result = test_opencv_codecs()

    # 测试2x2合成
    merge_result = test_2x2_merge()

    print(f"\n 最终结果:")
    print(f"编码器测试: {' 通过' if codec_result else ' 失败'}")
    print(f"2x2合成测试: {' 通过' if merge_result else ' 失败'}")

    if codec_result and merge_result:
        print(f"\n OpenCV编码器功能完全正常!")
        return 0
    elif codec_result:
        print(f"\n 基本编码器可用，但合成功能可能有问题")
        return 0
    else:
        print(f"\n OpenCV编码器存在问题，需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())