#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试队列功能的脚本
不依赖实际的视频文件，只测试队列声明和消息发送
"""

import sys
import os
import logging
import time
import uuid

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_queue_functionality():
    """测试队列功能"""
    try:
        # 导入必要的模块
        from core.managers.config_manager import ConfigManager
        from core.managers.event_manager import EventManager
        
        logger.info("=== 开始测试队列功能 ===")
        
        # 1. 初始化配置管理器
        logger.info("1. 初始化配置管理器...")
        config = ConfigManager()
        logger.info("✅ 配置管理器初始化成功")
        
        # 2. 初始化事件管理器
        logger.info("2. 初始化事件管理器...")
        event_manager = EventManager(config)
        logger.info("✅ 事件管理器初始化成功")
        
        # 3. 测试队列声明
        logger.info("3. 测试队列声明...")
        test_queue_name = "queue_420"
        
        # 直接调用队列声明方法
        try:
            event_manager._smart_queue_declare(test_queue_name)
            logger.info(f"✅ 队列 {test_queue_name} 声明成功，没有参数冲突")
        except Exception as e:
            if "PRECONDITION_FAILED" in str(e):
                logger.error(f"❌ 队列参数冲突仍然存在: {e}")
                return False
            else:
                logger.warning(f"⚠️ 其他队列错误: {e}")
        
        # 4. 测试消息发送
        logger.info("4. 测试消息发送...")
        test_message = {
            'task_id': 'test_420',
            'frame_number': 100,
            'progress': 50.0,
            'timestamp': time.time()
        }
        
        try:
            event_manager.send_message(test_queue_name, test_message)
            logger.info("✅ 消息发送成功，没有队列冲突")
        except Exception as e:
            if "PRECONDITION_FAILED" in str(e):
                logger.error(f"❌ 消息发送时队列参数冲突: {e}")
                return False
            else:
                logger.warning(f"⚠️ 其他消息发送错误: {e}")
        
        # 5. 测试不同的队列名称
        logger.info("5. 测试不同类型的队列...")
        test_queues = [
            "queue_421",  # 任务队列
            "system_test",  # 系统队列
            "fail_queue"  # 失败队列
        ]
        
        for queue_name in test_queues:
            try:
                event_manager._smart_queue_declare(queue_name)
                logger.info(f"✅ 队列 {queue_name} 声明成功")
            except Exception as e:
                if "PRECONDITION_FAILED" in str(e):
                    logger.error(f"❌ 队列 {queue_name} 参数冲突: {e}")
                else:
                    logger.warning(f"⚠️ 队列 {queue_name} 其他错误: {e}")
        
        logger.info("\n=== 队列功能测试完成 ===")
        logger.info("✅ 队列冲突问题已修复")
        logger.info("💡 EventManager 的 _smart_queue_declare 方法现在可以正确处理队列参数冲突")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        logger.exception("详细错误信息:")
        return False

def test_rabbitmq_connection():
    """测试RabbitMQ连接"""
    try:
        import pika
        
        logger.info("测试RabbitMQ连接...")
        
        # 连接参数 - 使用配置文件中的默认值
        connection_params = pika.ConnectionParameters(
            host='localhost',
            port=5672,
            virtual_host='transfer',
            credentials=pika.PlainCredentials('deploy', 'deploy@1234')
        )
        
        # 建立连接
        connection = pika.BlockingConnection(connection_params)
        channel = connection.channel()
        
        logger.info("✅ RabbitMQ连接成功")
        
        # 关闭连接
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ RabbitMQ连接失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=== 开始队列冲突修复验证 ===")
    
    # 1. 测试RabbitMQ连接
    logger.info("\n1. 测试RabbitMQ连接...")
    if not test_rabbitmq_connection():
        logger.error("RabbitMQ连接失败，无法继续测试")
        return
    
    # 2. 测试队列功能
    logger.info("\n2. 测试队列功能...")
    success = test_queue_functionality()
    
    # 3. 总结
    logger.info("\n=== 测试结果总结 ===")
    if success:
        logger.info("🎉 队列冲突问题修复验证成功！")
        logger.info("✅ EventManager._smart_queue_declare 方法工作正常")
        logger.info("✅ 队列参数冲突已解决")
        logger.info("✅ 消息发送功能正常")
    else:
        logger.warning("⚠️ 队列功能测试未完全成功")
        logger.info("💡 请检查 EventManager 的实现和 RabbitMQ 配置")
    
    logger.info("\n=== 验证完成 ===")

if __name__ == '__main__':
    main()