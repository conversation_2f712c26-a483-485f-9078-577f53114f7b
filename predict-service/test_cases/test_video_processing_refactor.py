#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理重构验证测试

测试重构后的_process_video_frames方法和相关组件
"""

import unittest
from unittest.mock import Mock
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.context.video_processing_context import VideoProcessingContext


class TestVideoProcessingRefactor(unittest.TestCase):
    """测试视频处理重构功能"""

    def setUp(self):
        """测试初始化"""
        self.mock_cap = Mock()
        self.mock_cap.get.side_effect = self._cap_get_side_effect

    def _cap_get_side_effect(self, prop):
        """模拟cv2.VideoCapture.get()方法"""
        import cv2
        if prop == cv2.CAP_PROP_FRAME_COUNT:
            return 1000 # 总帧数
        elif prop == cv2.CAP_PROP_FPS:
            return 30.0 # FPS
        elif prop == cv2.CAP_PROP_FRAME_WIDTH:
            return 1920 # 宽度
        elif prop == cv2.CAP_PROP_FRAME_HEIGHT:
            return 1080 # 高度
        return 0

    def test_video_processing_context_creation(self):
        """测试VideoProcessingContext创建"""
        context = VideoProcessingContext(
            task_id="test_task",
            video_id="test_video",
            user_id="test_user",
            confidence_threshold=0.5,
            gas_leakage_mode=True,
            total_frames=1000,
            fps=30.0,
            duration_seconds=33.33,
            frame_width=1920,
            frame_height=1080,
            temp_frames_dir="/tmp",
            output_dir="../output"
        )

        # 验证基本属性
        self.assertEqual(context.task_id, "test_task")
        self.assertEqual(context.video_id, "test_video")
        self.assertEqual(context.user_id, "test_user")
        self.assertEqual(context.confidence_threshold, 0.5)
        self.assertTrue(context.gas_leakage_mode)

        # 验证视频元数据
        self.assertEqual(context.total_frames, 1000)
        self.assertEqual(context.fps, 30.0)
        self.assertEqual(context.frame_width, 1920)
        self.assertEqual(context.frame_height, 1080)

        # 验证初始状态
        self.assertEqual(context.frame_counter, 0)
        self.assertEqual(context.recog_date, "")
        self.assertEqual(context.recog_lat, "")
        self.assertEqual(context.recog_lon, "")

        # 验证配置选项
        self.assertTrue(context.save_no_detection_frames)
        self.assertTrue(context.enable_progress_logging)
        self.assertEqual(context.mq_update_interval, 50)

    def test_video_processing_context_methods(self):
        """测试VideoProcessingContext计算方法"""
        context = VideoProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5, total_frames=1000, fps=30.0
        )

        # 测试进度计算
        context.frame_counter = 500
        self.assertEqual(context.get_progress_percentage(), 50.0)

        # 测试帧计数器递增
        context.increment_frame()
        self.assertEqual(context.frame_counter, 501)

        # 测试第一帧和最后一帧判断
        context.frame_counter = 0
        self.assertTrue(context.is_first_frame())
        self.assertFalse(context.is_last_frame())

        context.frame_counter = 999
        self.assertFalse(context.is_first_frame())
        self.assertTrue(context.is_last_frame())

        # 测试进度报告判断
        context.progress_interval = 20
        context.frame_counter = 20
        self.assertTrue(context.should_report_progress())

        context.frame_counter = 21
        self.assertFalse(context.should_report_progress())

        # 测试MQ更新判断
        context.mq_update_interval = 50
        context.frame_counter = 50
        self.assertTrue(context.should_update_mq_progress())

        context.frame_counter = 51
        self.assertFalse(context.should_update_mq_progress())

    def test_ocr_data_update(self):
        """测试OCR数据更新"""
        context = VideoProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5
        )

        # 测试空OCR数据
        context.update_ocr_data(None)
        self.assertEqual(context.recog_date, "")
        self.assertEqual(context.recog_lat, "")
        self.assertEqual(context.recog_lon, "")
        self.assertIsNone(context.first_ocr_data)

        # 测试有效OCR数据
        ocr_data = {
            "UTC": "2023-01-01 12:00:00",
            "Lat": "39.9042",
            "Lon": "116.4074"
        }
        context.update_ocr_data(ocr_data)
        self.assertEqual(context.recog_date, "2023-01-01 12:00:00")
        self.assertEqual(context.recog_lat, "39.9042")
        self.assertEqual(context.recog_lon, "116.4074")
        self.assertEqual(context.first_ocr_data, ocr_data)
        self.assertTrue(context.first_ocr_attempt_success)

    def test_video_info_dict(self):
        """测试视频信息字典生成"""
        context = VideoProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5, total_frames=1000, fps=30.0,
            duration_seconds=33.33, frame_width=1920, frame_height=1080
        )

        video_info = context.get_video_info_dict()
        expected = {
            'width': 1920,
            'height': 1080,
            'fps': 30.0,
            'total_frames': 1000,
            'duration_seconds': 33.33
        }
        self.assertEqual(video_info, expected)


def run_refactor_tests():
    """运行重构验证测试"""
    print(" 开始运行视频处理重构验证测试...")
    print("=" * 60)

    # 创建测试套件
    test_loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # 添加测试类
    test_suite.addTests(test_loader.loadTestsFromTestCase(TestVideoProcessingRefactor))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出结果总结
    print("\n" + "=" * 60)
    print(f" 测试总结:")
    print(f" ├─ 总测试数: {result.testsRun}")
    print(f" ├─ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f" ├─ 失败: {len(result.failures)}")
    print(f" └─ 错误: {len(result.errors)}")

    if result.wasSuccessful():
        print(" 所有测试通过！重构验证成功。")
        return True
    else:
        print(" 部分测试失败，请检查重构实现。")
        return False


if __name__ == "__main__":
    success = run_refactor_tests()
    sys.exit(0 if success else 1)