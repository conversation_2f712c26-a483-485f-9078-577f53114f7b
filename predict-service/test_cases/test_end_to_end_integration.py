#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端集成测试
测试完整的数据流：视频处理 → 数据分析 → MQ发送 → 数据验证
"""

import os
import sys
import json
import time
import tempfile
from typing import Dict, Any, List
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from core.models.enhanced_message_structure import (
    EnhancedMessageBuilder, EnhancedMQMessage, VideoLeakageStatistics, 
    LeakagePointSummary, GPSPersistenceData, RiskLevel, ConfidenceInterval, PDFReportData
)
from core.reports.pdf_data_builder import PDFDataBuilder, PdfReportData
from core.context.video_processing_context import VideoProcessingContext
from core.processors.gas_tracking_finalizer import GasTrackingFinalizer
from services.video_service import VideoService

class EndToEndIntegrationTest:
    """端到端集成测试类"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = tempfile.mkdtemp()
        
    def create_mock_video_context(self) -> VideoProcessingContext:
        """创建模拟视频上下文"""
        context = Mock(spec=VideoProcessingContext)
        
        # 设置基本属性
        context.task_id = "test_task_001"
        context.video_id = "test_video_001"
        context.user_id = "test_user_001"
        context.video_path = os.path.join(self.temp_dir, "test_video.mp4")
        context.frame_counter = 3000
        context.fps = 30
        context.frame_width = 1920
        context.frame_height = 1080
        context.total_frames = 3000
        context.file_size_mb = 150.5
        context.gas_leakage_mode = True
        
        # 模拟处理状态方法
        def mock_get_processing_summary():
            return {
                "total_frames_processed": 3000,
                "processing_time_seconds": 120.5,
                "average_fps": 24.9
            }
        
        def mock_get_video_output_path():
            return os.path.join(self.temp_dir, "output_video.mp4")
        
        context.get_processing_summary = mock_get_processing_summary
        context.get_video_output_path = mock_get_video_output_path
        
        return context
    
    def create_mock_gas_analysis_summary(self) -> Dict[str, Any]:
        """创建模拟气体分析汇总数据"""
        return {
            "unique_leakage_sources": 3,
            "confirmed_stable_leaks": 2,
            "max_simultaneous_leakages": 2,
            "leakage_frequency": 0.15,
            "final_tracked_leakage_points": [
                {
                    "leak_id": "leak_001",
                    "start_frame": 150,
                    "end_frame": 450,
                    "confidence_score": 0.85,
                    "center_x": 640,
                    "center_y": 480
                },
                {
                    "leak_id": "leak_002",
                    "start_frame": 800,
                    "end_frame": 1200,
                    "confidence_score": 0.92,
                    "center_x": 1280,
                    "center_y": 720
                }
            ],
            "summary_statistics": {
                "total_detection_time": 120.5,
                "average_confidence": 0.885,
                "detection_accuracy": 0.94
            }
        }
    
    def create_mock_temporal_gps_summary(self) -> Dict[str, Any]:
        """创建模拟时间GPS汇总数据"""
        return {
            "gps_analysis": {
                "start_position": {"lat": 39.9042, "lng": 116.4074, "alt": 50.0},
                "end_position": {"lat": 39.9142, "lng": 116.4174, "alt": 55.0},
                "center": {"lat": 39.9092, "lng": 116.4124, "alt": 52.5},
                "bounds": {
                    "north": 39.9142, "south": 39.9042,
                    "east": 116.4174, "west": 116.4074
                },
                "total_distance_meters": 1580.5,
                "average_speed_mps": 13.2
            },
            "data_quality": {
                "gps_success_rate": 0.95,
                "signal_strength": "strong"
            },
            "watermark_analysis": {
                "quality_assessment": "high_quality",
                "gps_accuracy": "meter_level"
            }
        }
    
    def create_mock_final_gas_tracking_summary(self):
        """创建模拟最终气体追踪汇总"""
        mock_summary = Mock()
        mock_summary.final_leakage_points = [
            Mock(
                track_id=1,
                start_time=5.0,
                end_time=15.0,
                confidence_score=0.85,
                center_x=640,
                center_y=480,
                dispersion_behavior="expanding",
                risk_level="medium"
            ),
            Mock(
                track_id=2,
                start_time=26.7,
                end_time=40.0,
                confidence_score=0.92,
                center_x=1280,
                center_y=720,
                dispersion_behavior="stable",
                risk_level="high"
            )
        ]
        return mock_summary
    
    def test_enhanced_message_building(self) -> bool:
        """测试增强消息构建"""
        try:
            print("\n=== 测试增强消息构建 ===")
            
            # 准备测试数据
            context = self.create_mock_video_context()
            gas_analysis_summary = self.create_mock_gas_analysis_summary()
            temporal_gps_summary = self.create_mock_temporal_gps_summary()
            final_gas_tracking_summary = self.create_mock_final_gas_tracking_summary()
            
            # 创建消息构建器
            builder = EnhancedMessageBuilder()
            
            # 构建增强消息
            enhanced_message = builder.build_enhanced_success_message(
                context=context,
                gas_analysis_summary=gas_analysis_summary,
                temporal_gps_summary=temporal_gps_summary,
                final_gas_tracking_summary=final_gas_tracking_summary
            )
            
            # 验证消息结构
            assert isinstance(enhanced_message, EnhancedMQMessage), "消息类型错误"
            assert enhanced_message.taskId == "test_task_001", "任务ID不匹配"
            assert enhanced_message.videoId == "test_video_001", "视频ID不匹配"
            assert enhanced_message.messageType == "notifySuccess", "消息类型不匹配"
            assert enhanced_message.gasLeakageMode == True, "气体泄漏模式不匹配"
            
            # 验证泄漏统计
            assert enhanced_message.leakage_statistics is not None, "泄漏统计数据缺失"
            assert enhanced_message.leakage_statistics.total_leakage_points == 3, "泄漏点总数不匹配"
            
            # 验证GPS数据
            assert enhanced_message.gps_persistence_data is not None, "GPS持久化数据缺失"
            assert enhanced_message.gps_persistence_data.has_gps_data == True, "GPS数据标志错误"
            
            # 验证PDF报告数据
            assert enhanced_message.pdf_report_data is not None, "PDF报告数据缺失"
            assert isinstance(enhanced_message.pdf_report_data, PdfReportData), "PDF报告数据类型错误"
            
            # 验证风险等级
            valid_risk_levels = ["none", "low", "medium", "high", "critical"]
            print(f"实际风险等级: {enhanced_message.overall_risk_level}, 类型: {type(enhanced_message.overall_risk_level)}")
            if hasattr(enhanced_message.overall_risk_level, 'value'):
                risk_level_value = enhanced_message.overall_risk_level.value
            else:
                risk_level_value = enhanced_message.overall_risk_level
            assert risk_level_value in valid_risk_levels, f"风险等级无效: {risk_level_value}"
            
            print("✅ 增强消息构建测试通过")
            print(f"   - 任务ID: {enhanced_message.taskId}")
            print(f"   - 泄漏点数量: {enhanced_message.leakage_statistics.total_leakage_points}")
            print(f"   - 风险等级: {enhanced_message.overall_risk_level}")
            print(f"   - GPS数据: {'有' if enhanced_message.gps_persistence_data.has_gps_data else '无'}")
            
            return True
            
        except Exception as e:
            import traceback
            print(f"❌ 增强消息构建测试失败: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def test_pdf_data_builder(self) -> bool:
        """测试PDF数据构建器"""
        try:
            print("\n=== 测试PDF数据构建器 ===")
            
            # 创建PDF数据构建器
            pdf_builder = PDFDataBuilder()
            
            # 准备测试数据
            video_info = {
                "task_id": "test_task_001",
                "video_id": "test_video_001",
                "file_name": "test_video.mp4",
                "duration_seconds": 100.0,
                "fps": 30,
                "width": 1920,
                "height": 1080
            }
            
            leakage_statistics = VideoLeakageStatistics(
                total_leakage_points=3,
                confirmed_leakage_points=2,
                suspected_leakage_points=1,
                max_simultaneous_leaks=2,
                leakage_density=0.15,
                average_leak_duration=12.5,
                total_leakage_time=25.0,
                leakage_frequency=0.15,
                coverage_percentage=0.08
            )
            
            # 创建简化的LeakagePointSummary对象
            from core.models.enhanced_message_structure import ConfidenceInterval, LeakageTimeSegment, GasDispersionAnalysis
            
            confidence_interval = ConfidenceInterval(
                min_confidence=0.8,
                max_confidence=0.9,
                average_confidence=0.85,
                median_confidence=0.82,
                confidence_distribution={"0.8-0.9": 10},
                stable_confidence_ratio=0.75
            )
            
            time_segment = LeakageTimeSegment(
                start_time="2024-01-01T10:05:00Z",
                end_time="2024-01-01T10:15:00Z",
                start_frame=150,
                end_frame=450,
                duration_seconds=10.0,
                duration_frames=300,
                peak_confidence_time="2024-01-01T10:10:00Z",
                peak_confidence_frame=300,
                average_confidence=0.85,
                is_continuous=True,
                interruption_count=0
            )
            
            from core.models.enhanced_message_structure import GasDispersionBehavior
            
            dispersion_analysis = GasDispersionAnalysis(
                behavior_type=GasDispersionBehavior.EXPANDING,
                expansion_rate=1.2,
                movement_velocity=0.5,
                direction_consistency=0.8,
                main_direction={"angle": 45.0, "magnitude": 0.8},
                area_change_pattern="increasing",
                dispersion_stability=0.9,
                wind_influence_factor=0.3
            )
            
            leakage_points = [
                LeakagePointSummary(
                    track_id=1,
                    class_name="gas_leak",
                    confidence_interval=confidence_interval,
                    time_segments=[time_segment],
                    dispersion_analysis=dispersion_analysis,
                    risk_assessment={"level": "medium", "score": 0.6},
                    spatial_characteristics={"center_x": 640, "center_y": 480},
                    stability_metrics={"stability_score": 0.85},
                    total_duration_seconds=10.0,
                    total_frames=300,
                    is_confirmed_leak=True,
                    severity_score=0.6
                )
            ]
            
            gps_data = {
                "has_gps_data": True,
                "start_position": {"lat": 39.9042, "lng": 116.4074},
                "end_position": {"lat": 39.9142, "lng": 116.4174}
            }
            
            # 构建PDF报告数据
            pdf_report_data = pdf_builder.build_pdf_report_data(
                video_info=video_info,
                leakage_statistics=leakage_statistics,
                leakage_points=leakage_points,
                gps_data=gps_data,
                overall_risk_level="medium"
            )
            
            # 验证PDF报告数据
            assert isinstance(pdf_report_data, PdfReportData), "PDF报告数据类型错误"
            assert pdf_report_data.video_info is not None, "视频信息缺失"
            assert pdf_report_data.leakage_summary is not None, "泄漏汇总缺失"
            assert pdf_report_data.leakage_details is not None, "泄漏详情缺失"
            assert pdf_report_data.risk_assessment is not None, "风险评估缺失"
            
            print("✅ PDF数据构建器测试通过")
            print(f"   - 视频信息: {pdf_report_data.video_info['file_name']}")
            print(f"   - 泄漏点数量: {len(pdf_report_data.leakage_details)}")
            print(f"   - 风险评估: {pdf_report_data.risk_assessment['overall_risk_level']}")
            
            return True
            
        except Exception as e:
            import traceback
            print(f"❌ PDF数据构建器测试失败: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def test_message_serialization(self) -> bool:
        """测试消息序列化"""
        try:
            print("\n=== 测试消息序列化 ===")
            
            # 构建测试消息
            context = self.create_mock_video_context()
            gas_analysis_summary = self.create_mock_gas_analysis_summary()
            temporal_gps_summary = self.create_mock_temporal_gps_summary()
            final_gas_tracking_summary = self.create_mock_final_gas_tracking_summary()
            
            builder = EnhancedMessageBuilder()
            enhanced_message = builder.build_enhanced_success_message(
                context=context,
                gas_analysis_summary=gas_analysis_summary,
                temporal_gps_summary=temporal_gps_summary,
                final_gas_tracking_summary=final_gas_tracking_summary
            )
            
            # 转换为字典
            message_dict = builder.to_dict(enhanced_message)
            
            # 序列化为JSON
            json_str = json.dumps(message_dict, ensure_ascii=False, indent=2)
            
            # 验证JSON可以反序列化
            parsed_dict = json.loads(json_str)
            
            # 验证关键字段
            assert "taskId" in parsed_dict, "任务ID字段缺失"
            assert "leakage_statistics" in parsed_dict, "泄漏统计字段缺失"
            assert "pdf_report_data" in parsed_dict, "PDF报告数据字段缺失"
            assert "gps_persistence_data" in parsed_dict, "GPS持久化数据字段缺失"
            
            # 验证消息大小（应该合理，不会过大）
            message_size_kb = len(json_str.encode('utf-8')) / 1024
            assert message_size_kb < 500, f"消息过大: {message_size_kb:.2f}KB"
            
            print("✅ 消息序列化测试通过")
            print(f"   - 消息大小: {message_size_kb:.2f}KB")
            print(f"   - 字段数量: {len(parsed_dict)}")
            
            return True
            
        except Exception as e:
            import traceback
            print(f"❌ 消息序列化测试失败: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def test_data_consistency(self) -> bool:
        """测试数据一致性"""
        try:
            print("\n=== 测试数据一致性 ===")
            
            # 构建测试消息
            context = self.create_mock_video_context()
            gas_analysis_summary = self.create_mock_gas_analysis_summary()
            temporal_gps_summary = self.create_mock_temporal_gps_summary()
            final_gas_tracking_summary = self.create_mock_final_gas_tracking_summary()
            
            builder = EnhancedMessageBuilder()
            enhanced_message = builder.build_enhanced_success_message(
                context=context,
                gas_analysis_summary=gas_analysis_summary,
                temporal_gps_summary=temporal_gps_summary,
                final_gas_tracking_summary=final_gas_tracking_summary
            )
            
            # 验证数据一致性
            # 1. 泄漏点数量一致性
            expected_leakage_count = gas_analysis_summary["unique_leakage_sources"]
            actual_leakage_count = enhanced_message.leakage_statistics.total_leakage_points
            assert actual_leakage_count == expected_leakage_count, f"泄漏点数量不一致: 期望{expected_leakage_count}, 实际{actual_leakage_count}"
            
            # 2. GPS数据一致性
            if enhanced_message.gps_persistence_data.has_gps_data:
                assert enhanced_message.gps_persistence_data.start_position is not None, "GPS起始位置缺失"
                assert enhanced_message.gps_persistence_data.end_position is not None, "GPS结束位置缺失"
            
            # 3. PDF报告数据一致性
            pdf_data = enhanced_message.pdf_report_data
            # 注意：PdfReportData中的video_info不包含task_id，leakage_summary是字典格式
            assert pdf_data.leakage_summary["total_leakage_points"] == expected_leakage_count, "PDF报告中泄漏点数量不一致"
            
            # 4. 时间戳合理性
            current_time = time.time()
            assert abs(enhanced_message.timestamp - current_time) < 60, "时间戳不合理"
            
            print("✅ 数据一致性测试通过")
            print(f"   - 泄漏点数量一致: {actual_leakage_count}")
            print(f"   - GPS数据完整: {'是' if enhanced_message.gps_persistence_data.has_gps_data else '否'}")
            print(f"   - 时间戳合理: {abs(enhanced_message.timestamp - current_time):.2f}秒差异")
            
            return True
            
        except Exception as e:
            import traceback
            print(f"❌ 数据一致性测试失败: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        try:
            print("\n=== 测试错误处理 ===")
            
            builder = EnhancedMessageBuilder()
            
            # 测试1: 缺失必要参数
            try:
                builder.build_enhanced_success_message(
                    context=None,
                    gas_analysis_summary={},
                    temporal_gps_summary={},
                    final_gas_tracking_summary=None
                )
                assert False, "应该抛出异常"
            except Exception:
                print("   ✓ 正确处理缺失参数异常")
            
            # 测试2: 无效数据格式
            context = self.create_mock_video_context()
            invalid_gas_summary = "invalid_data"  # 应该是字典
            
            try:
                enhanced_message = builder.build_enhanced_success_message(
                    context=context,
                    gas_analysis_summary=invalid_gas_summary,
                    temporal_gps_summary={},
                    final_gas_tracking_summary=None
                )
                # 应该能够处理并返回降级数据
                assert enhanced_message is not None, "应该返回降级消息"
                print("   ✓ 正确处理无效数据格式")
            except Exception as e:
                print(f"   ✓ 正确抛出异常: {type(e).__name__}")
            
            # 测试3: PDF数据构建失败的降级处理
            with patch('core.reports.pdf_data_builder.PDFDataBuilder') as mock_builder:
                mock_builder.side_effect = Exception("PDF构建失败")
                
                enhanced_message = builder.build_enhanced_success_message(
                    context=context,
                    gas_analysis_summary=self.create_mock_gas_analysis_summary(),
                    temporal_gps_summary=self.create_mock_temporal_gps_summary(),
                    final_gas_tracking_summary=self.create_mock_final_gas_tracking_summary()
                )
                
                # 应该有降级的PDF数据
                assert enhanced_message.pdf_report_data is not None, "应该有降级PDF数据"
                print("   ✓ 正确处理PDF构建失败")
            
            print("✅ 错误处理测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 错误处理测试失败: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("\n" + "="*60)
        print("开始端到端集成测试")
        print("="*60)
        
        test_methods = [
            ("enhanced_message_building", self.test_enhanced_message_building),
            ("pdf_data_builder", self.test_pdf_data_builder),
            ("message_serialization", self.test_message_serialization),
            ("data_consistency", self.test_data_consistency),
            ("error_handling", self.test_error_handling)
        ]
        
        results = {}
        passed_count = 0
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                results[test_name] = result
                if result:
                    passed_count += 1
            except Exception as e:
                print(f"❌ 测试 {test_name} 执行失败: {e}")
                results[test_name] = False
        
        # 输出测试汇总
        print("\n" + "="*60)
        print("测试汇总")
        print("="*60)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:30} {status}")
        
        print(f"\n总计: {passed_count}/{len(test_methods)} 个测试通过")
        
        if passed_count == len(test_methods):
            print("\n🎉 所有集成测试通过！")
            print("\n✅ 阶段三和阶段四的核心功能已实现：")
            print("   - PDF报告数据结构和构建器")
            print("   - 增强MQ消息结构")
            print("   - 端到端数据流测试")
            print("   - 数据一致性验证")
            print("   - 错误处理和降级机制")
        else:
            print(f"\n⚠️  {len(test_methods) - passed_count} 个测试失败，需要修复")
        
        return results

def main():
    """主函数"""
    test_runner = EndToEndIntegrationTest()
    results = test_runner.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)