#!/usr/bin/env python3
"""
验证MQ消息字段的数据可用性
检查每个字段是否能从相应的数据源正确获取
"""
import json


def analyze_field_availability():
    """分析字段数据可用性"""

    print(" 验证MQ消息字段的数据可用性")
    print("=" * 60)

    # 字段分类验证
    field_groups = {
        " 标签信息字段": {
            "labelInfoResult": {
                "data_source": "frame_result.get('labelInfo')",
                "generation": "frame_processor.py, enhanced_gas_leakage_tracker.py",
                "availability": "高 - 每帧都生成",
                "verified": True
            }
        },

        " 气体泄漏分析核心字段": {
            "gasLeakageMode": {
                "data_source": "frame_result.get('gas_mode')",
                "generation": "video_service.py - 根据配置设置",
                "availability": "高 - 配置确定",
                "verified": True
            },
            "gasAnalysisSummary": {
                "data_source": "video_processor.get_gas_leakage_analysis_summary()",
                "generation": "core/processors/video_processor.py:609 - 完整实现",
                "availability": "中 - 需要frame_counter>10且有追踪数据",
                "verified": True,
                "note": "包含status, total_frames_analyzed, unique_leakage_sources等完整数据"
            },
            "finalTrackedLeakagePoints": {
                "data_source": "gas_analysis_summary.get('final_tracked_leakage_points')",
                "generation": "core/processors/video_processor.py:650-720 - 追踪点汇总",
                "availability": "中 - 依赖气体分析汇总",
                "verified": True
            },
            "gasTrackingSummary": {
                "data_source": "gas_analysis_summary.get('summary_statistics')",
                "generation": "core/processors/video_processor.py:776-785 - 统计汇总",
                "availability": "中 - 依赖气体分析汇总",
                "verified": True
            },
            "temporalGpsSummary": {
                "data_source": "video_processor.get_temporal_gps_summary()",
                "generation": "core/processors/video_processor.py:1000-1120 - OCR GPS汇总",
                "availability": "高 - 总是生成",
                "verified": True
            }
        },

        " 实时进度字段": {
            "gasLeakageTrackingData": {
                "data_source": "frame_result.get('gas_tracking_results')",
                "generation": "enhanced_gas_leakage_tracker.py:730-755",
                "availability": "高 - 气体模式下每帧生成",
                "verified": True
            },
            "cameraMotionData": {
                "data_source": "gas_tracking_result.get('camera_motion')",
                "generation": "enhanced_gas_leakage_tracker.py:275-285",
                "availability": "中 - 有镜头运动时生成",
                "verified": True
            },
            "gasDispersionAnalysis": {
                "data_source": "gas_tracking_result.get('gas_dispersion_analysis')",
                "generation": "enhanced_gas_leakage_tracker.py:880-920",
                "availability": "高 - 气体模式下每帧生成",
                "verified": True
            },
            "trackingStatistics": {
                "data_source": "手动构建的统计信息",
                "generation": "video_service.py:1028-1032 - 实时构建",
                "availability": "高 - 总是生成",
                "verified": True
            },
            "byteTrackStatus": {
                "data_source": "gas_tracking_result.get('tracker_status')",
                "generation": " 未找到tracker_status生成代码",
                "availability": "低 - 可能缺失",
                "verified": False,
                "issue": "在enhanced_gas_leakage_tracker中未找到tracker_status字段的生成"
            }
        },

        " 扩展字段": {
            "motionPatternDistribution": {
                "data_source": "gas_analysis_summary.get('motion_pattern_distribution')",
                "generation": "core/processors/video_processor.py:752-755",
                "availability": "中 - 依赖气体分析汇总",
                "verified": True
            },
            "shapeFeatures": {
                "data_source": "frame_result.get('detections') - shape_analysis字段",
                "generation": " 需要验证shape_analysis是否在检测中生成",
                "availability": "未知 - 需要进一步验证",
                "verified": False,
                "issue": "需要检查检测结果中是否包含shape_analysis字段"
            },
            "irregularShapeDetected": {
                "data_source": "基于detections中shape_analysis.is_irregular判断",
                "generation": "依赖shape_analysis字段",
                "availability": "未知 - 依赖shape_analysis",
                "verified": False
            },
            "algorithmInfo": {
                "data_source": "手动构建的算法信息",
                "generation": "video_service.py:1045-1050 - 实时构建",
                "availability": "高 - 总是生成",
                "verified": True
            }
        }
    }

    # 分析结果
    total_fields = 0
    verified_fields = 0
    issues = []

    for group_name, fields in field_groups.items():
        print(f"\n{group_name}")
        print("-" * 40)

        for field_name, info in fields.items():
            total_fields += 1
            if info.get('verified', False):
                verified_fields += 1
                status = ""
            else:
                status = ""
                if 'issue' in info:
                    issues.append(f"{field_name}: {info['issue']}")

            print(f"{status} {field_name}")
            print(f" 数据源: {info['data_source']}")
            print(f" 生成位置: {info['generation']}")
            print(f" 可用性: {info['availability']}")
            if 'note' in info:
                print(f" 备注: {info['note']}")
            if 'issue' in info:
                print(f" 问题: {info['issue']}")
            print()

    # 汇总报告
    print("=" * 60)
    print(" 验证汇总报告")
    print(f"总字段数: {total_fields}")
    print(f"已验证字段: {verified_fields}")
    print(f"验证率: {verified_fields/total_fields*100:.1f}%")

    if issues:
        print("\n 需要修复的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")

        print("\n 建议修复方案:")
        print("1. 在enhanced_gas_leakage_tracker.py中添加tracker_status字段生成")
        print("2. 在检测结果中添加shape_analysis字段（用于不规则形状检测）")
        print("3. 验证所有字段在实际运行时的数据完整性")
    else:
        print("\n 所有字段数据源验证通过！")

    return verified_fields / total_fields >= 0.8


if __name__ == '__main__':
    success = analyze_field_availability()
    exit(0 if success else 1)