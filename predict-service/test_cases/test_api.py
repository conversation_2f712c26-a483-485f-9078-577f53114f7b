#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import os
import time

def test_health():
    """测试健康检查端点"""
    try:
        response = requests.get('http://localhost:9020/health')
        print(f"Health Check - Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_memory():
    """测试内存状态端点"""
    try:
        response = requests.get('http://localhost:9020/memory')
        print(f"Memory Status - Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Memory check failed: {e}")
        return False

def test_process_video():
    """测试视频处理端点"""
    try:
        # 创建一个测试视频文件
        import cv2
        import numpy as np
        import tempfile

        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.avi', delete=False)
        temp_video.close()

        # 生成简单的测试视频
        fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        out = cv2.VideoWriter(temp_video.name, fourcc, 10.0, (320, 240))

        for i in range(30): # 30帧，3秒视频
            frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
            out.write(frame)

        out.release()

        print(f"Created test video: {temp_video.name}")
        print(f"File size: {os.path.getsize(temp_video.name)} bytes")

        # 测试视频处理端点
        files = {'video': open(temp_video.name, 'rb')}

        print("Sending POST request to /process-video...")
        response = requests.post('http://localhost:9020/process-video', files=files)

        files['video'].close()

        print(f"Process Video - Status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("Success! Video processed successfully")
            # 如果返回的是文件，保存它
            if 'application/zip' in response.headers.get('Content-Type', ''):
                with open('test_result.zip', 'wb') as f:
                    f.write(response.content)
                print("Result saved as test_result.zip")
            else:
                print(f"Response content: {response.text}")
        else:
            print(f"Error response: {response.text}")

        # 清理临时文件
        os.unlink(temp_video.name)

        return response.status_code == 200

    except Exception as e:
        print(f"Process video test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_predict():
    """测试预测端点"""
    try:
        # 创建一个测试视频文件
        import cv2
        import numpy as np
        import tempfile

        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.avi', delete=False)
        temp_video.close()

        # 生成简单的测试视频
        fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        out = cv2.VideoWriter(temp_video.name, fourcc, 10.0, (320, 240))

        for i in range(10): # 10帧视频
            frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
            out.write(frame)

        out.release()

        print(f"Created test video for predict: {temp_video.name}")

        # 测试预测端点
        files = {'video': open(temp_video.name, 'rb')}

        print("Sending POST request to /predict...")
        response = requests.post('http://localhost:9020/predict', files=files)

        files['video'].close()

        print(f"Predict - Status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("Predict result:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"Error response: {response.text}")

        # 清理临时文件
        os.unlink(temp_video.name)

        return response.status_code == 200

    except Exception as e:
        print(f"Predict test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print(" 开始API测试...")
    print("=" * 50)

    # 等待服务启动
    print("等待服务启动...")
    time.sleep(5)

    results = {}

    # 测试健康检查
    print("\n1. 测试健康检查")
    results['health'] = test_health()

    # 测试内存状态
    print("\n2. 测试内存状态")
    results['memory'] = test_memory()

    # 测试预测端点
    print("\n3. 测试预测端点")
    results['predict'] = test_predict()

    # 测试视频处理端点
    print("\n4. 测试视频处理端点")
    results['process_video'] = test_process_video()

    # 总结
    print("\n" + "=" * 50)
    print(" 测试结果总结:")
    for test_name, success in results.items():
        status = " 通过" if success else " 失败"
        print(f" {test_name}: {status}")

    all_passed = all(results.values())
    if all_passed:
        print("\n 所有API测试通过!")
    else:
        print("\n 部分API测试失败")

    return results

if __name__ == "__main__":
    results = main()