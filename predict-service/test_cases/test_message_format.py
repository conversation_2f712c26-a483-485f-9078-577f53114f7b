#!/usr/bin/env python3
"""
测试MQ消息格式与Java端VideoPubProcessData的兼容性
"""
import json
import time


def test_video_pub_process_data_format():
    """测试视频处理进度消息格式"""

    # 模拟Python端发送的消息
    mock_frame_result = {
        'gas_mode': True,
        'max_confidence': 0.85,
        'max_velocity': 2.5,
        'detections': [
            {
                'confidence': 0.85,
                'shape_analysis': {
                    'is_irregular': True,
                    'shape_type': 'polygon'
                }
            }
        ],
        'gas_tracking_results': {
            'detections': [{'id': 1, 'confidence': 0.85}],
            'tracked_objects': [{'track_id': 1, 'confidence': 0.9}],
            'camera_motion': {
                'confidence': 0.7,
                'translation': [1.2, 0.8]
            },
            'gas_dispersion_analysis': {
                'active_leakage_count': 2,
                'total_leakage_area': 145.6,
                'risk_level': 'medium',
                'main_direction': {'angle': 45, 'magnitude': 1.5}
            },
            'tracker_status': {
                'active_tracks': 3,
                'lost_tracks': 1
            }
        },
        'labelInfo': {
            'detection_count': 1,
            'class_distribution': {'gas_leak': 1}
        }
    }

    mock_progress = {
        'userId': 12345,
        'videoId': 67890,
        'taskId': 'task_123456',
        'videoName': 'test_video.mp4',
        'videoPath': '/videos/test_video.mp4',
        'totalFrames': 1000,
        'fps': 30,
        'startTime': time.time() - 100
    }

    mock_gas_analysis_summary = {
        'status': 'success',
        'total_frames_analyzed': 500,
        'unique_leakage_sources': 3,
        'max_simultaneous_leakages': 2,
        'leakage_frequency': 0.6,
        'total_leakage_events': 15,
        'motion_pattern_distribution': {
            'stationary': 10,
            'moving': 5
        },
        'final_tracked_leakage_points': [
            {
                'track_id': 1,
                'average_confidence': 0.85,
                'is_stable': True
            }
        ],
        'summary_statistics': {
            'overall_risk_level': 'medium',
            'confirmed_stable_leaks': 2
        },
        'camera_motion_statistics': {
            'stability_assessment': 'stable'
        }
    }

    mock_temporal_gps_summary = {
        'gps_analysis': {
            'center': {'lat': 39.123, 'lon': 116.456}
        },
        'temporal_analysis': {
            'start_time': '2025-07-21T10:00:00Z',
            'end_time': '2025-07-21T10:05:00Z'
        },
        'watermark_analysis': {
            'detection_rate': 0.85,
            'quality_assessment': 'good'
        }
    }

    frame_counter = 500
    elapsed_seconds = 100.0
    confidence_threshold = 0.5
    detection_image_path = '/output/frame_500.jpg'
    recogDate = '2025-07-21T10:02:30Z'
    recogLat = '39.123456'
    recogLon = '116.654321'

    # 构建完整的消息（模拟_update_task_progress中的逻辑）
    queue_message = {
        # ========== 基础消息字段 ==========
        'messageType': 'message',
        'frameIndex': frame_counter,
        'userId': mock_progress['userId'],
        'videoId': mock_progress['videoId'],
        'taskId': mock_progress['taskId'],
        'videoName': mock_progress.get('videoName', ''),

        # ========== 进度相关字段 ==========
        'progress': frame_counter / mock_progress['totalFrames'],
        'seconds': float(elapsed_seconds),
        'videoInfos': json.dumps({
            'total_frames': mock_progress['totalFrames'],
            'current_frame': frame_counter,
            'fps': mock_progress.get('fps', 30),
            'duration': mock_progress['totalFrames'] / mock_progress.get('fps', 30)
        }),
        'picUrl': detection_image_path,
        'snapUrl': detection_image_path,

        # ========== 检测参数 ==========
        'maxConf': float(confidence_threshold),
        'thresholdValue': mock_frame_result.get('max_confidence', 0.0),
        'velocity': mock_frame_result.get('max_velocity', 0.0),

        # ========== 位置和时间信息 ==========
        'recogDate': recogDate,
        'recogLat': recogLat,
        'recogLon': recogLon,

        # ========== 标签信息 ==========
        'labelInfoResult': json.dumps(mock_frame_result.get('labelInfo', {})),

        # ========== 视频路径 ==========
        'videoPath': mock_progress.get('videoPath', ''),

        # ========== 气体泄漏分析核心字段 ==========
        'gasLeakageMode': mock_frame_result.get('gas_mode', False),
        'leakageTrackingData': json.dumps(mock_frame_result.get('gas_tracking_results', {})),
        'gasAnalysisSummary': mock_gas_analysis_summary,
        'finalTrackedLeakagePoints': mock_gas_analysis_summary.get('final_tracked_leakage_points', []),
        'gasTrackingSummary': mock_gas_analysis_summary.get('summary_statistics', {}),
        'temporalGpsSummary': mock_temporal_gps_summary,

        # ========== 关键统计指标 ==========
        'analysisStatus': mock_gas_analysis_summary.get('status', 'processing'),
        'totalFramesAnalyzed': mock_gas_analysis_summary.get('total_frames_analyzed', frame_counter),
        'uniqueLeakageSources': mock_gas_analysis_summary.get('unique_leakage_sources', 0),
        'maxSimultaneousLeakages': mock_gas_analysis_summary.get('max_simultaneous_leakages', 0),
        'leakageFrequency': mock_gas_analysis_summary.get('leakage_frequency', 0.0),
        'totalLeakageEvents': mock_gas_analysis_summary.get('total_leakage_events', 0),
        'overallRiskLevel': mock_gas_analysis_summary.get('summary_statistics', {}).get('overall_risk_level', 'low'),
        'cameraStabilityAssessment': mock_gas_analysis_summary.get('camera_motion_statistics', {}).get('stability_assessment', 'unknown'),
        'confirmedStableLeaks': mock_gas_analysis_summary.get('summary_statistics', {}).get('confirmed_stable_leaks', 0),

        # ========== 实时进度字段 ==========
        'gasLeakageTrackingData': json.dumps(mock_frame_result.get('gas_tracking_results', {})),
        'cameraMotionData': json.dumps(mock_frame_result.get('gas_tracking_results', {}).get('camera_motion', {})),
        'gasDispersionAnalysis': json.dumps(mock_frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {})),
        'trackingStatistics': json.dumps({
            'tracked_objects_count': len(mock_frame_result.get('gas_tracking_results', {}).get('tracked_objects', [])),
            'detections_count': len(mock_frame_result.get('gas_tracking_results', {}).get('detections', [])),
            'frame_processing_time': 0.05
        }),
        'activeLeakageCount': mock_frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('active_leakage_count', 0),
        'totalLeakageArea': mock_frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('total_leakage_area', 0.0),
        'riskLevel': mock_frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('risk_level', 'low'),
        'cameraCompensationEnabled': mock_frame_result.get('gas_tracking_results', {}).get('camera_motion', {}).get('confidence', 0) > 0.3,
        'cameraMotionConfidence': mock_frame_result.get('gas_tracking_results', {}).get('camera_motion', {}).get('confidence', 0.0),
        'currentTrackingSummary': json.dumps({
            'active_leak_count': 2,
            'total_unique_tracks': 3,
            'current_risk_level': 'medium'
        }),

        # ========== 扩展字段 ==========
        'motionPatternDistribution': json.dumps(mock_gas_analysis_summary.get('motion_pattern_distribution', {})),
        'shapeFeatures': json.dumps({
            'detection_shapes': [det.get('shape_analysis', {}) for det in mock_frame_result.get('detections', []) if det.get('shape_analysis')],
            'irregular_shape_count': len([det for det in mock_frame_result.get('detections', []) if det.get('shape_analysis', {}).get('is_irregular', False)])
        }),
        'irregularShapeDetected': any(det.get('shape_analysis', {}).get('is_irregular', False) for det in mock_frame_result.get('detections', [])),
        'mainDispersionDirection': json.dumps(mock_frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('main_direction', {})),
        'algorithmInfo': json.dumps({
            'gas_tracker_enabled': mock_frame_result.get('gas_mode', False),
            'detection_model': 'yolov8',
            'tracker_type': 'enhanced_gas_leakage_tracker',
            'frame_processing_method': 'sam_segmentation'
        }),
        'byteTrackStatus': json.dumps(mock_frame_result.get('gas_tracking_results', {}).get('tracker_status', {})),
        'videoGpsInfo': json.dumps(mock_temporal_gps_summary.get('gps_analysis', {})),
        'videoTimeInfo': json.dumps(mock_temporal_gps_summary.get('temporal_analysis', {})),
        'watermarkQuality': json.dumps(mock_temporal_gps_summary.get('watermark_analysis', {})),
        
        # ========== 新增泄漏统计字段（任务1.1） ==========
        'leakageCount': mock_gas_analysis_summary.get('unique_leakage_sources', 0),
        'leakageTimeRanges': json.dumps([
            {
                'track_id': 1,
                'start_seconds': 10.5,
                'end_seconds': 25.8,
                'duration_seconds': 15.3,
                'peak_confidence_time': 18.2
            },
            {
                'track_id': 2,
                'start_seconds': 30.1,
                'end_seconds': 45.6,
                'duration_seconds': 15.5,
                'peak_confidence_time': 37.8
            }
        ]),
        'confidenceRanges': json.dumps({
            'distribution': {
                '0.5-0.6': 12,
                '0.6-0.7': 25,
                '0.7-0.8': 38,
                '0.8-0.9': 42,
                '0.9-1.0': 18
            },
            'average_confidence': 0.75,
            'median_confidence': 0.78,
            'confidence_stability': 0.82
        }),
        'dispersionBehaviors': json.dumps([
            {
                'track_id': 1,
                'behavior_type': 'expanding',
                'expansion_rate': 2.5,
                'direction_consistency': 0.8,
                'description': '气体呈扩散型扩散，主要向东北方向移动'
            },
            {
                'track_id': 2,
                'behavior_type': 'stationary',
                'expansion_rate': 0.1,
                'direction_consistency': 0.9,
                'description': '气体相对静止，局部小幅波动'
            }
        ]),
        
        # ========== GPS数据持久化标识（任务1.2） ==========
        'requiresGpsPersistence': bool(mock_temporal_gps_summary.get('gps_analysis')),
        'gpsTrajectoryData': json.dumps(mock_temporal_gps_summary.get('gps_analysis', {})) if mock_temporal_gps_summary.get('gps_analysis') else None
    }

    # 验证消息格式
    print(" 测试MQ消息格式与Java端VideoPubProcessData兼容性")
    print("=" * 60)

    # 检查必需字段
    required_fields = [
        'messageType', 'userId', 'videoId', 'taskId', 'frameIndex',
        'progress', 'seconds', 'recogDate', 'recogLat', 'recogLon'
    ]

    missing_fields = []
    for field in required_fields:
        if field not in queue_message:
            missing_fields.append(field)

    if missing_fields:
        print(f" 缺少必需字段: {missing_fields}")
        return False
    else:
        print(" 所有必需字段都存在")

    # 检查数据类型
    type_checks = [
        ('userId', int),
        ('videoId', int),
        ('frameIndex', int),
        ('progress', float),
        ('seconds', float),
        ('gasLeakageMode', bool),
        ('activeLeakageCount', int),
        ('totalLeakageArea', float),
        ('cameraCompensationEnabled', bool),
        ('cameraMotionConfidence', float),
        ('irregularShapeDetected', bool),
        # 新增字段类型检查
        ('leakageCount', int),
        ('requiresGpsPersistence', bool)
    ]

    type_errors = []
    for field, expected_type in type_checks:
        if field in queue_message:
            if not isinstance(queue_message[field], expected_type):
                type_errors.append(f"{field}: 期望{expected_type.__name__}, 实际{type(queue_message[field]).__name__}")

    if type_errors:
        print(f" 数据类型错误: {type_errors}")
        return False
    else:
        print(" 数据类型检查通过")

    # 检查JSON字段的有效性
    json_fields = [
        'videoInfos', 'labelInfoResult', 'leakageTrackingData',
        'gasLeakageTrackingData', 'cameraMotionData', 'gasDispersionAnalysis',
        'trackingStatistics', 'currentTrackingSummary', 'motionPatternDistribution',
        'shapeFeatures', 'mainDispersionDirection', 'algorithmInfo',
        'byteTrackStatus', 'videoGpsInfo', 'videoTimeInfo', 'watermarkQuality'
    ]

    json_errors = []
    for field in json_fields:
        if field in queue_message and queue_message[field] is not None:
            try:
                if isinstance(queue_message[field], str):
                    json.loads(queue_message[field])
            except json.JSONDecodeError as e:
                json_errors.append(f"{field}: {e}")

    if json_errors:
        print(f" JSON格式错误: {json_errors}")
        return False
    else:
        print(" JSON字段格式检查通过")

    # 统计字段覆盖率
    java_fields = [
        # 基础消息字段
        'messageType', 'userId', 'videoId', 'taskId', 'videoName',
        # 进度相关字段
        'progress', 'seconds', 'frameIndex', 'videoInfos', 'picUrl', 'snapUrl',
        # 检测参数
        'maxConf', 'thresholdValue', 'velocity',
        # 位置和时间信息
        'recogDate', 'recogLat', 'recogLon',
        # 标签信息
        'labelInfoResult',
        # 视频路径
        'videoPath',
        # 气体泄漏分析核心字段
        'gasLeakageMode', 'gasAnalysisSummary', 'finalTrackedLeakagePoints',
        'gasTrackingSummary', 'temporalGpsSummary', 'leakageTrackingData',
        # 关键统计指标
        'analysisStatus', 'totalFramesAnalyzed', 'uniqueLeakageSources',
        'maxSimultaneousLeakages', 'leakageFrequency', 'totalLeakageEvents',
        'overallRiskLevel', 'cameraStabilityAssessment', 'confirmedStableLeaks',
        # 实时进度字段
        'gasLeakageTrackingData', 'cameraMotionData', 'gasDispersionAnalysis',
        'trackingStatistics', 'activeLeakageCount', 'totalLeakageArea',
        'riskLevel', 'cameraCompensationEnabled', 'cameraMotionConfidence',
        'currentTrackingSummary',
        # 扩展字段
        'motionPatternDistribution', 'shapeFeatures', 'irregularShapeDetected',
        'mainDispersionDirection', 'algorithmInfo', 'byteTrackStatus',
        'videoGpsInfo', 'videoTimeInfo', 'watermarkQuality',
        # 新增泄漏统计字段（任务1.1）
        'leakageCount', 'leakageTimeRanges', 'confidenceRanges', 'dispersionBehaviors',
        # GPS数据持久化字段（任务1.2）
        'requiresGpsPersistence', 'gpsTrajectoryData'
    ]

    covered_fields = sum(1 for field in java_fields if field in queue_message)
    coverage_rate = covered_fields / len(java_fields) * 100

    print(f" 字段覆盖率: {covered_fields}/{len(java_fields)} ({coverage_rate:.1f}%)")

    # 显示消息大小
    message_json = json.dumps(queue_message, ensure_ascii=False, indent=2)
    message_size = len(message_json.encode('utf-8'))
    print(f" 消息大小: {message_size} bytes ({message_size/1024:.1f} KB)")

    # 保存示例消息
    with open('test_message_sample.json', 'w', encoding='utf-8') as f:
        f.write(message_json)
    print(f" 示例消息已保存到: test_message_sample.json")

    print("=" * 60)
    if coverage_rate >= 90:
        print(" 测试通过！消息格式与Java端高度兼容")
        return True
    else:
        print(" 警告：字段覆盖率较低，建议检查缺失字段")
        return coverage_rate >= 80


if __name__ == '__main__':
    success = test_video_pub_process_data_format()
    exit(0 if success else 1)