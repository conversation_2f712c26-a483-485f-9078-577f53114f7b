#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试调试日志输出
用于验证enhanced_object_tracker中的调试日志是否正常显示
"""

import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def test_video_processing():
    """
    测试视频处理接口，触发enhanced_object_tracker的调试日志
    """
    url = "http://127.0.0.1:9020/process-video"
    
    # 构造测试数据 - 使用本地硬盘的视频文件（绝对路径）
    test_data = {
        "videoPath": "d:/IdeaProjects/github/iflight-data/predict-service/video_cache/429_20d2014575adc00bed6083d8637f7c4f_20250729_214933.mp4",
        "videoId": "429_20d2014575adc00bed6083d8637f7c4f",
        "taskId": "test_task_429",
        "userId": "test_user",
        "originalName": "429_20d2014575adc00bed6083d8637f7c4f_20250729_214933.mp4",
        "currentVideoPathDir": "d:/IdeaProjects/github/iflight-data/predict-service/video_cache",
        "gasLeakageMode": True,
        "enable_video_output": True,
        "output_path": "d:/IdeaProjects/github/iflight-data/predict-service/test_output/429_output.mp4"
    }
    
    logger.info("开始测试视频处理接口...")
    logger.info(f"请求URL: {url}")
    logger.info(f"请求数据: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=30)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text[:500]}...")  # 只显示前500个字符
        
    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败: {e}")
    except Exception as e:
        logger.error(f"未知错误: {e}")

def test_health_check():
    """
    测试健康检查接口
    """
    url = "http://127.0.0.1:9020/health"
    
    logger.info("测试健康检查接口...")
    
    try:
        response = requests.get(url, timeout=10)
        logger.info(f"健康检查响应: {response.status_code} - {response.text}")
        return response.status_code == 200
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("=== 开始调试日志测试 ===")
    
    # 等待服务器完全启动
    logger.info("等待服务器启动...")
    time.sleep(2)
    
    # 测试健康检查
    if test_health_check():
        logger.info("服务器健康检查通过，开始测试视频处理")
        test_video_processing()
    else:
        logger.error("服务器健康检查失败，无法进行测试")
    
    logger.info("=== 调试日志测试完成 ===")