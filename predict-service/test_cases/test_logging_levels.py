#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志级别配置是否正确工作
"""

import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_logging_configuration():
    """测试日志配置是否正确"""
    print("=== 测试日志配置 ===")
    
    # 导入统一日志配置
    from utils.logging_config import setup_logging_from_config
    
    # 配置日志系统
    logger = setup_logging_from_config(logger_name='test_logger')
    
    print("\n=== 测试各级别日志输出 ===")
    logger.debug("这是 DEBUG 级别的日志")
    logger.info("这是 INFO 级别的日志")
    logger.warning("这是 WARNING 级别的日志")
    logger.error("这是 ERROR 级别的日志")
    logger.critical("这是 CRITICAL 级别的日志")
    
    print("\n=== 测试第三方库日志级别 ===")
    
    # 测试第三方库日志
    third_party_loggers = [
        'pika', 'urllib3', 'ultralytics', 'ppocr', 'paddleocr'
    ]
    
    for logger_name in third_party_loggers:
        third_party_logger = logging.getLogger(logger_name)
        current_level = third_party_logger.getEffectiveLevel()
        level_name = logging.getLevelName(current_level)
        print(f"{logger_name}: {level_name} ({current_level})")
        
        # 测试输出
        third_party_logger.debug(f"{logger_name} DEBUG 消息")
        third_party_logger.info(f"{logger_name} INFO 消息")
        third_party_logger.warning(f"{logger_name} WARNING 消息")
    
    print("\n=== 测试根日志器级别 ===")
    root_logger = logging.getLogger()
    root_level = root_logger.getEffectiveLevel()
    root_level_name = logging.getLevelName(root_level)
    print(f"根日志器级别: {root_level_name} ({root_level})")
    
    print("\n=== 测试配置文件读取 ===")
    try:
        from core.managers.config_manager import ConfigManager
        config_manager = ConfigManager()
        log_config = config_manager.logging
        print(f"配置文件中的日志级别: {log_config['level']}")
        print(f"控制台输出: {log_config['console']}")
        print(f"日志文件: {log_config['file']}")
    except Exception as e:
        print(f"配置读取失败: {e}")


def test_dynamic_log_level_change():
    """测试动态修改日志级别"""
    print("\n=== 测试动态日志级别修改 ===")
    
    from utils.logging_config import set_log_level, get_logger
    
    logger = get_logger('dynamic_test')
    
    # 测试不同级别
    levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
    
    for level in levels:
        print(f"\n--- 设置日志级别为 {level} ---")
        set_log_level(level)
        
        logger.debug(f"DEBUG 消息 (当前级别: {level})")
        logger.info(f"INFO 消息 (当前级别: {level})")
        logger.warning(f"WARNING 消息 (当前级别: {level})")
        logger.error(f"ERROR 消息 (当前级别: {level})")


def test_module_specific_loggers():
    """测试模块特定的日志器"""
    print("\n=== 测试模块特定日志器 ===")
    
    # 测试项目中的主要模块
    modules = [
        'services.video_service',
        'core.processors.video_processor',
        'core.processors.ocr_processor',
        'core.managers.config_manager'
    ]
    
    for module_name in modules:
        try:
            module_logger = logging.getLogger(module_name)
            current_level = module_logger.getEffectiveLevel()
            level_name = logging.getLevelName(current_level)
            print(f"{module_name}: {level_name} ({current_level})")
            
            # 测试输出
            module_logger.info(f"{module_name} 测试消息")
            
        except Exception as e:
            print(f"测试 {module_name} 失败: {e}")


if __name__ == "__main__":
    try:
        test_logging_configuration()
        test_dynamic_log_level_change()
        test_module_specific_loggers()
        
        print("\n=== 日志配置测试完成 ===")
        print("如果你看到这条消息，说明日志系统基本正常工作")
        print("请检查上面的输出，确认:")
        print("1. 只有配置级别及以上的日志被输出")
        print("2. 第三方库的日志级别被正确设置")
        print("3. 动态级别修改正常工作")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
