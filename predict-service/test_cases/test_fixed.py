#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复验证测试 - 验证0xC0000005问题是否完全解决
"""

import faulthandler
import sys
import os
import gc
import traceback
import psutil
import time
from datetime import datetime

# 启用崩溃追踪
faulthandler.enable()
print(" 修复验证测试开始...")

# 设置相同的安全环境
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['PP_OCR_VERSION'] = 'PP-OCRv3'
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_cpu_memory_to_use'] = '0.5'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'

def test_imports():
    """测试所有关键导入"""
    print("\n=== 1. 导入测试 ===")
    try:
        import numpy as np
        print(f" NumPy: {np.__version__}")

        import cv2
        print(f" OpenCV: {cv2.__version__}")

        import torch
        print(f" PyTorch: {torch.__version__} (CUDA: {torch.cuda.is_available()})")

        from ultralytics import YOLO
        print(" Ultralytics YOLO导入成功")

        # from core.processors.detection_processor import DetectionProcessor # 已删除
        from core.processors.ocr_processor import OCRProcessor
        from core.processors.video_processor import VideoProcessor
        print(" 自定义处理器导入成功")

        return True
    except Exception as e:
        print(f" 导入失败: {e}")
        traceback.print_exc()
        return False

def test_pytorch_compatibility():
    """测试PyTorch兼容性修复"""
    print("\n=== 2. PyTorch兼容性测试 ===")
    try:
        from ultralytics import YOLO
        print(" 加载YOLO模型...")
        model = YOLO('yolov8n.pt') # 这会触发torch.load
        print(" YOLO模型加载成功 - PyTorch兼容性修复有效!")

        # 测试推理
        import numpy as np
        test_img = np.ones((480, 640, 3), dtype=np.uint8) * 128
        results = model(test_img, verbose=False)
        print(f" 推理测试成功: {len(results)} 个结果")

        return model
    except Exception as e:
        print(f" PyTorch兼容性测试失败: {e}")
        traceback.print_exc()
        return None

def test_ocr_compatibility():
    """测试OCR兼容性修复"""
    print("\n=== 3. OCR兼容性测试 ===")
    try:
        from core.processors.ocr_processor import OCRProcessor

        ocr_processor = OCRProcessor()
        print(" OCR处理器创建成功")

        # 测试处理
        import numpy as np
        test_frame = np.ones((100, 300, 3), dtype=np.uint8) * 255
        result = ocr_processor.process_frame(test_frame)
        print(f" OCR处理成功: {result}")

        # 清理
        ocr_processor.cleanup()
        return True

    except Exception as e:
        print(f" OCR兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def test_memory_stability(model, iterations=100):
    """测试内存稳定性"""
    print(f"\n=== 4. 内存稳定性测试 ({iterations}帧) ===")
    try:
        import numpy as np
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024

        print(f"初始内存: {initial_memory:.1f}MB")

        for i in range(iterations):
            # 创建随机测试图像
            test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

            # 执行推理
            results = model(test_img, verbose=False)

            # 定期检查内存
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                print(f"帧 {i}: 内存 {current_memory:.1f}MB (+{memory_increase:.1f}MB)")

                # 如果内存增长过快，执行垃圾回收
                if memory_increase > 300:
                    print(" 执行垃圾回收...")
                    gc.collect()
                    current_memory = process.memory_info().rss / 1024 / 1024
                    print(f"GC后内存: {current_memory:.1f}MB")

        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        print(f" 内存稳定性测试通过")
        print(f" 最终内存: {final_memory:.1f}MB")
        print(f" 总增长: {total_increase:.1f}MB")

        return total_increase < 200 # 内存增长小于200MB认为稳定

    except Exception as e:
        print(f" 内存稳定性测试失败: {e}")
        traceback.print_exc()
        return False

def test_full_integration():
    """测试完整集成"""
    print("\n=== 5. 完整集成测试 ===")
    try:
        from core.processors.ocr_processor import OCRProcessor
        from core.processors.video_processor import VideoProcessor

        # 初始化所有处理器
        print(" 初始化处理器...")
        ocr_processor = OCRProcessor()
        # 使用配置初始化VideoProcessor
        from core.config.config_manager import ConfigManager
        config = ConfigManager()
        video_processor = VideoProcessor(config, ocr_processor=ocr_processor)

        print(" 所有处理器初始化成功")

        # 模拟处理几帧
        import numpy as np
        for i in range(10):
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

            # OCR处理
            ocr_result = ocr_processor.process_frame(test_frame)

            if i % 5 == 0:
                print(f" 帧 {i}: OCR={bool(ocr_result.get('UTC'))}")

        # 清理
        ocr_processor.cleanup()
        video_processor.cleanup()

        print(" 完整集成测试通过")
        return True

    except Exception as e:
        print(f" 完整集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试流程"""
    print(" 开始修复验证测试...")
    print(f"Python版本: {sys.version}")
    print(f"测试时间: {datetime.now()}")

    results = {}

    # 测试1: 导入
    results['imports'] = test_imports()
    if not results['imports']:
        print(" 导入测试失败，停止测试")
        return results

    # 测试2: PyTorch兼容性
    model = test_pytorch_compatibility()
    results['pytorch_compatibility'] = model is not None

    # 测试3: OCR兼容性
    results['ocr_compatibility'] = test_ocr_compatibility()

    # 测试4: 内存稳定性 (仅在模型加载成功时)
    if model:
        results['memory_stability'] = test_memory_stability(model)
    else:
        results['memory_stability'] = False

    # 测试5: 完整集成
    results['full_integration'] = test_full_integration()

    # 总结
    print("\n" + "="*60)
    print(" 修复验证结果总结:")
    print("="*60)

    all_passed = True
    for test_name, success in results.items():
        status = " 通过" if success else " 失败"
        print(f" {test_name.replace('_', ' ').title()}: {status}")
        if not success:
            all_passed = False

    print("="*60)
    if all_passed:
        print(" 所有测试通过！0xC0000005问题已完全解决！")
        print(" 现在可以安全运行主应用程序:")
        print(" python app.py")
        print(" 或使用: run_fixed.bat")
    else:
        print(" 部分测试失败，可能仍存在问题")
        print(" 建议检查失败的测试项目")

    print("="*60)

    return results

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n 测试完成，共 {len(results)} 项测试")

        # 最终内存检查
        process = psutil.Process()
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"最终内存使用: {final_memory:.1f}MB")

    except Exception as e:
        print(f" 测试程序崩溃: {e}")
        traceback.print_exc()

    print("\n按 Enter 键退出...")
    input()