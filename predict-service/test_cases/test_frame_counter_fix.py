#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AsyncProcessingContext的frame_counter属性修复
"""

import requests
import json
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_frame_counter_fix():
    """
    测试AsyncProcessingContext的frame_counter属性是否正常工作
    """
    logger.info("=== 开始测试frame_counter修复 ===")
    
    # 服务器健康检查
    try:
        health_response = requests.get("http://127.0.0.1:9020/health", timeout=5)
        if health_response.status_code != 200:
            logger.error(f"服务器健康检查失败，状态码: {health_response.status_code}")
            return False
        logger.info("✅ 服务器健康检查通过")
    except Exception as e:
        logger.error(f"❌ 服务器连接失败: {e}")
        return False
    
    # 准备测试数据
    test_video_path = "/video_cache/429_20d2014575adc00bed6083d8637f7c4f_20250729_214933.mp4"
    
    if not Path(test_video_path).exists():
        logger.error(f"❌ 测试视频文件不存在: {test_video_path}")
        return False
    
    # 构建请求数据
    request_data = {
        "videoPath": test_video_path,
        "videoId": "test_frame_counter_429",
        "taskId": "test_frame_counter_task",
        "userId": "test_user",
        "originalName": "test_frame_counter_video.mp4",
        "currentVideoPathDir": "d:/IdeaProjects/github/iflight-data/predict-service/video_cache",
        "gasLeakageMode": True,
        "enable_video_output": True,
        "confidenceThreshold": 0.5
    }
    
    logger.info("🚀 开始测试视频处理接口...")
    logger.info(f"请求URL: http://127.0.0.1:9020/process-video")
    logger.info(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        # 发送视频处理请求
        response = requests.post(
            "http://127.0.0.1:9020/process-video",
            json=request_data,
            timeout=30
        )
        
        logger.info(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"✅ 视频处理请求成功")
            logger.info(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            # 等待一段时间让异步处理开始
            logger.info("⏳ 等待异步处理开始...")
            time.sleep(5)
            
            # 检查任务状态
            task_id = request_data["taskId"]
            logger.info(f"📊 检查任务状态: {task_id}")
            
            return True
        else:
            logger.error(f"❌ 视频处理请求失败，状态码: {response.status_code}")
            logger.error(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 请求异常: {e}")
        return False

def main():
    """
    主函数
    """
    logger.info("=== frame_counter修复测试开始 ===")
    
    success = test_frame_counter_fix()
    
    if success:
        logger.info("✅ frame_counter修复测试完成 - 成功")
        logger.info("🔍 请检查服务器日志，确认没有AttributeError: 'AsyncProcessingContext' object has no attribute 'frame_counter'错误")
    else:
        logger.error("❌ frame_counter修复测试完成 - 失败")
    
    logger.info("=== 测试结束 ===")

if __name__ == "__main__":
    main()