#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试numpy数组JSON序列化修复

功能：验证VideoService._convert_numpy_to_json_serializable方法是否能正确处理numpy数组
作者：Trae 编程助手
日期：2024
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import numpy as np

def _convert_numpy_to_json_serializable(obj):
    """
    递归地将numpy数组和其他不可JSON序列化的对象转换为可序列化格式
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.integer, np.floating)):
        return obj.item()
    elif isinstance(obj, dict):
        return {key: _convert_numpy_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_convert_numpy_to_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(_convert_numpy_to_json_serializable(item) for item in obj)
    else:
        return obj

def test_numpy_json_conversion():
    """
    测试numpy数组转换为JSON可序列化格式
    """
    print("=== 测试numpy数组JSON序列化修复 ===")
    
    # 模拟包含numpy数组的gas_tracking_results数据结构
    test_data = {
        'detections': [
            {
                'bbox': np.array([100, 200, 300, 400]),  # numpy数组
                'confidence': np.float32(0.85),  # numpy浮点数
                'class_id': np.int32(1),  # numpy整数
                'center': np.array([200, 300])
            }
        ],
        'tracked_objects': [
            {
                'track_id': 1,
                'detection': {
                    'bbox': np.array([150, 250, 350, 450]),
                    'confidence': np.float64(0.92)
                },
                'trajectory': np.array([[100, 200], [150, 250], [200, 300]])
            }
        ],
        'camera_motion': {
            'confidence': np.float32(0.75),
            'motion_vector': np.array([5.2, -3.1])
        },
        'gas_dispersion_analysis': {
            'total_leakage_area': np.float64(1250.5),
            'active_leakage_count': np.int32(3),
            'risk_level': 'medium'
        }
    }
    
    print("原始数据类型:")
    print(f"bbox类型: {type(test_data['detections'][0]['bbox'])}")
    print(f"confidence类型: {type(test_data['detections'][0]['confidence'])}")
    print(f"trajectory类型: {type(test_data['tracked_objects'][0]['trajectory'])}")
    
    try:
        # 测试转换函数
        converted_data = _convert_numpy_to_json_serializable(test_data)
        
        print("\n转换后数据类型:")
        print(f"bbox类型: {type(converted_data['detections'][0]['bbox'])}")
        print(f"confidence类型: {type(converted_data['detections'][0]['confidence'])}")
        print(f"trajectory类型: {type(converted_data['tracked_objects'][0]['trajectory'])}")
        
        # 测试JSON序列化
        json_string = json.dumps(converted_data)
        print("\n✅ JSON序列化成功!")
        print(f"JSON字符串长度: {len(json_string)} 字符")
        
        # 测试反序列化
        deserialized_data = json.loads(json_string)
        print("✅ JSON反序列化成功!")
        
        # 验证数据完整性
        assert len(deserialized_data['detections']) == 1
        assert len(deserialized_data['tracked_objects']) == 1
        assert deserialized_data['detections'][0]['bbox'] == [100, 200, 300, 400]
        assert abs(deserialized_data['detections'][0]['confidence'] - 0.85) < 0.001
        assert deserialized_data['tracked_objects'][0]['track_id'] == 1
        print("✅ 数据完整性验证通过!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n=== 测试边界情况 ===")
    
    # 测试空数据
    empty_data = {}
    converted = _convert_numpy_to_json_serializable(empty_data)
    json.dumps(converted)  # 应该不抛出异常
    print("✅ 空数据测试通过")
    
    # 测试嵌套numpy数组
    nested_data = {
        'level1': {
            'level2': {
                'array': np.array([1, 2, 3]),
                'nested_list': [np.array([4, 5]), np.array([6, 7])]
            }
        }
    }
    converted = _convert_numpy_to_json_serializable(nested_data)
    json.dumps(converted)
    print("✅ 嵌套数据测试通过")
    
    # 测试混合数据类型
    mixed_data = {
        'string': 'test',
        'int': 42,
        'float': 3.14,
        'bool': True,
        'none': None,
        'numpy_array': np.array([1, 2, 3]),
        'numpy_int': np.int32(100),
        'numpy_float': np.float64(2.718)
    }
    converted = _convert_numpy_to_json_serializable(mixed_data)
    json.dumps(converted)
    print("✅ 混合数据类型测试通过")
    
    return True

if __name__ == "__main__":
    try:
        success1 = test_numpy_json_conversion()
        success2 = test_edge_cases()
        
        if success1 and success2:
            print("\n🎉 所有测试通过! numpy数组JSON序列化修复成功!")
        else:
            print("\n❌ 部分测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()