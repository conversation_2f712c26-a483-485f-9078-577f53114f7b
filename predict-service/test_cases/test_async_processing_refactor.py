#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步视频处理重构验证测试

测试重构后的_async_process_video方法和相关组件
"""

import unittest
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.context.async_processing_context import AsyncProcessingContext


class TestAsyncProcessingContext(unittest.TestCase):
    """测试异步处理上下文功能"""

    def setUp(self):
        """测试初始化"""
        self.context = AsyncProcessingContext(
            task_id="test_task_001",
            video_id="test_video_001",
            user_id="test_user_001",
            confidence_threshold=0.6,
            gas_leakage_mode=True,
            temp_frames_dir="/tmp/frames",
            output_dir="../output",
            local_output_path="/output/video.mp4",
            minio_output_path="s3://bucket/video.mp4",
            frame_width=1920,
            frame_height=1080,
            fps=30.0
        )

    def test_context_creation(self):
        """测试上下文创建"""
        # 验证基本属性
        self.assertEqual(self.context.task_id, "test_task_001")
        self.assertEqual(self.context.video_id, "test_video_001")
        self.assertEqual(self.context.user_id, "test_user_001")
        self.assertEqual(self.context.confidence_threshold, 0.6)
        self.assertTrue(self.context.gas_leakage_mode)

        # 验证路径配置
        self.assertEqual(self.context.temp_frames_dir, "/tmp/frames")
        self.assertEqual(self.context.output_dir, "/output")
        self.assertEqual(self.context.local_output_path, "/output/video.mp4")
        self.assertEqual(self.context.minio_output_path, "s3://bucket/video.mp4")

        # 验证视频元数据
        self.assertEqual(self.context.frame_width, 1920)
        self.assertEqual(self.context.frame_height, 1080)
        self.assertEqual(self.context.fps, 30.0)

        # 验证初始状态
        self.assertEqual(self.context.frame_processing_time, 0.0)
        self.assertEqual(self.context.synthesis_time, 0.0)
        self.assertFalse(self.context.synthesis_success)
        self.assertIsNone(self.context.gas_analysis_summary)
        self.assertIsNone(self.context.temporal_gps_summary)

        # 验证配置选项
        self.assertTrue(self.context.enable_video_synthesis)
        self.assertFalse(self.context.enable_minio_upload)
        self.assertTrue(self.context.save_summary_files)

    def test_context_methods(self):
        """测试上下文计算方法"""
        # 测试时间计算
        start_time = self.context.start_time
        time.sleep(0.1) # 等待一点时间
        elapsed = self.context.get_total_elapsed_time()
        self.assertGreater(elapsed, 0.0)

        # 测试部分成功判断
        self.assertFalse(self.context.is_partial_success()) # 初始状态

        self.context.frame_processing_time = 10.0
        self.context.synthesis_success = False
        self.assertTrue(self.context.is_partial_success()) # 部分成功

        self.context.synthesis_success = True
        self.assertFalse(self.context.is_partial_success()) # 完全成功

    def test_processing_summary(self):
        """测试处理摘要生成"""
        self.context.frame_processing_time = 15.5
        self.context.synthesis_time = 5.2
        self.context.synthesis_success = True

        summary = self.context.get_processing_summary()

        self.assertIn('total_time', summary)
        self.assertEqual(summary['frame_processing_time'], 15.5)
        self.assertEqual(summary['synthesis_time'], 5.2)
        self.assertTrue(summary['synthesis_success'])
        self.assertEqual(summary['status'], 'success')

    def test_data_availability_checks(self):
        """测试数据可用性检查"""
        # 测试气体分析数据
        self.assertFalse(self.context.has_gas_analysis())

        self.context.gas_analysis_summary = {"test": "data"}
        self.assertTrue(self.context.has_gas_analysis())

        # 测试GPS数据
        self.assertFalse(self.context.has_temporal_gps_data())

        self.context.temporal_gps_summary = {"gps": "data"}
        self.assertTrue(self.context.has_temporal_gps_data())

    def test_video_synthesis_conditions(self):
        """测试视频合成条件判断"""
        # 默认应该可以合成
        self.assertTrue(self.context.should_synthesize_video())

        # 禁用合成
        self.context.enable_video_synthesis = False
        self.assertFalse(self.context.should_synthesize_video())

        # 恢复启用，但缺少路径
        self.context.enable_video_synthesis = True
        self.context.temp_frames_dir = None
        self.assertFalse(self.context.should_synthesize_video())

        self.context.temp_frames_dir = "/tmp/frames"
        self.context.local_output_path = None
        self.assertFalse(self.context.should_synthesize_video())

    def test_video_output_path(self):
        """测试视频输出路径获取"""
        # 合成失败时返回None
        self.context.synthesis_success = False
        self.assertIsNone(self.context.get_video_output_path())

        # 合成成功，但MinIO禁用时返回本地路径
        self.context.synthesis_success = True
        self.context.enable_minio_upload = False
        self.assertEqual(self.context.get_video_output_path(), "/output/video.mp4")

        # 合成成功且MinIO启用时返回MinIO路径
        self.context.enable_minio_upload = True
        self.assertEqual(self.context.get_video_output_path(), "s3://bucket/video.mp4")


class TestAsyncProcessingFlow(unittest.TestCase):
    """测试异步处理流程"""

    def test_processing_status_transitions(self):
        """测试处理状态转换"""
        context = AsyncProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5
        )

        # 初始状态
        summary = context.get_processing_summary()
        self.assertEqual(summary['status'], 'processing')

        # 帧处理完成，但合成失败（部分成功）
        context.frame_processing_time = 10.0
        context.synthesis_success = False
        summary = context.get_processing_summary()
        self.assertEqual(summary['status'], 'partial_success')

        # 完全成功
        context.synthesis_success = True
        summary = context.get_processing_summary()
        self.assertEqual(summary['status'], 'success')

    def test_error_recovery_scenarios(self):
        """测试错误恢复场景"""
        context = AsyncProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5, gas_leakage_mode=True
        )

        # 模拟部分处理成功的场景
        context.frame_processing_time = 15.0 # 帧处理成功
        context.synthesis_time = 3.0 # 合成尝试了但失败
        context.synthesis_success = False # 合成失败
        context.gas_analysis_summary = {"status": "success"} # 气体分析成功
        context.temporal_gps_summary = None # GPS分析失败

        # 验证部分成功状态
        self.assertTrue(context.is_partial_success())
        self.assertTrue(context.has_gas_analysis())
        self.assertFalse(context.has_temporal_gps_data())

        # 验证处理摘要包含所有信息
        summary = context.get_processing_summary()
        self.assertEqual(summary['status'], 'partial_success')
        self.assertEqual(summary['frame_processing_time'], 15.0)
        self.assertEqual(summary['synthesis_time'], 3.0)
        self.assertFalse(summary['synthesis_success'])


class TestConfigurationOptions(unittest.TestCase):
    """测试配置选项"""

    def test_feature_switches(self):
        """测试功能开关"""
        context = AsyncProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5,
            temp_frames_dir="/tmp",
            local_output_path="/output/video.mp4"
        )

        # 测试视频合成开关
        context.enable_video_synthesis = True
        self.assertTrue(context.should_synthesize_video())

        context.enable_video_synthesis = False
        self.assertFalse(context.should_synthesize_video())

        # 测试MinIO上传开关
        context.synthesis_success = True
        context.enable_minio_upload = False
        self.assertEqual(context.get_video_output_path(), "/output/video.mp4")

        context.enable_minio_upload = True
        context.minio_output_path = "s3://bucket/video.mp4"
        self.assertEqual(context.get_video_output_path(), "s3://bucket/video.mp4")

    def test_path_configurations(self):
        """测试路径配置"""
        context = AsyncProcessingContext(
            task_id="test", video_id="test", user_id="test",
            confidence_threshold=0.5
        )

        # 缺少必要路径时不应该合成视频
        self.assertFalse(context.should_synthesize_video())

        # 设置临时帧目录
        context.temp_frames_dir = "/tmp/frames"
        self.assertFalse(context.should_synthesize_video()) # 仍缺少输出路径

        # 设置输出路径
        context.local_output_path = "/output/video.mp4"
        self.assertTrue(context.should_synthesize_video()) # 现在应该可以合成


def run_async_refactor_tests():
    """运行异步处理重构验证测试"""
    print(" 开始运行异步处理重构验证测试...")
    print("=" * 70)

    # 创建测试套件
    test_loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # 添加测试类
    test_suite.addTests(test_loader.loadTestsFromTestCase(TestAsyncProcessingContext))
    test_suite.addTests(test_loader.loadTestsFromTestCase(TestAsyncProcessingFlow))
    test_suite.addTests(test_loader.loadTestsFromTestCase(TestConfigurationOptions))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出结果总结
    print("\n" + "=" * 70)
    print(f" 异步处理重构测试总结:")
    print(f" ├─ 总测试数: {result.testsRun}")
    print(f" ├─ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f" ├─ 失败: {len(result.failures)}")
    print(f" └─ 错误: {len(result.errors)}")

    if result.wasSuccessful():
        print(" 所有测试通过！异步处理重构验证成功。")
        print("\n 重构成果:")
        print(" ├─ AsyncProcessingContext 状态管理")
        print(" ├─ 智能条件判断方法")
        print(" ├─ 处理状态跟踪")
        print(" ├─ 错误恢复机制")
        print(" ├─ 配置选项灵活性")
        print(" └─ 部分成功状态支持")
        return True
    else:
        print(" 部分测试失败，请检查重构实现。")
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f" - {test}: {traceback.splitlines()[-1]}")
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f" - {test}: {traceback.splitlines()[-1]}")
        return False


if __name__ == "__main__":
    success = run_async_refactor_tests()
    sys.exit(0 if success else 1)