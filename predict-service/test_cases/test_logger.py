#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from utils.logging_utils import UUIDLogger, configure_root_logger_with_uuid

# 配置根日志器
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 配置UUID日志系统
uuid_filter = configure_root_logger_with_uuid()
print("UUID日志系统配置成功")

# 测试标准logger
standard_logger = logging.getLogger('test_standard')
print("\n=== 测试标准logger ===")
standard_logger.info("这是标准logger的info消息")
standard_logger.error("这是标准logger的error消息")

# 测试UUIDLogger
print("\n=== 测试UUIDLogger ===")
try:
    uuid_logger = UUIDLogger('test_uuid')
    uuid_logger.info("这是UUIDLogger的info消息")
    uuid_logger.error("这是UUIDLogger的error消息")
    print("UUIDLogger测试成功")
except Exception as e:
    print(f"UUIDLogger测试失败: {e}")

# 测试video_service的logger导入
print("\n=== 测试video_service logger导入 ===")
try:
    from services.video_service import VideoService
    print("video_service导入成功")
    
    # 直接测试video_service中的logger
    from services import video_service
    if hasattr(video_service, 'logger'):
        print("video_service.logger存在")
        video_service.logger.info("直接调用video_service.logger.info")
        video_service.logger.error("直接调用video_service.logger.error")
    else:
        print("video_service.logger不存在")
        
except Exception as e:
    print(f"video_service测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===")