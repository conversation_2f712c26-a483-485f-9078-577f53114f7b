#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import logging
import tempfile
import yaml

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.processors.enhanced_gas_leakage_tracker import EnhancedGasLeakageTracker
from core.config.tracker_config_loader import load_tracker_config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tracker_config_dict():
    """测试使用配置字典初始化追踪器"""
    logger.info("=== 测试配置字典 ===")

    try:
        # 加载配置字典
        tracker_config = load_tracker_config(gas_leakage_mode=True)
        logger.info(f"加载的配置类型: {type(tracker_config)}")
        logger.info(f"配置模式: {tracker_config.get('_mode', 'unknown')}")

        # 使用配置字典创建追踪器
        tracker = EnhancedGasLeakageTracker(
            model_path='../models/best.pt',
            device='cpu', # 使用CPU避免GPU依赖
            tracker_config=tracker_config, # 传递字典
            confidence_threshold=0.3
        )

        logger.info(" 追踪器创建成功")
        logger.info(f"最终配置文件路径: {tracker.tracker_config}")

        # 检查临时文件是否创建
        if tracker.tracker_config_file:
            logger.info(f"临时配置文件: {tracker.tracker_config_file}")
            if os.path.exists(tracker.tracker_config_file):
                logger.info(" 临时配置文件存在")
                # 读取文件内容验证
                with open(tracker.tracker_config_file, 'r', encoding='utf-8') as f:
                    temp_config = yaml.safe_load(f)
                logger.info(f"临时文件内容: {temp_config}")
            else:
                logger.warning(" 临时配置文件不存在")

        # 清理资源
        tracker.cleanup()
        logger.info(" 资源清理完成")

        return True

    except Exception as e:
        logger.error(f" 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_tracker_config_file():
    """测试使用配置文件路径初始化追踪器"""
    logger.info("=== 测试配置文件路径 ===")

    try:
        # 使用现有配置文件
        config_path = '../configs/unified_bytetrack.yaml'

        if not os.path.exists(config_path):
            logger.warning(f"配置文件不存在: {config_path}，跳过此测试")
            return True

        tracker = EnhancedGasLeakageTracker(
            model_path='../models/best.pt',
            device='cpu',
            tracker_config=config_path, # 传递文件路径
            confidence_threshold=0.3
        )

        logger.info(" 追踪器创建成功")
        logger.info(f"配置文件路径: {tracker.tracker_config}")

        # 清理资源
        tracker.cleanup()
        logger.info(" 资源清理完成")

        return True

    except Exception as e:
        logger.error(f" 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_tracker_config_invalid():
    """测试无效配置的处理"""
    logger.info("=== 测试无效配置处理 ===")

    try:
        # 使用无效配置
        tracker = EnhancedGasLeakageTracker(
            model_path='../models/best.pt',
            device='cpu',
            tracker_config=123, # 无效类型
            confidence_threshold=0.3
        )

        logger.info(" 追踪器创建成功（使用默认配置）")
        logger.info(f"最终配置文件路径: {tracker.tracker_config}")

        # 清理资源
        tracker.cleanup()
        logger.info(" 资源清理完成")

        return True

    except Exception as e:
        logger.error(f" 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """运行所有测试"""
    logger.info("开始测试 Enhanced Gas Leakage Tracker 配置修复")

    tests = [
        ("配置字典测试", test_tracker_config_dict),
        ("配置文件路径测试", test_tracker_config_file),
        ("无效配置处理测试", test_tracker_config_invalid)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")

        if test_func():
            logger.info(f" {test_name} 通过")
            passed += 1
        else:
            logger.error(f" {test_name} 失败")

    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*50}")

    if passed == total:
        logger.info(" 所有测试通过！修复成功！")
        return 0
    else:
        logger.error(" 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)