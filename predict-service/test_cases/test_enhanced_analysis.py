#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的泄漏分析功能
包括：
- 任务2.1：泄漏点时间段精确化
- 任务2.2：气体扩散行为分类
- 任务2.3：置信值区间统计
"""

import sys
import os
import numpy as np
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.processors.gas_tracking_finalizer import GasTrackingFinalizer

def create_mock_track_data() -> List[Dict[str, Any]]:
    """创建模拟轨迹数据用于测试"""
    track_data = []
    
    # 模拟一个30帧的气体泄漏轨迹
    for frame in range(30):
        # 模拟置信度变化（先上升后下降）
        if frame < 10:
            confidence = 0.3 + (frame * 0.05)  # 0.3 -> 0.8
        elif frame < 20:
            confidence = 0.8 + (frame - 10) * 0.01  # 0.8 -> 0.9
        else:
            confidence = 0.9 - (frame - 20) * 0.03  # 0.9 -> 0.6
        
        # 模拟位置变化（定向移动）
        center_x = 100 + frame * 2  # 向右移动
        center_y = 200 + frame * 1  # 向下移动
        
        # 模拟面积变化（逐渐扩大）
        width = 50 + frame * 1.5
        height = 40 + frame * 1.2
        
        obj = {
            'frame_number': frame,
            'confidence': confidence,
            'bbox': {
                'x': center_x - width/2,
                'y': center_y - height/2,
                'width': width,
                'height': height
            },
            'center': [center_x, center_y],
            'area': width * height,
            'track_id': 1,
            'class_name': 'gas_leak'
        }
        track_data.append(obj)
    
    return track_data

def test_active_periods_analysis():
    """测试活跃期分析功能"""
    print("\n=== 测试活跃期分析功能 ===")
    
    finalizer = GasTrackingFinalizer()
    track_data = create_mock_track_data()
    fps = 30.0
    
    # 测试活跃期识别
    active_periods = finalizer._identify_active_periods_for_track(track_data, fps)
    
    print(f"识别到 {len(active_periods)} 个活跃期：")
    for i, period in enumerate(active_periods):
        print(f"  活跃期 {i+1}:")
        print(f"    帧范围: {period['start_frame']} - {period['end_frame']}")
        print(f"    时间范围: {period['start_seconds']:.2f}s - {period['end_seconds']:.2f}s")
        print(f"    持续时间: {period['duration_seconds']:.2f}s")
        print(f"    平均置信度: {period['average_confidence']:.3f}")
        print(f"    最高置信度: {period['max_confidence']:.3f}")
    
    # 测试连续性分析
    continuity = finalizer._analyze_leakage_continuity(track_data)
    print(f"\n连续性分析:")
    print(f"  是否连续: {continuity['is_continuous']}")
    print(f"  连续性比率: {continuity['continuity_ratio']:.3f}")
    print(f"  中断次数: {continuity['interruption_count']}")
    print(f"  置信度稳定性: {continuity['confidence_stability']:.3f}")
    
    return len(active_periods) > 0 and continuity['is_continuous']

def test_dispersion_behavior_classification():
    """测试扩散行为分类功能"""
    print("\n=== 测试扩散行为分类功能 ===")
    
    finalizer = GasTrackingFinalizer()
    track_data = create_mock_track_data()
    
    # 测试扩散行为分类
    behavior = finalizer._classify_dispersion_behavior(track_data)
    
    print(f"扩散行为分类结果:")
    print(f"  行为类型: {behavior['behavior_type']}")
    print(f"  描述: {behavior['description']}")
    print(f"  置信度: {behavior['confidence']:.3f}")
    
    metrics = behavior['metrics']
    print(f"\n行为指标:")
    print(f"  平均位置变化: {metrics['average_position_change']:.2f}")
    print(f"  位置变化稳定性: {metrics['position_change_stability']:.3f}")
    print(f"  平均面积变化: {metrics['average_area_change']:.3f}")
    print(f"  面积增长趋势: {metrics['area_growth_trend']:.3f}")
    print(f"  方向一致性: {metrics['direction_consistency']:.3f}")
    
    return behavior['behavior_type'] in ['directional', 'expansion', 'stationary', 'irregular']

def test_confidence_distribution():
    """测试置信度分布统计功能"""
    print("\n=== 测试置信度分布统计功能 ===")
    
    finalizer = GasTrackingFinalizer()
    track_data = create_mock_track_data()
    
    # 测试置信度分布统计
    distribution = finalizer._calculate_confidence_distribution(track_data)
    
    print(f"置信度分布统计:")
    print(f"  总帧数: {distribution['total_frames']}")
    print(f"  平均置信度: {distribution['average_confidence']:.3f}")
    print(f"  置信度稳定性: {distribution['confidence_stability']:.3f}")
    print(f"  置信度趋势: {distribution['confidence_trend']}")
    
    print(f"\n置信度区间分布:")
    for level, stats in distribution['distribution'].items():
        print(f"  {level} ({stats['range']}): {stats['count']} 帧 ({stats['percentage']:.1f}%)")
    
    print(f"\n百分位数:")
    percentiles = distribution['percentiles']
    for p, value in percentiles.items():
        print(f"  {p}: {value:.3f}")
    
    quality = distribution['quality_assessment']
    print(f"\n质量评估:")
    print(f"  高置信度比率: {quality['high_confidence_ratio']:.3f}")
    print(f"  低置信度比率: {quality['low_confidence_ratio']:.3f}")
    print(f"  是否可靠: {quality['is_reliable']}")
    
    return distribution['total_frames'] > 0 and distribution['average_confidence'] > 0

def main():
    """主测试函数"""
    print("🧪 开始测试增强的泄漏分析功能")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("活跃期分析", test_active_periods_analysis),
        ("扩散行为分类", test_dispersion_behavior_classification),
        ("置信度分布统计", test_confidence_distribution)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{status} {test_name}")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"\n❌ 失败 {test_name}: {e}")
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result, error in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
        if error:
            print(f"    错误: {error}")
        if result:
            passed += 1
    
    print(f"\n🎯 通过率: {passed}/{len(tests)} ({passed/len(tests)*100:.1f}%)")
    
    if passed == len(tests):
        print("\n🎉 所有增强功能测试通过！")
        print("✨ 泄漏分析增强功能已成功实现：")
        print("   - 任务2.1: 泄漏点时间段精确化 ✅")
        print("   - 任务2.2: 气体扩散行为分类 ✅")
        print("   - 任务2.3: 置信值区间统计 ✅")
    else:
        print("\n⚠️  部分测试未通过，请检查实现")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)