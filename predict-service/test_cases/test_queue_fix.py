#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队列冲突修复
发送一个简单的视频处理请求，观察是否还有队列参数冲突问题
"""

import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_video_processing():
    """测试视频处理API"""
    
    # API端点
    url = "http://localhost:9020/process-video"
    
    # 测试数据 - 使用正确的API参数格式
    test_data = {
        "videoPath": "test_video.mp4",
        "videoId": "video_420",
        "taskId": "test_queue_fix_420",
        "userId": "test_user",
        "originalName": "test_video.mp4",
        "currentVideoPathDir": "./videos/",
        "gasLeakageMode": True,
        "enable_video_output": True,
        "output_path": "./output/test_queue_fix.mp4"
    }
    
    try:
        logger.info("发送视频处理请求...")
        logger.info(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送POST请求
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {dict(response.headers)}")
        
        # 打印响应内容
        try:
            response_data = response.json()
            logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            logger.info(f"响应文本: {response.text}")
        
        # 检查是否成功
        if response.status_code == 200:
            logger.info("✅ 请求成功发送，没有队列冲突错误")
            return True
        else:
            logger.warning(f"⚠️ 请求返回非200状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error("❌ 无法连接到服务器，请确保服务正在运行")
        return False
    except requests.exceptions.Timeout:
        logger.error("❌ 请求超时")
        return False
    except Exception as e:
        logger.error(f"❌ 请求失败: {e}")
        return False

def test_service_status():
    """测试服务状态"""
    try:
        # 测试健康检查端点
        health_url = "http://localhost:9020/health"
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ 服务健康检查通过")
            return True
        else:
            logger.warning(f"⚠️ 健康检查返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=== 开始测试队列冲突修复 ===")
    
    # 1. 测试服务状态
    logger.info("\n1. 测试服务状态...")
    if not test_service_status():
        logger.error("服务状态检查失败，退出测试")
        return
    
    # 2. 等待一下让服务完全启动
    logger.info("\n2. 等待服务完全启动...")
    time.sleep(2)
    
    # 3. 测试视频处理请求
    logger.info("\n3. 测试视频处理请求...")
    success = test_video_processing()
    
    # 4. 总结
    logger.info("\n=== 测试结果总结 ===")
    if success:
        logger.info("✅ 队列冲突问题已修复，API请求正常")
        logger.info("💡 建议：检查服务日志确认没有队列参数冲突错误")
    else:
        logger.warning("⚠️ 测试未完全成功，请检查服务日志")
        logger.info("💡 建议：查看服务启动日志，确认是否还有RabbitMQ相关错误")
    
    logger.info("\n=== 测试完成 ===")

if __name__ == '__main__':
    main()