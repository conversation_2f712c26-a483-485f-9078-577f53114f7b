# PowerShell 调试启动脚本
# 用于诊断 0xC0000005 访问冲突问题

param(
    [switch]$VerboseMode = $false,
    [switch]$GenerateDump = $false
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$host.UI.RawUI.WindowTitle = "Python 调试增强模式"

Write-Host "🚀 启动增强调试模式的 Python 应用程序" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

$startTime = Get-Date
Write-Host "📅 启动时间: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "📁 当前目录: $(Get-Location)" -ForegroundColor Cyan
Write-Host "💻 PowerShell 版本: $($PSVersionTable.PSVersion)" -ForegroundColor Cyan

# 🚀 步骤1: 设置 Python 故障处理器环境变量
Write-Host "🔍 设置 Python 调试环境变量..." -ForegroundColor Yellow
$env:PYTHONFAULTHANDLER = "1"
$env:PYTHONDEBUG = "1"
$env:PYTHONVERBOSE = if ($VerboseMode) { "1" } else { "0" }
$env:PYTHONASYNCIODEBUG = "1"
$env:PYTHONDEV = "1"
$env:PYTHONPATH = Get-Location
Write-Host "✅ Python 调试变量已设置" -ForegroundColor Green

# 🚀 步骤2: 设置 CUDA 和 GPU 相关环境变量
Write-Host "🔧 禁用 CUDA/GPU 以避免访问冲突..." -ForegroundColor Yellow
$env:CUDA_VISIBLE_DEVICES = "-1"
$env:ENABLE_CUDA = "false"
$env:CUDA_LAUNCH_BLOCKING = "1"
$env:PYTORCH_CUDA_ALLOC_CONF = "max_split_size_mb:512"
Write-Host "✅ CUDA 已禁用" -ForegroundColor Green

# 🚀 步骤3: 设置内存和多线程相关环境变量
Write-Host "🧠 设置内存和多线程优化..." -ForegroundColor Yellow
$env:OMP_NUM_THREADS = "1"
$env:MKL_NUM_THREADS = "1"
$env:NUMBA_NUM_THREADS = "1"
$env:OPENBLAS_NUM_THREADS = "1"
$env:VECLIB_MAXIMUM_THREADS = "1"
$env:KMP_DUPLICATE_LIB_OK = "TRUE"
$env:MKL_THREADING_LAYER = "sequential"
Write-Host "✅ 多线程冲突预防已设置" -ForegroundColor Green

# 🚀 步骤4: 设置 OpenCV 和视频处理相关环境变量
Write-Host "🎬 设置 OpenCV 和视频处理环境..." -ForegroundColor Yellow
$env:OPENCV_DNN_BACKEND_INFERENCE_ENGINE_TYPE = "CPU"
$env:OPENCV_DNN_TARGET = "CPU"
$env:OPENCV_LOG_LEVEL = "ERROR"
$env:FFMPEG_LOG_LEVEL = "error"
Write-Host "✅ OpenCV 优化已设置" -ForegroundColor Green

# 🚀 步骤5: 设置内存调试相关环境变量
Write-Host "🔍 设置内存调试环境..." -ForegroundColor Yellow
$env:MALLOC_CHECK_ = "2"
$env:PYTHONMALLOC = "debug"
$env:PYTHONMALLOCSTATS = "1"
Write-Host "✅ 内存调试已启用" -ForegroundColor Green

# 🚀 步骤6: 设置 PaddlePaddle 相关环境变量
Write-Host "🏊 设置 PaddlePaddle 环境..." -ForegroundColor Yellow
$env:FLAGS_allocator_strategy = "auto_growth"
$env:FLAGS_fraction_of_gpu_memory_to_use = "0.1"
$env:FLAGS_eager_delete_tensor_gb = "0.0"
$env:CPU_NUM = "1"
Write-Host "✅ PaddlePaddle 优化已设置" -ForegroundColor Green

# 🚀 步骤7: 显示关键环境变量
Write-Host ""
Write-Host "📋 当前关键环境变量:" -ForegroundColor Cyan
Write-Host "   PYTHONFAULTHANDLER: $env:PYTHONFAULTHANDLER" -ForegroundColor Gray
Write-Host "   CUDA_VISIBLE_DEVICES: $env:CUDA_VISIBLE_DEVICES" -ForegroundColor Gray
Write-Host "   OMP_NUM_THREADS: $env:OMP_NUM_THREADS" -ForegroundColor Gray
Write-Host "   KMP_DUPLICATE_LIB_OK: $env:KMP_DUPLICATE_LIB_OK" -ForegroundColor Gray
Write-Host "   ENABLE_CUDA: $env:ENABLE_CUDA" -ForegroundColor Gray

# 🚀 步骤8: 检查 Python 环境
Write-Host ""
Write-Host "🐍 检查 Python 环境..." -ForegroundColor Yellow

try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
    
    $pythonPath = where.exe python 2>$null
    Write-Host "📍 Python 路径: $pythonPath" -ForegroundColor Gray
} catch {
    Write-Host "❌ Python 未找到或无法运行" -ForegroundColor Red
    Write-Host "请确保 Python 已安装并添加到 PATH 环境变量中" -ForegroundColor Red
    pause
    exit 1
}

# 🚀 步骤9: 检查关键依赖包
Write-Host ""
Write-Host "📦 检查关键依赖包版本..." -ForegroundColor Yellow

$dependencies = @(
    @{Name="torch"; Command="import torch; print(f'PyTorch: {torch.__version__}')"},
    @{Name="cv2"; Command="import cv2; print(f'OpenCV: {cv2.__version__}')"},
    @{Name="numpy"; Command="import numpy; print(f'NumPy: {numpy.__version__}')"},
    @{Name="PIL"; Command="import PIL; print(f'Pillow: {PIL.__version__}')"},
    @{Name="ultralytics"; Command="import ultralytics; print(f'Ultralytics: {ultralytics.__version__}')"}
)

foreach ($dep in $dependencies) {
    try {
        $result = python -c $dep.Command 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $result" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $($dep.Name) 导入失败: $result" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  $($dep.Name) 未安装或导入失败" -ForegroundColor Yellow
    }
}

# 🚀 步骤10: 配置 Windows 错误报告 (可选)
if ($GenerateDump) {
    Write-Host ""
    Write-Host "🔧 配置 Windows 错误报告以生成转储文件..." -ForegroundColor Yellow
    
    try {
        # 设置注册表以生成用户模式转储
        $regPath = "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting\LocalDumps"
        if (-not (Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "DumpFolder" -Value (Get-Location).Path -Type String
        Set-ItemProperty -Path $regPath -Name "DumpType" -Value 2 -Type DWord  # Full dump
        Set-ItemProperty -Path $regPath -Name "DumpCount" -Value 5 -Type DWord
        
        Write-Host "✅ 转储文件配置完成，将保存到当前目录" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  转储文件配置失败: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 🚀 步骤11: 启动应用程序
Write-Host ""
Write-Host "🚀 启动调试增强版应用程序..." -ForegroundColor Green
Write-Host "📁 崩溃日志将保存到当前目录" -ForegroundColor Cyan
Write-Host "🔍 如果程序崩溃，请查看生成的 crash_*.log 文件" -ForegroundColor Cyan
Write-Host ""

# 记录启动
$logFile = "startup_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
"=== Python 调试会话开始 ===" | Out-File -FilePath $logFile -Encoding UTF8
"启动时间: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"PowerShell 版本: $($PSVersionTable.PSVersion)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Python 版本: $pythonVersion" | Out-File -FilePath $logFile -Append -Encoding UTF8

try {
    # 运行 Python 程序
    $process = Start-Process -FilePath "python" -ArgumentList "debug_app.py" -NoNewWindow -Wait -PassThru
    $exitCode = $process.ExitCode
} catch {
    Write-Host "❌ 启动 Python 程序时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    $exitCode = -1
}

$endTime = Get-Date
$duration = $endTime - $startTime

# 🚀 步骤12: 分析结果
Write-Host ""
Write-Host "📊 程序执行完成" -ForegroundColor Cyan
Write-Host "   退出代码: $exitCode" -ForegroundColor Gray
Write-Host "   运行时长: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Gray
Write-Host "   结束时间: $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray

# 记录结束
"结束时间: $endTime" | Out-File -FilePath $logFile -Append -Encoding UTF8
"退出代码: $exitCode" | Out-File -FilePath $logFile -Append -Encoding UTF8
"运行时长: $duration" | Out-File -FilePath $logFile -Append -Encoding UTF8

if ($exitCode -ne 0) {
    Write-Host ""
    Write-Host "❌ 程序异常退出 (代码: $exitCode)" -ForegroundColor Red
    Write-Host "🔍 请检查以下文件获取更多信息:" -ForegroundColor Yellow
    Write-Host "   - crash_*.log (C级别崩溃信息)" -ForegroundColor Gray
    Write-Host "   - debug_*.log (Python级别调试信息)" -ForegroundColor Gray
    Write-Host "   - $logFile (启动日志)" -ForegroundColor Gray
    
    # 显示最新的日志文件
    Write-Host ""
    Write-Host "📄 当前目录中的日志文件:" -ForegroundColor Cyan
    $logFiles = Get-ChildItem -Path "." -Name "crash_*.log", "debug_*.log", "startup_*.log" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 5
    foreach ($file in $logFiles) {
        $fileInfo = Get-Item $file
        Write-Host "   $file ($(($fileInfo.Length/1KB).ToString('F1')) KB, $($fileInfo.LastWriteTime.ToString('HH:mm:ss')))" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "💡 建议的下一步:" -ForegroundColor Yellow
    Write-Host "   1. 查看上述日志文件的详细内容" -ForegroundColor Gray
    Write-Host "   2. 使用 Windows 事件查看器检查系统日志" -ForegroundColor Gray
    Write-Host "   3. 检查是否有转储文件生成" -ForegroundColor Gray
    Write-Host "   4. 考虑逐个禁用第三方库以隔离问题" -ForegroundColor Gray
    
    if ($GenerateDump) {
        $dumpFiles = Get-ChildItem -Path "." -Name "*.dmp" -ErrorAction SilentlyContinue
        if ($dumpFiles) {
            Write-Host "   5. 使用 WinDbg 或 Visual Studio 分析转储文件:" -ForegroundColor Gray
            foreach ($dump in $dumpFiles) {
                Write-Host "      - $dump" -ForegroundColor Gray
            }
        }
    }
} else {
    Write-Host "✅ 程序正常退出" -ForegroundColor Green
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 