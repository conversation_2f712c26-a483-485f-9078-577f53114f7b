# 开发环境配置文件
# 适用于本地开发和调试环境

[general]
output_dir = ./output
max_workers = 4
gas_leakage_mode = true

[model]
model_path = ./models/best-mini.pt
enable_cuda = true

[smart_ocr]
# 智能OCR策略配置
early_detection_frames = 30
early_detection_interval = 10
success_interval = 10
enable_failure_retry = false
failure_retry_interval = 100
enable_optimization = true
log_detailed_stats = true
estimated_ocr_time_per_frame = 0.1

[storage]
# 存储类型: minio 或 nas
type = nas

[rabbitmq]
host=localhost
port=5672
username=deploy
password=deploy@1234
virtual_host=transfer
# 增强连接配置
heartbeat=60
blocked_connection_timeout=30
socket_timeout=10
stack_timeout=15
connection_attempts=3
retry_delay=2.0
# 连接池配置
max_connections=8
min_connections=2
# 健康检查配置
health_check_interval=30

[minio]
minio_api_url=localhost:9010
access_key=minioadmin
secret_key=minioadmin
secure=False
bucket_name=linked-all

[nas]
# NAS存储配置（本地硬盘）
root_path = /data/nas
backup_enabled = false

[video]
video_codec=h264

[database]
# 开发环境数据库连接（如果需要直连）
host=localhost
port=5433
database=uav_detector
username=deployop
password=deployop@1234

[redis]
# 开发环境Redis连接
host=localhost
port=6379
db=0

[logging]
# 开发环境日志配置
level=INFO
console=true
file=./logs/predict-service-dev.log 