# 生产环境配置文件（Docker Compose）
# 适用于Docker容器化部署环境

[general]
output_dir = /app/output
max_workers = 8
gas_leakage_mode = true

[model]
model_path = /app/models/best-mini.pt
enable_cuda = true
use_gpu = true

[smart_ocr]
# 智能OCR策略配置
early_detection_frames = 30
early_detection_interval = 10
success_interval = 10
enable_failure_retry = false
failure_retry_interval = 100
enable_optimization = true
log_detailed_stats = true
estimated_ocr_time_per_frame = 0.1

[storage]
# 存储类型: minio 或 nas
type = nas

[rabbitmq]
host=rabbitmq
port=5672
username=deploy
password=deploy@1234
virtual_host=transfer

[minio]
minio_api_url=minio:9000
access_key=deployop
secret_key=deployop@1234
secure=False
bucket_name=linked-all

[nas]
# NAS存储配置（本地硬盘）
root_path = /data/nas
backup_enabled = true

[video]
video_codec=h264

[database]
# 生产环境数据库连接
host=postgis
port=5432
database=uav_detector
username=deployop
password=deployop@1234

[redis]
# 生产环境Redis连接
host=redis
port=6379
db=0

[logging]
# 生产环境日志配置
level=INFO
console=false
file=/app/logs/predict-service.log 