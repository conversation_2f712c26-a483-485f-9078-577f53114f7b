[general]
output_dir = /app/output
max_workers = 4
gas_leakage_mode = true

[model]
model_path = ./models/best-mini.pt
enable_cuda = true

[smart_ocr]
# 智能OCR策略配置
early_detection_frames = 30
early_detection_interval = 10
success_interval = 10
enable_failure_retry = false
failure_retry_interval = 100
enable_optimization = true
log_detailed_stats = true
estimated_ocr_time_per_frame = 0.1

[rabbitmq]
host=rabbitmq
port=5672
username=deploy
password=deploy@1234
virtual_host=transfer

[minio]
minio_api_url=minio:9000
access_key=minioadmin
secret_key=minioadmin
secure=False
bucket_name=linked-all

[video]
video_codec=h264