# 预测服务 (Predict Service)

## 概述
预测服务是一个基于Flask的微服务，专门用于视频和图像处理，具备机器学习能力。通过消息队列系统提供强大的任务管理、事件处理和实时进度跟踪功能。

## 架构设计

### 核心组件

#### 1. 应用入口 (app.py)
- 提供视频和图像处理的REST API接口
- 集成日志系统
- 服务生命周期管理

**主要接口：**
- `/process-video`: 视频处理接口
- `/process-image`: 图像处理接口

#### 2. 事件管理器 (event_manager.py)
- 单例模式实现
- RabbitMQ连接管理
- 消息发布和订阅处理
- 重试机制和错误处理

#### 3. 任务管理器 (task_manager.py)
- 视频处理任务生命周期管理
- 任务进度跟踪
- 状态更新机制
- 自动清理过期任务

#### 4. 服务层 (services/)

##### 视频服务 (video_service.py)
- 视频处理的核心编排服务
- 管理多个处理器和服务组件
- 线程池并发处理
- 实时进度跟踪和更新
- 与MinIO存储集成
- 支持任务中断

##### 帧处理器 (frame_processor.py)
- 逐帧视频分析
- 对象检测和掩码处理
- 光流计算
- 置信度映射
- 泄漏检测和速度计算

##### 图像处理器 (image_processor.py)
- 静态图像分析
- 对象检测和分类
- 机器学习模型集成

##### OCR处理器 (ocr_processor.py)
- 图像文字识别
- GPU加速支持
- 文本提取和处理

##### MinIO服务 (minio_service.py)
- 对象存储集成
- 文件上传和下载
- 临时URL生成

##### 标签服务 (label_service.py)
- 标签配置管理
- 检测分类
- 可视化颜色映射

### 目录结构
```
predict-service/
├── app.py                  # 主应用入口
├── config.ini             # 配置文件
├── config_manager.py      # 配置管理
├── event_manager.py       # 事件处理系统
├── task_manager.py        # 任务管理
├── services/
│   ├── video_service.py   # 视频处理编排
│   ├── frame_processor.py # 帧分析
│   ├── image_processor.py # 图像处理
│   ├── ocr_processor.py   # 文字识别
│   ├── minio_service.py   # 存储服务
│   └── label_service.py   # 标签管理
├── models/               # 机器学习模型目录
├── utils/               # 工具函数
└── requirements.txt     # Python依赖
```

## 核心功能

### 1. 视频处理流程
1. 请求接收和验证
2. 任务初始化和配置
3. 逐帧处理：
   - 对象检测
   - 掩码生成
   - 光流计算
   - 泄漏检测
4. 进度跟踪和状态更新
5. 结果存储和通知

### 2. 图像处理流程
1. 图像验证和预处理
2. 对象检测和分类
3. OCR处理（如需要）
4. 结果生成和存储

## 关键特性

### 1. 可靠性
- 线程安全操作
- 健壮的重试机制
- 完善的错误处理
- 详细的日志系统

### 2. 可扩展性
- 模块化设计
- 配置驱动架构
- CPU/GPU处理支持
- 容器化部署就绪

### 3. 监控功能
- 详细的任务进度跟踪
- 完整的日志系统
- 自动化资源清理
- 实时状态更新

## 配置说明

### 环境配置
1. CPU环境：
```bash
pip install -r requirements-cpu.txt
```

2. GPU环境：
```bash
pip install -r requirements.txt
```

### 配置文件 (config.ini)
主要配置项：
```ini
[rabbitmq]
host=localhost
port=5672
username=guest
password=guest
virtual_host=transfer

[service]
output_dir=/app/output
model_path=models/best.pt
```

## 部署说明

### Docker部署
```bash
# 构建Docker镜像
docker build -t predict-service .

# 运行容器
docker run -d \
  -p 9020:9020 \
  -v /path/to/output:/app/output \
  predict-service
```

### 生产环境部署
- 使用Gunicorn作为WSGI服务器
- 支持Docker Compose集成
- 包含健康检查机制

## 开发指南

### 1. 设置开发环境
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux
.\venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements-dev.txt
```

### 2. 运行测试
```bash
python -m pytest tests/
```

### 3. 代码规范
- 遵循PEP 8规范
- 使用类型提示
- 为所有公共函数和类添加文档

## 故障排除

### 常见问题

1. RabbitMQ连接问题
```python
# 检查连接状态
rabbitmq_client.check_connection()
```

2. 任务状态查询
```python
# 获取任务状态
task_manager.get_task_progress(task_id)
```

3. 资源使用监控
- 监控GPU内存使用
- 检查视频存储磁盘空间
- 监控RabbitMQ队列大小

## 未来改进计划

1. 测试完善
- 添加全面的单元测试
- 实现集成测试
- 添加性能基准测试

2. 监控增强
- 添加性能指标收集
- 实现详细的系统监控
- 增强错误跟踪

3. 文档完善
- 添加API文档生成
- 改进代码内文档
- 创建开发指南

4. 功能增强
- 实现健康检查接口
- 添加更多视频格式支持
- 增强错误处理粒度
- 添加批处理能力

## 许可证
详见LICENSE文件。
