#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试光流可视化效果

测试新的光流可视化特点：
1. 边缘箭头 - 表示气体运动方向和强度
2. mask内部HSV对比 - 使用HSV颜色空间表示光流
3. 内部HSV深度对比 - 通过颜色深度变化表示强度

Author: Trae AI Assistant
Date: 2024
"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.processors.improved_gradient_localizer import ImprovedGradientLeakageLocalizer


def create_test_data():
    """
    创建测试数据：模拟光流场和mask
    """
    h, w = 480, 640
    
    # 创建测试图像
    frame = np.random.randint(50, 200, (h, w, 3), dtype=np.uint8)
    
    # 创建圆形mask（模拟气体区域）
    mask = np.zeros((h, w), dtype=np.uint8)
    center_x, center_y = w // 2, h // 2
    radius = 100
    cv2.circle(mask, (center_x, center_y), radius, 255, -1)
    
    # 创建模拟光流场（径向流动，模拟气体扩散）
    flow = np.zeros((h, w, 2), dtype=np.float32)
    
    for y in range(h):
        for x in range(w):
            # 计算相对于中心的位置
            dx = x - center_x
            dy = y - center_y
            distance = np.sqrt(dx**2 + dy**2)
            
            if distance > 0 and distance < radius:
                # 径向流动，强度随距离变化
                intensity = (radius - distance) / radius * 3.0
                flow[y, x, 0] = dx / distance * intensity
                flow[y, x, 1] = dy / distance * intensity
    
    # 添加一些噪声
    noise = np.random.normal(0, 0.2, flow.shape)
    flow += noise
    
    # 计算光流强度
    flow_magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
    
    return frame, mask, flow, flow_magnitude


def test_visualization():
    """
    测试新的光流可视化效果
    """
    print("创建测试数据...")
    frame, mask, flow, flow_magnitude = create_test_data()
    
    print("初始化改进版定位器...")
    localizer = ImprovedGradientLeakageLocalizer(debug_mode=True)
    
    # 创建一些模拟的泄漏源点
    sources = [
        {
            'x': 320,
            'y': 240,
            'confidence': 0.85,
            'stability_score': 0.9,
            'tracker_id': 1,
            'prediction_error': 2.1
        },
        {
            'x': 280,
            'y': 200,
            'confidence': 0.72,
            'stability_score': 0.6,
            'tracker_id': 2,
            'prediction_error': 1.5
        }
    ]
    
    print("生成可视化...")
    visualization = localizer._create_improved_visualization(
        frame, flow, flow_magnitude, sources, mask
    )
    
    # 保存结果
    output_path = "test_optical_flow_visualization.jpg"
    cv2.imwrite(output_path, visualization)
    print(f"可视化结果已保存到: {output_path}")
    
    # 保存原始图像和mask用于对比
    cv2.imwrite("test_original_frame.jpg", frame)
    cv2.imwrite("test_mask.jpg", mask)

    print("所有图像已保存，无需显示窗口")
    
    return visualization


def test_edge_arrows():
    """
    专门测试边缘箭头功能
    """
    print("\n测试边缘箭头功能...")
    
    # 创建简单的测试数据
    h, w = 300, 400
    frame = np.ones((h, w, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 创建矩形mask
    mask = np.zeros((h, w), dtype=np.uint8)
    cv2.rectangle(mask, (100, 75), (300, 225), 255, -1)
    
    # 创建简单的向右流动
    flow = np.zeros((h, w, 2), dtype=np.float32)
    flow[:, :, 0] = 2.0  # 向右流动
    flow[:, :, 1] = 0.5  # 轻微向下
    
    # 在中心区域增加流动强度
    flow[100:200, 150:250, 0] = 4.0
    flow[100:200, 150:250, 1] = 1.0
    
    magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
    
    # 创建定位器并测试
    localizer = ImprovedGradientLeakageLocalizer(debug_mode=True)
    
    # 测试边缘箭头绘制
    test_overlay = frame.copy()
    localizer._draw_edge_flow_arrows(test_overlay, flow, magnitude, mask)
    
    # 保存结果
    output_path = "test_edge_arrows.jpg"
    cv2.imwrite(output_path, test_overlay)
    print(f"边缘箭头测试结果已保存到: {output_path}")
    
    return test_overlay


if __name__ == "__main__":
    print("开始测试光流可视化...")
    
    # 测试完整可视化
    visualization = test_visualization()
    
    # 测试边缘箭头
    edge_test = test_edge_arrows()
    
    print("\n测试完成！")
    print("新的光流可视化特点：")
    print("1. ✓ 边缘箭头 - 表示气体运动方向和强度")
    print("2. ✓ mask内部HSV对比 - 颜色表示方向，饱和度表示强度")
    print("3. ✓ 内部HSV深度对比 - 亮度变化表示流动强度")
    print("4. ✓ 多层次箭头 - 外边缘和内边缘不同颜色的箭头")
    print("5. ✓ 强度分级 - 绿色(低)、黄色(中)、红色(高)")
