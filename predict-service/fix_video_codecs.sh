#!/bin/bash

echo "Restoring video codec support..."

# Update package lists
echo "Updating package lists..."
apt-get update

# Install required video codec libraries
echo "Installing video codec libraries..."
apt-get install -y \
    libx264-dev \
    libx265-dev \
    libvpx-dev \
    libfdk-aac-dev \
    libmp3lame-dev \
    libopus-dev \
    ffmpeg \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libavresample-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav

# Reinstall OpenCV to support new codecs
echo "Reinstalling OpenCV..."
pip uninstall -y opencv-python opencv-python-headless opencv-contrib-python
pip install --no-cache-dir opencv-python==*********

# Test if codecs are available
echo "Testing available codecs..."
python3 << 'EOF'
import cv2
import numpy as np
import tempfile
import os

print(f"OpenCV version: {cv2.__version__}")

# Codecs to test
codecs_to_test = [
    ('H264', '.mp4'),
    ('XVID', '.avi'),
    ('MJPG', '.avi'),
    (0, '.avi')  # No compression
]

working_codecs = []

for codec, ext in codecs_to_test:
    try:
        with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
            output_path = tmp_file.name

        if codec == 0:
            fourcc = 0
        else:
            fourcc = cv2.VideoWriter_fourcc(*codec)

        writer = cv2.VideoWriter(output_path, fourcc, 24.0, (640, 480))

        if writer.isOpened():
            # Try writing one frame
            test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            success = writer.write(test_frame)
            writer.release()

            if success is None and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                working_codecs.append(f"{codec}_{ext}")
                print(f"{codec} + {ext} works")
            else:
                print(f"{codec} + {ext} failed to write")
        else:
            print(f"{codec} + {ext} could not be opened")

        # Clean up temporary file
        if os.path.exists(output_path):
            os.remove(output_path)

    except Exception as e:
        print(f"{codec} + {ext} exception: {e}")

print(f"\nWorking codecs: {working_codecs}")
if not working_codecs:
    print("No usable codecs found; the system will use uncompressed AVI format by default.")
else:
    print("At least one codec is usable.")

EOF

echo "Restore complete!"
