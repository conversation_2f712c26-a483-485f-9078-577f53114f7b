@echo off
echo 🚀 启动修复后的预测服务...
echo.

:: 设置环境变量
set CUDA_VISIBLE_DEVICES=-1
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set KMP_DUPLICATE_LIB_OK=TRUE
set PP_OCR_VERSION=PP-OCRv3

:: PaddlePaddle安全设置
set FLAGS_allocator_strategy=auto_growth
set FLAGS_fraction_of_cpu_memory_to_use=0.5
set FLAGS_eager_delete_tensor_gb=0.0

echo ✅ 环境变量设置完成
echo 🔧 PyTorch兼容性: 已修复weights_only问题
echo 🔧 OCR兼容性: 使用PP-OCRv3版本
echo 🔧 内存管理: 启用自动监控和清理
echo.

:: 启动应用
echo 🎯 启动修复后的应用程序...
python app.py

echo.
echo 程序已退出，按任意键关闭...
pause > nul 