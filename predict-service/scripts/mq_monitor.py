#!/usr/bin/env python3
"""
MQ 监控脚本
监控 RabbitMQ 连接状态、队列情况和消息处理状态
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.managers.config_manager import ConfigManager
from core.managers.event_manager_kombu import EventManagerKombu as EventManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('MQMonitor')

class MQMonitor:
    """MQ 监控器"""

    def __init__(self, config_path: str = None):
        self.config = ConfigManager()
        self.event_manager = None
        self.is_running = False

    def initialize(self) -> bool:
        """初始化监控器"""
        try:
            self.event_manager = EventManager(self.config)
            logger.info("MQ Monitor initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize MQ Monitor: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        if not self.event_manager:
            return {'status': 'not_initialized'}

        try:
            health_status = self.event_manager.get_health_status()
            return {
                'timestamp': datetime.now().isoformat(),
                'publisher_healthy': health_status.get('publisher_healthy', False),
                'consumer_healthy': health_status.get('consumer_healthy', False),
                'is_shutting_down': health_status.get('is_shutting_down', False),
                'last_heartbeat': health_status.get('last_heartbeat', 0),
                'cached_messages': health_status.get('cached_messages', 0),
                'stats': health_status.get('stats', {})
            }
        except Exception as e:
            logger.error(f"Failed to get connection status: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }

    def check_queue_health(self) -> Dict[str, Any]:
        """检查队列健康状态"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'queues': {}
        }

        # 这里可以添加更多队列检查逻辑
        # 例如：检查队列长度、消费速率等

        return result

    def test_message_flow(self) -> bool:
        """测试消息流"""
        try:
            test_task_id = f"monitor_test_{int(time.time())}"
            test_message = {
                'messageType': 'test',
                'timestamp': time.time(),
                'test_id': test_task_id
            }

            # 发送测试消息
            self.event_manager.enqueue_send_message(test_task_id, test_message)
            logger.info(f"Test message sent for task {test_task_id}")
            return True

        except Exception as e:
            logger.error(f"Message flow test failed: {e}")
            return False

    def generate_report(self) -> Dict[str, Any]:
        """生成监控报告"""
        connection_status = self.get_connection_status()
        queue_health = self.check_queue_health()
        message_flow_ok = self.test_message_flow()

        # 计算整体健康评分
        health_score = 0
        if connection_status.get('publisher_healthy', False):
            health_score += 40
        if connection_status.get('consumer_healthy', False):
            health_score += 40
        if message_flow_ok:
            health_score += 20

        overall_status = 'healthy' if health_score >= 80 else \
                        'warning' if health_score >= 60 else 'critical'

        return {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'health_score': health_score,
            'connection_status': connection_status,
            'queue_health': queue_health,
            'message_flow_test': message_flow_ok
        }

    def run_continuous_monitoring(self, interval: int = 30):
        """运行连续监控"""
        logger.info(f"Starting continuous monitoring (interval: {interval}s)")
        self.is_running = True

        try:
            while self.is_running:
                report = self.generate_report()

                # 输出监控结果
                print(f"\n{'='*50}")
                print(f"MQ Monitor Report - {report['timestamp']}")
                print(f"{'='*50}")
                print(f"Overall Status: {report['overall_status'].upper()}")
                print(f"Health Score: {report['health_score']}/100")

                conn_status = report['connection_status']
                print(f"\nConnection Status:")
                print(f" Publisher: {'' if conn_status.get('publisher_healthy') else ''}")
                print(f" Consumer: {'' if conn_status.get('consumer_healthy') else ''}")
                print(f" Message Flow Test: {'' if report['message_flow_test'] else ''}")

                if 'cached_messages' in conn_status:
                    print(f" Cached Messages: {conn_status['cached_messages']}")

                if 'stats' in conn_status and conn_status['stats']:
                    stats = conn_status['stats']
                    print(f"\nStatistics:")
                    print(f" Messages Sent: {stats.get('messages_sent', 0)}")
                    print(f" Messages Failed: {stats.get('messages_failed', 0)}")
                    print(f" Connection Failures: {stats.get('connection_failures', 0)}")

                # 如果状态不健康，显示警告
                if report['overall_status'] != 'healthy':
                    print(f"\n Warning: MQ is not fully healthy!")
                    if not conn_status.get('publisher_healthy'):
                        print(" - Publisher connection is down")
                    if not conn_status.get('consumer_healthy'):
                        print(" - Consumer connection is down")
                    if not report['message_flow_test']:
                        print(" - Message flow test failed")

                time.sleep(interval)

        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error in continuous monitoring: {e}")
        finally:
            self.stop()

    def stop(self):
        """停止监控"""
        logger.info("Stopping MQ monitor...")
        self.is_running = False

        if self.event_manager:
            try:
                self.event_manager.close()
            except Exception as e:
                logger.error(f"Error closing event manager: {e}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='MQ Health Monitor')
    parser.add_argument('--interval', '-i', type=int, default=30,
                       help='Monitoring interval in seconds (default: 30)')
    parser.add_argument('--once', action='store_true',
                       help='Run once and exit')
    parser.add_argument('--json', action='store_true',
                       help='Output in JSON format')

    args = parser.parse_args()

    monitor = MQMonitor()

    if not monitor.initialize():
        logger.error("Failed to initialize monitor")
        sys.exit(1)

    try:
        if args.once:
            # 运行一次
            report = monitor.generate_report()

            if args.json:
                print(json.dumps(report, indent=2))
            else:
                print(f"\nMQ Health Report - {report['timestamp']}")
                print(f"Overall Status: {report['overall_status'].upper()}")
                print(f"Health Score: {report['health_score']}/100")

                conn_status = report['connection_status']
                print(f"Publisher Healthy: {conn_status.get('publisher_healthy', False)}")
                print(f"Consumer Healthy: {conn_status.get('consumer_healthy', False)}")
                print(f"Message Flow Test: {report['message_flow_test']}")
        else:
            # 连续监控
            monitor.run_continuous_monitoring(args.interval)

    except Exception as e:
        logger.error(f"Monitor error: {e}")
        sys.exit(1)
    finally:
        monitor.stop()

if __name__ == '__main__':
    main()