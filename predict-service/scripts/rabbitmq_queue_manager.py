#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RabbitMQ队列管理工具
功能：
1. 清理冲突的队列
2. 检查队列状态
3. 重建队列（如果需要）
"""

import subprocess
import sys
import json
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class RabbitMQQueueManager:
    """RabbitMQ队列管理器"""
    
    def __init__(self, vhost: str = 'transfer', container_name: str = 'rabbitmq'):
        """
        初始化队列管理器
        参数:
            vhost: RabbitMQ虚拟主机名
            container_name: RabbitMQ容器名称
        """
        self.vhost = vhost
        self.container_name = container_name
    
    def _run_rabbitmqctl_command(self, command: str) -> str:
        """
        执行rabbitmqctl命令
        参数:
            command: 要执行的命令
        返回:
            命令输出结果
        """
        full_command = f"docker exec -it {self.container_name} rabbitmqctl {command}"
        try:
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode != 0:
                logger.error(f"命令执行失败: {full_command}")
                logger.error(f"错误输出: {result.stderr}")
                return ""
            return result.stdout
        except subprocess.TimeoutExpired:
            logger.error(f"命令执行超时: {full_command}")
            return ""
        except Exception as e:
            logger.error(f"命令执行异常: {e}")
            return ""
    
    def list_queues(self) -> List[Dict[str, Any]]:
        """
        列出所有队列及其属性
        返回:
            队列信息列表
        """
        command = f"list_queues name durable auto_delete messages consumers -p {self.vhost}"
        output = self._run_rabbitmqctl_command(command)
        
        queues = []
        lines = output.strip().split('\n')
        
        # 跳过标题行
        for line in lines[2:]:  # 前两行通常是标题
            if line.strip() and not line.startswith('Listing'):
                parts = line.split()
                if len(parts) >= 5:
                    queues.append({
                        'name': parts[0],
                        'durable': parts[1] == 'true',
                        'auto_delete': parts[2] == 'true',
                        'messages': int(parts[3]),
                        'consumers': int(parts[4])
                    })
        
        return queues
    
    def delete_queue(self, queue_name: str) -> bool:
        """
        删除指定队列
        参数:
            queue_name: 队列名称
        返回:
            是否删除成功
        """
        command = f"delete_queue {queue_name} -p {self.vhost}"
        output = self._run_rabbitmqctl_command(command)
        
        if "successfully deleted" in output:
            logger.info(f"队列 {queue_name} 删除成功")
            return True
        else:
            logger.error(f"队列 {queue_name} 删除失败: {output}")
            return False
    
    def purge_queue(self, queue_name: str) -> bool:
        """
        清空指定队列的消息
        参数:
            queue_name: 队列名称
        返回:
            是否清空成功
        """
        command = f"purge_queue {queue_name} -p {self.vhost}"
        output = self._run_rabbitmqctl_command(command)
        
        if "messages purged" in output or "Purging queue" in output:
            logger.info(f"队列 {queue_name} 消息清空成功")
            return True
        else:
            logger.error(f"队列 {queue_name} 消息清空失败: {output}")
            return False
    
    def clean_task_queues(self, keep_active: bool = True) -> int:
        """
        清理任务队列（queue_开头的队列）
        参数:
            keep_active: 是否保留有消费者的队列
        返回:
            清理的队列数量
        """
        queues = self.list_queues()
        cleaned_count = 0
        
        for queue in queues:
            queue_name = queue['name']
            
            # 只处理任务队列
            if not queue_name.startswith('queue_'):
                continue
            
            # 如果设置保留活跃队列，跳过有消费者的队列
            if keep_active and queue['consumers'] > 0:
                logger.info(f"跳过活跃队列: {queue_name} (消费者数: {queue['consumers']})")
                continue
            
            # 删除队列
            if self.delete_queue(queue_name):
                cleaned_count += 1
        
        logger.info(f"清理完成，共删除 {cleaned_count} 个任务队列")
        return cleaned_count
    
    def show_queue_status(self):
        """
        显示队列状态报告
        """
        queues = self.list_queues()
        
        if not queues:
            logger.info("未找到任何队列")
            return
        
        logger.info("=== 队列状态报告 ===")
        logger.info(f"虚拟主机: {self.vhost}")
        logger.info(f"总队列数: {len(queues)}")
        
        # 按类型分组
        task_queues = [q for q in queues if q['name'].startswith('queue_')]
        system_queues = [q for q in queues if not q['name'].startswith('queue_')]
        
        logger.info(f"任务队列数: {len(task_queues)}")
        logger.info(f"系统队列数: {len(system_queues)}")
        
        # 详细信息
        logger.info("\n=== 详细队列信息 ===")
        for queue in queues:
            status = "活跃" if queue['consumers'] > 0 else "空闲"
            logger.info(
                f"队列: {queue['name']:20} | "
                f"持久化: {queue['durable']:5} | "
                f"自动删除: {queue['auto_delete']:5} | "
                f"消息数: {queue['messages']:6} | "
                f"消费者: {queue['consumers']:3} | "
                f"状态: {status}"
            )

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RabbitMQ队列管理工具')
    parser.add_argument('--action', choices=['list', 'clean', 'delete', 'purge'], 
                       default='list', help='执行的操作')
    parser.add_argument('--queue', help='指定队列名称（用于delete和purge操作）')
    parser.add_argument('--keep-active', action='store_true', 
                       help='清理时保留有消费者的队列')
    parser.add_argument('--vhost', default='transfer', help='RabbitMQ虚拟主机')
    parser.add_argument('--container', default='rabbitmq', help='RabbitMQ容器名称')
    
    args = parser.parse_args()
    
    manager = RabbitMQQueueManager(vhost=args.vhost, container_name=args.container)
    
    try:
        if args.action == 'list':
            manager.show_queue_status()
        
        elif args.action == 'clean':
            logger.info("开始清理任务队列...")
            cleaned = manager.clean_task_queues(keep_active=args.keep_active)
            logger.info(f"清理完成，共删除 {cleaned} 个队列")
        
        elif args.action == 'delete':
            if not args.queue:
                logger.error("删除操作需要指定队列名称 (--queue)")
                sys.exit(1)
            manager.delete_queue(args.queue)
        
        elif args.action == 'purge':
            if not args.queue:
                logger.error("清空操作需要指定队列名称 (--queue)")
                sys.exit(1)
            manager.purge_queue(args.queue)
    
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()