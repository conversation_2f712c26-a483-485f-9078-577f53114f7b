#!/bin/bash

# 🚀 Docker 容器启动脚本
echo "🚀 开始执行启动脚本..."

# 切换到应用目录
cd /app

# 验证关键依赖
echo "🔍 验证关键依赖..."
python3 -c "import cv2; print(f'OpenCV: {cv2.__version__}')" || echo "⚠️ OpenCV导入失败"
python3 -c "import paddle; print(f'PaddlePaddle: {paddle.__version__}')" || echo "⚠️ PaddlePaddle导入失败"
python3 -c "import torch; print(f'PyTorch: {torch.__version__}')" || echo "⚠️ PyTorch导入失败"

# 启动Python应用
echo "🔥 启动Python应用..."
echo "📍 当前工作目录: $(pwd)"
echo "🐍 Python版本: $(python --version)"

# 执行应用
exec python app.py 