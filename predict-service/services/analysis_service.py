import numpy as np
import cv2
import torch
from PIL import Image
import io
from typing import Dict, List, Any
from core.managers.config_manager import ConfigManager
from core.processors.image_processor import ImageProcessor
from core.processors.spectral_processor import SpectralProcessor


class AnalysisService:
    """
    分析服务类 - Analysis Service

    主要功能：
    - 图像目标检测分析（无人机检测、气体泄漏检测等）
    - 光谱数据分析
    - 环境数据处理和整合
    - 检测结果的后处理和格式化

    Main Functions:
    - Image target detection analysis (drone detection, gas leak detection, etc.)
    - Spectral data analysis
    - Environmental data processing and integration
    - Post-processing and formatting of detection results

    使用的模型：YOLOv8 目标检测模型
    支持的检测类别：甲烷(METHANE)、丙烷(PROPANE)、丁烷(BUTANE)
    """

    def __init__(self, config: ConfigManager):
        """
        初始化分析服务
        Initialize the analysis service

        Args:
            config (ConfigManager): 配置管理器，用于获取模型路径等配置信息
                                   Configuration manager for getting model paths and other settings
        """
        self.config = config
        # 初始化图像处理器 - Initialize image processor
        self.image_processor = ImageProcessor()
        # 初始化光谱处理器 - Initialize spectral processor
        self.spectral_processor = SpectralProcessor()
        # 加载 YOLOv8 目标检测模型 - Load YOLOv8 target detection model
        self.model = self._load_model()

    def _load_model(self):
        """
        加载 YOLOv8 目标检测模型
        Load YOLOv8 target detection model

        Returns:
            torch.nn.Module: 加载的 YOLOv8 模型实例
                           Loaded YOLOv8 model instance
        """
        # 从配置中获取模型文件路径 - Get model file path from configuration
        model_path = self.config.get('model', 'path')

        # 检测并设置计算设备（优先使用 GPU） - Detect and set computing device (prefer GPU)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 从 Ultralytics Hub 加载自定义 YOLOv8 模型 - Load custom YOLOv8 model from Ultralytics Hub
        model = torch.hub.load('ultralytics/yolov8', 'custom', path=model_path)

        # 将模型移动到指定设备 - Move model to specified device
        model.to(device)
        return model

    def analyze_image(self, image_data: bytes) -> Dict[str, Any]:
        """
        分析图像中的目标检测
        Analyze target detection in images

        主要步骤：
        1. 解码图像数据
        2. 执行目标检测
        3. 处理检测结果

        Main steps:
        1. Decode image data
        2. Execute target detection
        3. Process detection results

        Args:
            image_data (bytes): 原始图像数据（二进制格式）
                              Raw image data in binary format

        Returns:
            Dict[str, Any]: 包含检测结果的字典
                          Dictionary containing detection results
                          {
                              'detection_points': List[Dict], # 检测到的目标列表
                              'environmental_data': Dict, # 环境数据
                              'timestamp': str # 分析时间戳
                          }
        """
        # 步骤1：解码图像数据为 OpenCV 格式 - Step 1: Decode image data to OpenCV format
        image = self.image_processor.decode_image(image_data)

        # 步骤2：使用 YOLOv8 模型进行目标检测 - Step 2: Perform target detection using YOLOv8 model
        results = self.image_processor.detect(image)

        # 步骤3：处理和格式化检测结果 - Step 3: Process and format detection results
        return self._process_results(results)

    def analyze_spectrum(self, spectral_data: bytes) -> Dict[str, Any]:
        """
        进行光谱分析
        Perform spectral analysis

        用于分析光谱数据，识别气体成分和浓度
        Used to analyze spectral data and identify gas components and concentrations

        Args:
            spectral_data (bytes): 光谱数据（二进制格式）
                                 Spectral data in binary format

        Returns:
            Dict[str, Any]: 光谱分析结果
                          Spectral analysis results
        """
        return self.spectral_processor.analyze(spectral_data)

    def _process_results(self, results) -> Dict[str, Any]:
        """
        处理 YOLOv8 检测结果
        Process YOLOv8 detection results

        将原始检测结果转换为结构化的数据格式，包括：
        - 边界框坐标
        - 检测置信度
        - 目标类别
        - 目标中心点坐标

        Convert raw detection results to structured data format, including:
        - Bounding box coordinates
        - Detection confidence
        - Target category
        - Target center point coordinates

        Args:
            results: YOLOv8 模型的原始检测结果
                    Raw detection results from YOLOv8 model

        Returns:
            Dict[str, Any]: 格式化的检测结果
                          Formatted detection results
        """
        detections = []

        # 遍历检测结果中的每个目标 - Iterate through each target in detection results
        for det in results.xyxy[0]: # xyxy格式：[x1, y1, x2, y2, confidence, class]
            # 提取检测信息 - Extract detection information
            x1, y1, x2, y2, conf, cls = det.tolist()

            # 构建单个检测结果字典 - Build single detection result dictionary
            detection = {
                'bbox': [x1, y1, x2, y2], # 边界框坐标 [左上x, 左上y, 右下x, 右下y]
                'confidence': conf, # 检测置信度 (0-1)
                'class': self._get_class_name(int(cls)), # 目标类别名称
                'center': [(x1 + x2) / 2, (y1 + y2) / 2] # 目标中心点坐标 [center_x, center_y]
            }
            detections.append(detection)

        # 返回完整的分析结果 - Return complete analysis results
        return {
            'detection_points': detections, # 所有检测到的目标
            'environmental_data': self._get_environmental_data(), # 环境数据
            'timestamp': self.config.get_current_time() # 分析时间戳
        }

    def _get_environmental_data(self) -> Dict[str, float]:
        """
        获取环境数据
        Get environmental data

        获取当前环境的物理参数，这些数据对于气体检测和分析很重要
        Get current environmental physical parameters, which are important for gas detection and analysis

        注意：当前返回模拟数据，实际部署时应连接真实传感器
        Note: Currently returns simulated data, should connect to real sensors in actual deployment

        Returns:
            Dict[str, float]: 环境数据字典
                            Environmental data dictionary
                            {
                                'temperature': float, # 温度 (°C)
                                'humidity': float, # 湿度 (%)
                                'pressure': float, # 大气压力 (kPa)
                                'wind_speed': float, # 风速 (m/s)
                                'wind_direction': float # 风向 (度)
                            }
        """
        # TODO: 实际部署时应从传感器获取真实环境数据
        # TODO: Should get real environmental data from sensors in actual deployment
        return {
            'temperature': 25.0, # 环境温度 - Ambient temperature (°C)
            'humidity': 60.0, # 相对湿度 - Relative humidity (%)
            'pressure': 101.325, # 大气压力 - Atmospheric pressure (kPa)
            'wind_speed': 2.5, # 风速 - Wind speed (m/s)
            'wind_direction': 180.0 # 风向角度 - Wind direction (degrees)
        }

    def _get_class_name(self, class_id: int) -> str:
        """
        获取类别名称
        Get class name

        将数字类别ID转换为对应的气体类别名称
        Convert numeric class ID to corresponding gas category name

        Args:
            class_id (int): 模型输出的类别ID
                          Class ID output by the model

        Returns:
            str: 对应的气体类别名称
                Corresponding gas category name
        """
        # 气体类别映射表 - Gas category mapping table
        classes = {
            0: 'METHANE', # 甲烷 - Methane
            1: 'PROPANE', # 丙烷 - Propane
            2: 'BUTANE' # 丁烷 - Butane
        }

        # 返回对应的类别名称，如果ID不存在则返回 'UNKNOWN'
        # Return corresponding class name, or 'UNKNOWN' if ID doesn't exist
        return classes.get(class_id, 'UNKNOWN')
