import logging
import os
from minio import Minio
from typing import Optional

logger = logging.getLogger(__name__)

class MinioService:
    def __init__(self, config):
        self.config = config
        self.minio_client = Minio(
            endpoint=self.config.minio_url,
            access_key=self.config.access_key,
            secret_key=self.config.secret_key,
            secure=self.config.secure
        )
        # Use absolute path for cache directory
        self.cache_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'video_cache'))
        os.makedirs(self.cache_dir, exist_ok=True)

    def download_file(self, minio_path: str, task_id: str) -> str:
        """从MinIO下载文件到本地缓存"""
        try:
            bucket_name = self.config.bucket_name
            # Fix object_name construction
            object_name = minio_path.replace('\\', '/').lstrip('/')
            local_path = os.path.join(self.cache_dir, f"{task_id}_{os.path.basename(minio_path)}")

            if not os.path.exists(local_path):
                logger.info(f"Downloading file from MinIO: {minio_path} to {local_path}")
                self.minio_client.fget_object(bucket_name, object_name, local_path)
            else:
                logger.info(f"Using cached file: {local_path}")

            return local_path
        except Exception as e:
            logger.error(f"Error downloading file from MinIO: {str(e)}")
            raise

    def upload_file(self, local_path: str, minio_path: str) -> None:
        """将本地文件上传到MinIO"""
        try:
            bucket_name = self.config.bucket_name
            # Fix object_name construction
            object_name = minio_path.replace('\\', '/').lstrip('/')
            logger.info(f"Uploading file to MinIO: {local_path} -> {minio_path}")
            self.minio_client.fput_object(bucket_name, object_name, local_path)
        except Exception as e:
            logger.error(f"Error uploading file to MinIO: {str(e)}")
            raise

    def cleanup_cache(self, task_id: str) -> None:
        """清理指定任务的缓存文件"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.startswith(f"{task_id}_"):
                    file_path = os.path.join(self.cache_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Cleaned up cached file: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up cache: {str(e)}")

    def get_local_output_path(self, minio_path: str, task_id: str) -> str:
        """根据MinIO路径生成本地输出路径"""
        return os.path.join(self.cache_dir, f"{task_id}_output_{os.path.basename(minio_path)}")
