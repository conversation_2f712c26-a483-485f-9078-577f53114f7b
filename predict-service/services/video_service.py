#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
import threading
import time
import uuid
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import json

import cv2
import numpy as np
# import pika # 已替换为kombu
from flask import jsonify, Response
from werkzeug.exceptions import BadRequest

from .file_service import FileServiceFactory
from core.processors.video_processor import VideoProcessor
from core.processors.image_processor import ImageProcessor
from core.managers.task_manager import TaskManager
from core.managers.event_manager import EventManager
from core.context.video_processing_context import VideoProcessingContext
from core.context.async_processing_context import AsyncProcessingContext
from core.analyzers.enhanced_data_analyzer import EnhancedDataAnalyzer
from core.models.enhanced_message_structure import EnhancedMessageBuilder

# 导入UUID日志工具
try:
    from utils.logging_utils import UUIDLogger
    logger = UUIDLogger(__name__)
except ImportError:
    logger = logging.getLogger(__name__)

class VideoService:
    def __init__(self, config, label_service, ocr_processor=None):
        """
        初始化视频服务

        Args:
            config: 配置管理器
            label_service: 标签服务
            ocr_processor: 可选的OCR处理器
        """
        self.config = config
        self.task_manager = TaskManager()
        self.event_manager = EventManager(config)
        self.file_service = FileServiceFactory.create_file_service(config)

        # 新增：最终气体追踪数据处理器
        from core.processors.gas_tracking_finalizer import GasTrackingFinalizer
        self.gas_tracking_finalizer = GasTrackingFinalizer()
        
        # 新增：增强数据分析器
        self.enhanced_data_analyzer = EnhancedDataAnalyzer(fps=30.0)
        self.enhanced_message_builder = EnhancedMessageBuilder()

        # 智能VideoProcessor初始化 - 支持气体泄漏模式（已移除detection_processor支持）
        try:
            if ocr_processor:
                # 使用传入的OCR处理器，支持气体泄漏模式参数
                self.video_processor = VideoProcessor(config, ocr_processor=ocr_processor, gas_leakage_mode=True)
                logger.info("VideoProcessor initialized with provided OCR processor")
            else:
                # 尝试使用配置初始化
                self.video_processor = VideoProcessor(config, gas_leakage_mode=True)
                logger.info("VideoProcessor initialized with config")
        except TypeError as e:
            # 如果都不工作，创建默认处理器
            logger.warning(f"VideoProcessor initialization failed: {e}")
            # detection_processor已删除，不再使用
            if not ocr_processor:
                from core.processors.ocr_processor import OCRProcessor
                ocr_processor = OCRProcessor()
            # 只使用OCR处理器初始化VideoProcessor
            self.video_processor = VideoProcessor(config, ocr_processor=ocr_processor, gas_leakage_mode=True)
            logger.info("VideoProcessor initialized with OCR processor only")

        # 智能ImageProcessor初始化
        try:
            self.image_processor = ImageProcessor(config)
        except Exception as e:
            logger.warning(f"ImageProcessor initialization failed: {e}")
            # 如果失败，设为None，在使用时再处理
            self.image_processor = None

        # OCR处理器初始化
        if ocr_processor:
            self.ocrProcessor = ocr_processor
        else:
            from core.processors.ocr_processor import OCRProcessor
            self.ocrProcessor = OCRProcessor()

        # 新增：智能OCR策略管理器
        self.smart_ocr_strategy = None # 将在每个视频处理时创建新实例

        # 移除独立的EnhancedGasLeakageTracker，整合到VideoProcessor

        self.label_service = label_service
        self.break_flags: Dict[str, bool] = {}
        # 创建线程池，线程数为CPU核心数的2倍
        self.thread_pool = ThreadPoolExecutor(max_workers=multiprocessing.cpu_count() * 2)
        # Subscribe to message_received events
        self.event_manager.subscribe('message_received', self.update_break_flags)
        self.start_consumer_thread()

    def update_break_flags(self, message: Dict[str, Any]) -> None:
        """更新任务中断标志"""
        task_id = message.get('task_id')
        flag = message.get('flag', False)
        self.break_flags[task_id] = flag
        logger.info(f"Updated break flag for task {task_id}: {flag}")
        # Publish the event
        self.event_manager.publish('break_flags_updated', message)

    def remove_break_flag(self, task_id: str) -> None:
        """移除任务中断标志"""
        flag = self.break_flags.pop(task_id, None)
        if flag is not None:
            logger.info(f"Break flag removed for task {task_id}")

    def check_break_flag(self, task_id: str) -> bool:
        """检查任务是否需要中断"""
        return self.break_flags.get(task_id, False)

    def _convert_numpy_to_json_serializable(self, obj):
        """
        递归转换numpy数组和其他不可JSON序列化的对象为可序列化格式
        
        Args:
            obj: 需要转换的对象
            
        Returns:
            转换后的可JSON序列化对象
        """
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._convert_numpy_to_json_serializable(item) for item in obj)
        else:
            return obj

    def enable_gas_leakage_mode(self, enable: bool = True) -> bool:
        """
        启用或禁用气体泄漏追踪模式

        Args:
            enable: 是否启用气体泄漏模式

        Returns:
            bool: 操作是否成功
        """
        try:
            if hasattr(self.video_processor, 'gas_leakage_mode'):
                # 设置模式
                self.video_processor.gas_leakage_mode = True

                # 气体泄漏模式的初始化在initialize_processing方法中完成
                # 这里只需要设置模式标志，实际的追踪器初始化会在视频处理开始时进行

                logger.info(f"气体泄漏模式已{'启用' if enable else '禁用'}")
                return True
            else:
                logger.error("VideoProcessor不支持气体泄漏模式")
                return False
        except Exception as e:
            logger.error(f"切换气体泄漏模式失败: {e}")
            return False

    def consumer_thread(self):
        """消费者线程 - 防栈溢出版"""
        logger.info("Starting MQ-Consumer thread...")
        retry_count = 0
        max_retries_before_reset = 10 # 最大重试次数，之后强制重置

        while True:
            try:
                # 栈深度检查
                import sys
                stack_depth = len([frame for frame in sys._current_frames().values()])
                if stack_depth > 800: # 比EventManager更早警告
                    logger.error(f"Stack depth too high in consumer_thread ({stack_depth}), forcing reset")
                    self._force_reset_event_manager()
                    time.sleep(10) # 更长的等待时间
                    retry_count = 0 # 重置重试计数
                    continue

                # 重试次数检查
                if retry_count >= max_retries_before_reset:
                    logger.warning(f"Consumer thread exceeded max retries ({max_retries_before_reset}), forcing event manager reset")
                    self._force_reset_event_manager()
                    retry_count = 0
                    time.sleep(15) # 强制等待更长时间

                # 尝试启动消费
                logger.info(f"MQ-Consumer: Attempting to start consuming from 'for_java' queue (attempt {retry_count + 1})")
                self.event_manager.start_consuming('for_java')

                # 如果成功到达这里，重置重试计数
                retry_count = 0

            except KeyboardInterrupt:
                logger.info("MQ-Consumer: Received interrupt signal, stopping...")
                break
            except Exception as e:
                retry_count += 1
                logger.error(f"MQ-Consumer: Error in consumer thread (attempt {retry_count}): {str(e)}", exc_info=True)

                # 检查是否是栈相关错误
                error_str = str(e).lower()
                if 'stack' in error_str or 'recursion' in error_str or 'overflow' in error_str:
                    logger.error("Stack-related error detected, forcing reset")
                    self._force_reset_event_manager()
                    retry_count = 0
                    time.sleep(20) # 栈错误后等待更久
                else:
                    # 等待时间随重试次数递增
                    wait_time = min(5 + retry_count * 2, 30) # 最多等待30秒
                    logger.info(f"MQ-Consumer: Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)

                continue

        logger.info("MQ-Consumer: Consumer thread stopped")

    def _force_reset_event_manager(self):
        """强制重置EventManager状态"""
        try:
            logger.warning(" Forcing EventManager reset to prevent stack overflow")

            # 调用EventManager的重置方法
            if hasattr(self.event_manager, '_reset_consumer_connection'):
                self.event_manager._reset_consumer_connection()

            # 强制垃圾回收
            import gc
            gc.collect()

            logger.info(" EventManager reset completed")

        except Exception as e:
            logger.error(f"Error during EventManager reset: {e}")

        # 无论如何都给系统一些时间恢复
        time.sleep(2)

    def start_consumer_thread(self) -> None:
        """启动消费者线程"""
        try:
            consumer_thread = threading.Thread(
                target=self.consumer_thread,
                daemon=True,
                name="VideoService-MQConsumer"
            )
            consumer_thread.start()
            logger.info("Consumer thread started successfully")
        except Exception as e:
            logger.error(f"Failed to start consumer thread: {str(e)}", exc_info=True)
            # 即使消费者线程启动失败，也不应该影响主服务

    def process_video_request(self, data: Dict[str, Any], request_uuid: str = None) -> Response:
        """处理视频请求
        
        Args:
            data: 视频处理请求数据
            request_uuid: 可选的请求UUID，用于日志追踪
        
        Returns:
            Response: 处理结果响应
        """
        try:
            # 获取请求UUID（优先使用传入的参数，然后从data中获取）
            if not request_uuid:
                request_uuid = data.get('request_uuid')
            if not request_uuid:
                # 如果没有传入UUID，尝试从当前线程的日志过滤器获取
                try:
                    from utils.logging_utils import get_current_uuid
                    request_uuid = get_current_uuid()
                except (ImportError, AttributeError):
                    request_uuid = str(uuid.uuid4())  # 生成新的UUID作为备用
            
            logger.info(f"开始处理视频请求 - 任务ID: {data.get('taskId')} - UUID: {request_uuid}")
            
            # 验证必要参数
            required_fields = ['videoPath', 'videoId', 'taskId', 'userId', 'originalName', 'currentVideoPathDir']
            if not all(field in data for field in required_fields):
                raise BadRequest('Missing required parameters')

            # 检查是否启用气体泄漏追踪模式
            # 首先从配置文件读取默认值，然后从请求数据中覆盖
            config_default = getattr(self.config, 'gas_leakage_mode', True) # 配置文件默认为True
            gas_leakage_mode = data.get('gasLeakageMode', data.get('gas_leakage_mode', config_default))
            if gas_leakage_mode:
                logger.info(f"启用气体泄漏追踪模式 - 任务ID: {data['taskId']}")
                success = self.enable_gas_leakage_mode(True)
                if not success:
                    logger.warning("气体泄漏模式启用失败，使用标准模式")
                    gas_leakage_mode = False
            else:
                # 确保关闭气体泄漏模式
                self.enable_gas_leakage_mode(False)

            # 从MinIO下载视频到本地缓存
            minio_path = data['videoPath']
            local_video_path = self.file_service.download_file(minio_path, data['taskId'])
            self.video_processor.validate_video_duration(local_video_path)

            # 准备输出路径
            combined_output_path = os.path.join(
                data['currentVideoPathDir'],
                f"{data['originalName']}-combined.mp4"
            )
            logger.info(f"local_video_path output path: {local_video_path}")
            logger.info(f"Combined output path: {combined_output_path}")
            # 检查是否启用视频输出
            enable_video_output = getattr(self.config, 'enable_video_output', True)
            logger.info(f"Video output enabled: {enable_video_output}")

            # 初始化视频处理资源
            resources = self.video_processor.initialize_processing(
                local_video_path,
                self.config.model_path,
                combined_output_path
            )

            if not resources:
                raise BadRequest('Failed to initialize video processing')

            model, cap, temp_frames_dir, total_frames, frame_width, frame_height, fps, duration_seconds, actual_output_path, writer_type = resources

            # 记录使用的编码器类型
            logger.info(f" Video encoder: {writer_type}")

            # 初始化任务
            self.task_manager.create_task(data['videoId'], data['taskId'], data['userId'], total_frames, fps)

            # 提交异步处理任务
            self.thread_pool.submit(
                self._async_process_video,
                model, cap, temp_frames_dir, data['taskId'], data['videoId'],
                data['userId'], data['currentVideoPathDir'], frame_width, frame_height,
                data.get('confidenceThreshold', 0.5), self.label_service,
                actual_output_path if enable_video_output else None, # 使用实际的输出路径
                combined_output_path if enable_video_output else None,
                fps, # 添加fps参数
                total_frames, # 添加total_frames参数
                gas_leakage_mode, # 添加气体泄漏模式标识
                request_uuid # 添加UUID参数
            )

            # 获取视频文件大小
            file_size_bytes = 0
            try:
                if os.path.exists(local_video_path):
                    file_size_bytes = os.path.getsize(local_video_path)
                    logger.info(f" Video file size: {file_size_bytes} bytes ({file_size_bytes / (1024*1024):.2f} MB)")
            except Exception as e:
                logger.warning(f"Failed to get video file size: {e}")

            return jsonify({
                'status': 'processing',
                'taskId': data['taskId'],
                'video_path': combined_output_path if enable_video_output else None,
                'video_info': {
                    'width': frame_width,
                    'height': frame_height,
                    'fps': float(fps),
                    'total_frames': total_frames,
                    'duration_seconds': float(duration_seconds),
                    'file_size_bytes': file_size_bytes
                }
            })

        except BadRequest as e:
            logger.warning(str(e))
            return jsonify({'status': 'error', 'message': str(e)}), 400
        except Exception as e:
            logger.error(f'Error processing video request: {str(e)}', exc_info=True)
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    def _process_video_frames(self, model, cap, temp_frames_dir, task_id, video_id,
                            user_id, output_dir, frame_width, frame_height, confidence_threshold,
                            label_service, gas_leakage_mode=False):
        """处理视频帧 - 重构版本"""

        # 初始化处理上下文
        context = self._init_video_metadata(
            cap, task_id, video_id, user_id, confidence_threshold,
            gas_leakage_mode, temp_frames_dir, output_dir
        )

        # 初始化OCR策略
        self._init_ocr_strategy()

        # 开始处理循环
        while cap.isOpened():
            if self.check_break_flag(context.task_id):
                logger.info('Break flag is set. Exiting video processing loop.')
                break

            try:
                # 读取下一帧
                frame = self._read_next_frame(cap, context)
                if frame is None:
                    break

                # 简化进度日志（移除独立的进度报告，统一使用MQ更新）
                if context.should_report_progress():
                    elapsed_time = context.get_elapsed_time()
                    progress_percentage = context.get_progress_percentage()
                    processing_fps = context.get_processing_fps()
                    logger.info(f" 进度 {context.frame_counter}/{context.total_frames} ({progress_percentage:.1f}%) - "
                               f"已用时: {elapsed_time:.1f}s, 速度: {processing_fps:.1f}FPS")

                # 处理检测帧
                frame_result = self._process_detection_frame(
                    frame, model, context, confidence_threshold, label_service
                )

                # 处理OCR
                extracted_info = self._handle_ocr_for_frame(frame, context)

                # 更新OCR数据
                context.update_ocr_data(extracted_info)

                # 无检测情况通过MQ统一处理，不需要单独的无检测消息

                # 合成并保存帧
                detection_image_path = self._combine_and_save_frame(
                    frame, frame_result, context
                )

                # 更新任务进度（MQ）
                if context.should_update_mq_progress():
                    self._update_task_progress(
                        context.task_id, context.frame_counter, frame_result, 0,
                        context.recog_date, context.recog_lat, context.recog_lon,
                        detection_image_path, confidence_threshold
                    )

                # 更新状态
                context.prev_frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                context.increment_frame()

            except MemoryError as e:
                self._handle_processing_error(e, context, 'Memory error')
                break
            except (cv2.error, IOError, Exception) as e:
                if not self._handle_frame_error(e, context):
                    break
                continue

        # 处理完成总结
        self._log_summary(context)

    # ========== 拆分的私有方法 ==========

    def _init_video_metadata(self, cap, task_id, video_id, user_id, confidence_threshold,
                           gas_leakage_mode, temp_frames_dir, output_dir):
        """初始化视频元数据和处理上下文"""
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration_seconds = total_frames / fps if fps > 0 else 0
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        context = VideoProcessingContext(
            task_id=task_id,
            video_id=video_id,
            user_id=user_id,
            confidence_threshold=confidence_threshold,
            gas_leakage_mode=gas_leakage_mode,
            total_frames=total_frames,
            fps=fps,
            duration_seconds=duration_seconds,
            frame_width=frame_width,
            frame_height=frame_height,
            temp_frames_dir=temp_frames_dir,
            output_dir=output_dir
        )

        logger.info(f" 开始处理视频 - 任务ID: {task_id}, 总帧数: {total_frames}, FPS: {fps:.2f}, 时长: {duration_seconds:.2f}秒")
        logger.info(f" Video info: {frame_width}x{frame_height}, {total_frames} frames, {fps:.2f} fps, {duration_seconds:.2f}s")
        logger.info(f" 进度报告配置 - 间隔: {context.progress_interval}帧, 总帧数: {total_frames}")

        return context

    def _init_ocr_strategy(self):
        """初始化智能OCR策略"""
        from core.processors.smart_ocr_strategy import create_smart_ocr_strategy
        self.smart_ocr_strategy = create_smart_ocr_strategy()



    def _read_next_frame(self, cap, context):
        """读取下一帧"""
        ret, frame = cap.read()
        if not ret:
            logger.info(f" 视频读取结束 - 已处理 {context.frame_counter} 帧")
            return None
        return frame

    # 移除 _report_progress 方法 - 统一使用 _update_task_progress 通知Java

    def _process_detection_frame(self, frame, model, context, confidence_threshold, label_service):
        """处理检测帧"""
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 统一使用VideoProcessor处理，整合气体模式
        frame_result = self.video_processor.process_frame(
                    frame, model, confidence_threshold, context.prev_frame_gray,
                    frame_gray, context.scale, None, context.fps,
                    context.task_id, label_service
                )

        return frame_result


    def _handle_ocr_for_frame(self, frame, context):
        """处理当前帧的OCR"""
        extracted_info = None

        # 使用智能策略判断是否执行OCR
        should_perform_ocr = self.smart_ocr_strategy.should_perform_ocr(context.frame_counter)

        if should_perform_ocr:
            # 执行OCR识别
            extracted_info = self.ocrProcessor.process_frame(frame)

            # 记录OCR结果到策略管理器
            self.smart_ocr_strategy.record_ocr_result(context.frame_counter, extracted_info)

            # 收集OCR数据到video_processor
            if hasattr(self.video_processor, 'collect_ocr_data'):
                self.video_processor.collect_ocr_data(context.frame_counter, extracted_info, context.fps)

            # 检查首次OCR成功并发送videoStart消息
            if extracted_info and any(extracted_info.values()) and not context.first_ocr_data:
                self._send_video_start_message(context, extracted_info)
                logger.info(f" 首次OCR成功 - 帧: {context.frame_counter}, GPS: [{extracted_info.get('Lat')}, {extracted_info.get('Lon')}]")
        else:
            # 不执行OCR的帧，只统计总数
            if hasattr(self.video_processor, 'collect_ocr_data'):
                self.video_processor.collect_ocr_data(context.frame_counter, None, context.fps)

        return extracted_info

    def _send_video_start_message(self, context, extracted_info):
        """发送视频开始消息"""
        message = {
            'messageType': 'videoStart',
            'videoId': context.video_id,
            'taskId': context.task_id,
            'userId': context.user_id,
            'recogDate': extracted_info['UTC'],
            'recogLat': extracted_info['Lat'],
            'recogLon': extracted_info['Lon']
        }
        self.event_manager.enqueue_send_message(context.task_id, message)

    def _send_video_end_message(self, context: VideoProcessingContext):
        """发送视频结束消息"""
        message = {
            'messageType': 'videoEnd',
            'videoId': context.video_id,
            'taskId': context.task_id,
            'userId': context.user_id,
            'videoPath': context.get_video_output_path(),
            'processingStatus': context.get_processing_summary(),
            'totalFrames': context.total_frames,
            'processedFrames': context.frame_counter
        }
        self.event_manager.enqueue_send_message(context.task_id, message)

    # 移除重复的 _handle_no_detection 方法 - 统一通过 _update_task_progress 处理无检测情况

    def _combine_and_save_frame(self, frame, frame_result, context):
        """合成并保存帧"""
        # 创建组合帧 - 添加安全检查
        try:
            combined_frame = self._create_safe_combined_frame(frame, frame_result, context.frame_height)
        except Exception as e:
            logger.error(f"创建组合帧失败: {e}")
            combined_frame = frame # 使用原始帧作为备用

        # 弹出窗口展示合成帧（临时可视化）
        try:
            cv2.imshow("Combined Frame Preview", combined_frame)
            cv2.waitKey(1) # 等待1毫秒，防止阻塞。设为 0 会暂停直到用户按键
        except Exception as e:
            logger.warning(f"展示 combined_frame 失败: {e}")
        # 保存检测图像用于上传
        detection_image_path = self._save_detection_image(frame, context)

        # 使用FFmpeg方案：保存帧到临时目录
        if context.temp_frames_dir:
            self._save_frame_to_temp(combined_frame, context)
        else:
            logger.debug("Temp frames directory not available, skipping frame save")

        return detection_image_path

    def _create_safe_combined_frame(self, frame, frame_result, frame_height):
        """安全地创建组合帧"""
        # 确保所有必要的键存在，并且是图像格式
        required_keys = ['optical_flow', 'confidence', 'detection']
        for key in required_keys:
            if key not in frame_result:
                logger.warning(f"frame_result缺少键: {key}, 使用默认值")
                frame_result[key] = frame
            elif not isinstance(frame_result[key], np.ndarray):
                logger.warning(f"frame_result[{key}]不是图像数组，使用默认值")
                frame_result[key] = frame

        return self.video_processor.create_combined_frame(
            frame, frame_result['optical_flow'], frame_result['confidence'],
            frame_result['detection'], frame_height
        )

    def _save_detection_image(self, frame, context):
        """保存检测图像"""
        overlay = np.zeros_like(frame)
        detection_result = cv2.addWeighted(frame, 0.8, overlay, 0.2, 0)

        # 确保输出目录存在
        if not os.path.exists(context.output_dir):
            os.makedirs(context.output_dir, exist_ok=True)

        detection_image_path = os.path.join(context.output_dir, f"detection_{uuid.uuid4()}.png")
        cv2.imwrite(detection_image_path, detection_result)

        return detection_image_path

    def _save_frame_to_temp(self, combined_frame, context):
        """保存帧到临时目录"""
        try:
            success = self.video_processor.save_frame_to_temp(combined_frame)
            if not success:
                logger.warning(f"Failed to save frame {context.frame_counter} to temp directory")
        except Exception as save_error:
            logger.warning(f"Error saving frame {context.frame_counter}: {save_error}")
            # 不要因为保存错误而停止整个处理过程

    def _handle_frame_error(self, error, context):
        """处理帧处理错误，返回True继续处理，False退出"""
        error_str = str(error).lower()

        # 特殊处理栈溢出和内存访问错误
        if any(keyword in error_str for keyword in ['stack', 'overflow', 'access violation', 'segmentation']):
            logger.error(f" CRITICAL: Potential stack overflow or memory access error detected!")
            logger.error(f" - Error: {error}")
            logger.error(f" - Frame: {context.frame_counter}")
            logger.error(f" - Stopping video processing to prevent crash")

            # 标记任务失败并退出
            self._handle_processing_error(error, context, 'Critical memory/stack error')
            return False

        error_type = 'OpenCV error' if isinstance(error, cv2.error) else \
                    'IO error' if isinstance(error, IOError) else 'Unexpected error'

        # 对于OpenCV错误，检查是否是尺寸相关
        if isinstance(error, cv2.error) and any(keyword in error_str for keyword in ['size', 'dimension', 'shape', 'contiguous']):
            logger.error(f" OpenCV size/dimension error - likely frame mismatch: {error}")
            # 跳过当前帧，但不退出整个处理
            return True

        self._handle_processing_error(error, context, error_type)
        return True # 继续处理

    def _log_summary(self, context):
        """输出处理完成总结"""
        total_elapsed_time = context.get_elapsed_time()
        final_progress = context.get_progress_percentage()
        avg_fps = context.get_processing_fps()

        logger.info(f" 视频处理完成总结 - 任务ID: {context.task_id}")
        logger.info(f" ├─ 处理帧数: {context.frame_counter}/{context.total_frames} ({final_progress:.1f}%)")
        logger.info(f" ├─ 总耗时: {total_elapsed_time:.1f}秒")
        logger.info(f" ├─ 平均处理速度: {avg_fps:.1f} FPS")
        logger.info(f" ├─ 原视频FPS: {context.fps:.1f}")
        if context.fps > 0:
            efficiency = (avg_fps / context.fps) * 100
            logger.info(f" └─ 处理效率: {efficiency:.1f}% (实时处理为100%)")
        else:
            logger.info(f" └─ 处理效率: N/A")

        # 发送处理完成消息到MQ（最终帧状态）
        final_result = {
            'max_confidence': 0.0,
            'max_velocity': 0.0,
            'detections': [],
            'labelInfo': self._get_default_label_info(),
            'processing_completed': True # 标记为处理完成
        }
        self._update_task_progress(
            context.task_id, context.frame_counter, final_result, 0,
            "", "", "", "", context.confidence_threshold
        )

        # 智能OCR策略执行报告
        self._log_ocr_strategy_report()

        # OCR数据收集总结
        self._log_ocr_data_summary()

    def _log_ocr_strategy_report(self):
        """输出OCR策略报告"""
        if not self.smart_ocr_strategy:
            return

        try:
            # 输出策略执行报告
            strategy_report = self.smart_ocr_strategy.get_summary_report()
            logger.info(strategy_report)

            # 输出简要状态
            status = self.smart_ocr_strategy.get_strategy_status()
            if status['ocr_permanently_disabled']:
                frames_saved = status['performance_optimization']['frames_saved_from_ocr']
                logger.warning(f" OCR已被智能策略禁用，节省了 {frames_saved} 帧的处理时间")
            else:
                success_rate = status['ocr_success_rate']
                logger.info(f" OCR策略正常运行，成功率: {success_rate:.1f}%")
        except Exception as e:
            logger.warning(f"获取智能OCR策略报告失败: {e}")

    def _log_ocr_data_summary(self):
        """输出OCR数据收集总结"""
        if not hasattr(self.video_processor, 'get_ocr_data_summary'):
            return

        try:
            ocr_summary = self.video_processor.get_ocr_data_summary()
            if ocr_summary:
                logger.info(f" OCR数据总结:")
                logger.info(f" ├─ 总尝试帧数: {ocr_summary.get('total_attempts', 0)}")
                logger.info(f" ├─ 成功提取帧数: {ocr_summary.get('successful_extractions', 0)}")
                logger.info(f" └─ 成功率: {ocr_summary.get('success_rate', 0):.1f}%")
        except Exception as e:
            logger.warning(f"获取OCR总结失败: {e}")

    def _handle_processing_error(self, error, context, error_type="processing"):
        """处理视频处理过程中的错误"""
        logger.error(f"Error processing video {context.video_id} at frame {context.frame_counter}: {str(error)}")
        self.task_manager.mark_task_failed(context.task_id, str(error))
        message = {
            'task_id': context.task_id,
            'video_id': context.video_id,
            'frame_counter': context.frame_counter,
            'error_type': error_type,
            'error': str(error),
            'userId': context.user_id
        }
        try:
            self.event_manager.enqueue_send_message(context.task_id, message)
        except Exception as e:
            # 使用warning而不是error，避免MQ问题导致服务退出
            logger.warning(f"Failed to send error message to queue (service continues normally): {str(e)}")
            # 不重新抛出异常，让主业务流程继续

    def _async_process_video(self, model, cap, temp_frames_dir, task_id, video_id,
                             user_id, output_dir, frame_width, frame_height, confidence_threshold,
                             label_service, local_output_path, minio_output_path, fps, total_frames, gas_leakage_mode=False, request_uuid=None):
        """异步处理视频的方法 - 重构版本"""

        # 在异步线程中设置UUID到日志系统
        if request_uuid:
            try:
                from utils.logging_utils import UUIDLogger
                UUIDLogger.set_uuid(request_uuid)
                logger.info(f"异步线程UUID设置成功: {request_uuid}")
            except Exception as e:
                logger.warning(f"异步线程UUID设置失败: {e}")

        # 初始化处理上下文
        context = self._init_async_processing_context(
            task_id, video_id, user_id, confidence_threshold, gas_leakage_mode,
            temp_frames_dir, output_dir, local_output_path, minio_output_path,
            frame_width, frame_height, fps, total_frames, request_uuid
        )

        logger.info(f" 开始异步视频处理流程 - 任务ID: {context.task_id}, 视频ID: {context.video_id}")

        try:
            # 步骤1: 执行逐帧处理
            self._execute_frame_processing(model, cap, context, confidence_threshold, label_service)
            
            # 设置frame_counter为total_frames，表示所有帧都已处理完成
            context.frame_counter = context.total_frames

            # 步骤2: 视频合成（根据环境选择合成方法）
            if context.should_synthesize_video():
                self._synthesize_video_by_environment(context)

            # 步骤3: 生成汇总数据
            self._generate_analysis_summaries(context)

            # 步骤4: 标记任务完成
            self.task_manager.mark_task_completed(context.task_id)

            # 步骤5: 发送视频结束消息
            self._send_video_end_message(context)

            # 步骤6: 构建并发送成功消息
            success_message = self._build_success_message(context)
            self.event_manager.enqueue_send_message(context.task_id, success_message)

            # 步骤6: 输出处理总结
            self._log_async_processing_summary(context)

        except Exception as e:
            # 处理异常
            self._handle_processing_exception(e, context)
        finally:
            # 清理UUID
            if request_uuid:
                try:
                    from utils.logging_utils import UUIDLogger
                    UUIDLogger.clear_uuid()
                    logger.info(f"异步线程UUID清理完成: {request_uuid}")
                except Exception as e:
                    logger.warning(f"异步线程UUID清理失败: {e}")
            
            # 清理资源
            self._cleanup_resources(model, cap, temp_frames_dir, context)

    def _init_async_processing_context(self, task_id, video_id, user_id, confidence_threshold,
                                     gas_leakage_mode, temp_frames_dir, output_dir,
                                     local_output_path, minio_output_path, frame_width, frame_height, fps, total_frames, request_uuid=None):
        """初始化异步处理上下文"""
        return AsyncProcessingContext(
            task_id=task_id,
            video_id=video_id,
            user_id=user_id,
            confidence_threshold=confidence_threshold,
            gas_leakage_mode=gas_leakage_mode,
            temp_frames_dir=temp_frames_dir,
            output_dir=output_dir,
            local_output_path=local_output_path,
            minio_output_path=minio_output_path,
            frame_width=frame_width,
            frame_height=frame_height,
            fps=fps,
            total_frames=total_frames,
            request_uuid=request_uuid
        )

    def _execute_frame_processing(self, model, cap, context, confidence_threshold, label_service):
        """执行逐帧处理"""
        logger.info(f" 开始逐帧处理阶段 - 任务ID: {context.task_id}, 气体泄漏模式: {context.gas_leakage_mode}")

        frame_processing_start = time.time()
        try:
            self._process_video_frames(
                model, cap, context.temp_frames_dir, context.task_id, context.video_id,
                context.user_id, context.output_dir, context.frame_width, context.frame_height,
                confidence_threshold, label_service, context.gas_leakage_mode
            )
            cap.release()

            context.frame_processing_time = time.time() - frame_processing_start
            logger.info(f" 逐帧处理完成 - 任务ID: {context.task_id}, 耗时: {context.frame_processing_time:.1f}秒")

        except Exception as e:
            context.frame_processing_time = time.time() - frame_processing_start
            logger.error(f" 逐帧处理失败 - 任务ID: {context.task_id}, 耗时: {context.frame_processing_time:.1f}秒, 错误: {e}")
            raise

    def _synthesize_video_by_environment(self, context):
        """根据环境选择视频合成方法"""
        if not context.should_synthesize_video():
            logger.info(f"⏭ 跳过视频合成 - 任务ID: {context.task_id} (条件不满足)")
            return

        # 获取当前环境
        environment = os.getenv('ENVIRONMENT', 'dev').lower()

        if environment == 'dev':
            logger.info(f" 开发环境 - 使用CV2合成视频 - 任务ID: {context.task_id}")
            self._synthesize_video_with_cv2(context)
        else:
            logger.info(f" 生产环境 - 使用FFmpeg合成视频 - 任务ID: {context.task_id}")
            self._synthesize_video_with_ffmpeg(context)

    def _synthesize_video_with_cv2(self, context):
        """使用CV2合成视频（开发环境）"""
        synthesis_start_time = time.time()
        logger.info(f" 开始CV2视频合成阶段 - 任务ID: {context.task_id}")

        try:
            import cv2
            import glob

            # 使用video_processor的temp_frames_dir，确保路径一致
            actual_temp_dir = self.video_processor.temp_frames_dir
            if not actual_temp_dir:
                logger.error(f" video_processor的temp_frames_dir未初始化 - 任务ID: {context.task_id}")
                context.synthesis_success = False
                return

            # 获取所有4宫格帧文件（这些是video_processor已经处理好的组合帧）
            frame_pattern = os.path.join(actual_temp_dir, "frame_*.png")
            frame_files = sorted(glob.glob(frame_pattern))
            
            if not frame_files:
                logger.error(f" 未找到4宫格帧文件 - 任务ID: {context.task_id}")
                logger.error(f" 检查路径: {frame_pattern}")
                logger.error(f" video_processor.temp_frames_dir: {actual_temp_dir}")
                logger.error(f" context.temp_frames_dir: {context.temp_frames_dir}")
                context.synthesis_success = False
                return

            # 读取第一个4宫格帧获取尺寸
            first_combined_frame = cv2.imread(frame_files[0])
            if first_combined_frame is None:
                logger.error(f" 无法读取第一个4宫格帧 - 任务ID: {context.task_id}")
                context.synthesis_success = False
                return

            # 4宫格帧的实际尺寸
            combined_height, combined_width = first_combined_frame.shape[:2]
            logger.info(f" 4宫格帧尺寸: {combined_width}x{combined_height}")

            # 设置视频编码器 - 使用4宫格帧的实际尺寸
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out_combined = cv2.VideoWriter(
                context.local_output_path,
                fourcc,
                context.fps,
                (combined_width, combined_height)  # 使用4宫格帧的实际尺寸
            )

            if not out_combined.isOpened():
                logger.error(f" 无法创建视频写入器 - 任务ID: {context.task_id}")
                context.synthesis_success = False
                return

            logger.info(f" 开始写入4宫格视频帧 - 总帧数: {len(frame_files)}")

            for i, frame_file in enumerate(frame_files):
                # 直接读取已经处理好的4宫格帧
                combined_frame = cv2.imread(frame_file)
                if combined_frame is None:
                    logger.warning(f" 跳过损坏的4宫格帧: {frame_file}")
                    continue

                # 直接写入4宫格帧，无需再次处理
                out_combined.write(combined_frame)

                if (i + 1) % 100 == 0:
                    logger.info(f" 已处理 {i + 1}/{len(frame_files)} 个4宫格帧")

            out_combined.release()

            context.synthesis_time = time.time() - synthesis_start_time
            context.synthesis_success = True

            logger.info(f" CV2视频合成成功 - 任务ID: {context.task_id}")
            logger.info(f" ├─ 输出文件: {context.local_output_path}")
            logger.info(f" ├─ 4宫格视频尺寸: {combined_width}x{combined_height}")
            logger.info(f" └─ 合成耗时: {context.synthesis_time:.1f}秒")

        except Exception as synthesis_error:
            context.synthesis_time = time.time() - synthesis_start_time
            logger.error(f" CV2视频合成过程出错 - 任务ID: {context.task_id}: {synthesis_error}")
            context.synthesis_success = False
            # 不重新抛出异常，视频合成失败不应影响整体流程

    # 注意：_create_combined_frame方法已移除
    # dev环境现在直接使用video_processor保存的4宫格帧，无需重新创建

    def _synthesize_video_with_ffmpeg(self, context):
        """使用FFmpeg合成视频（生产环境）"""
        synthesis_start_time = time.time()
        logger.info(f" 开始FFmpeg视频合成阶段 - 任务ID: {context.task_id}")

        try:
            context.synthesis_success = self.video_processor.synthesize_video_with_ffmpeg(
                context.local_output_path, context.fps
            )

            context.synthesis_time = time.time() - synthesis_start_time

            if context.synthesis_success:
                logger.info(f" FFmpeg视频合成成功 - 任务ID: {context.task_id}")
                logger.info(f" ├─ 输出文件: {context.local_output_path}")
                logger.info(f" └─ 合成耗时: {context.synthesis_time:.1f}秒")
            else:
                logger.warning(f" FFmpeg视频合成失败 - 任务ID: {context.task_id}, 耗时: {context.synthesis_time:.1f}秒")
                logger.info(f" 任务将标记为部分成功（逐帧处理完成，但合成失败）")

        except Exception as synthesis_error:
            context.synthesis_time = time.time() - synthesis_start_time
            logger.error(f" 视频合成过程出错 - 任务ID: {context.task_id}: {synthesis_error}")
            context.synthesis_success = False
            # 不重新抛出异常，视频合成失败不应影响整体流程

    def _generate_analysis_summaries(self, context):
        """生成分析汇总数据"""
        # 生成气体泄漏分析汇总
        if context.gas_leakage_mode:
            self._generate_gas_analysis_summary(context)

        # 生成时间戳GPS汇总
        self._generate_temporal_gps_summary(context)

    def _generate_gas_analysis_summary(self, context):
        """生成气体泄漏分析汇总"""
        if not hasattr(self.video_processor, 'get_gas_leakage_analysis_summary'):
            logger.warning(f" 气体泄漏分析功能不可用 - 任务ID: {context.task_id}")
            return

        try:
            # 新增：处理最终气体追踪数据
            final_gas_summary = self._process_final_gas_tracking_data(context)

            # 生成标准气体分析汇总
            context.gas_analysis_summary = self.video_processor.get_gas_leakage_analysis_summary()

            # 将最终气体追踪数据合并到汇总中
            if final_gas_summary and isinstance(context.gas_analysis_summary, dict):
                context.gas_analysis_summary['final_gas_tracking_summary'] = self.gas_tracking_finalizer.export_summary_to_dict(final_gas_summary)
                logger.info(f" 最终气体追踪数据已合并到汇总中 - 任务ID: {context.task_id}")

            logger.info(f" 生成气体泄漏分析汇总 - 任务ID: {context.task_id}")

            # 保存汇总文件
            if context.save_summary_files and context.gas_analysis_summary:
                self._save_gas_analysis_summary_file(context)

        except Exception as e:
            logger.error(f" 生成气体泄漏分析汇总失败 - 任务ID: {context.task_id}: {e}")
            context.gas_analysis_summary = {"status": "error", "error": str(e)}

    def _generate_temporal_gps_summary(self, context):
        """生成时间戳GPS汇总"""
        if not hasattr(self.video_processor, 'get_temporal_gps_summary'):
            logger.debug(f" 时间戳GPS汇总功能不可用 - 任务ID: {context.task_id}")
            return

        try:
            context.temporal_gps_summary = self.video_processor.get_temporal_gps_summary()
            if context.temporal_gps_summary:
                logger.info(f" 生成时间戳GPS汇总 - 任务ID: {context.task_id}")

                # 保存汇总文件
                if context.save_summary_files:
                    self._save_temporal_gps_summary_file(context)
            else:
                logger.info(f" 无GPS数据生成汇总 - 任务ID: {context.task_id}")

        except Exception as e:
            logger.error(f" 生成时间戳GPS汇总失败 - 任务ID: {context.task_id}: {e}")
            context.temporal_gps_summary = {"status": "error", "error": str(e)}

    def _save_gas_analysis_summary_file(self, context):
        """保存气体分析汇总文件"""
        import json
        try:
            summary_filename = f"gas_analysis_summary_{context.task_id}.json"
            summary_path = os.path.join(context.output_dir, summary_filename)
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(context.gas_analysis_summary, f, indent=2, ensure_ascii=False)
            logger.info(f" 气体泄漏分析汇总已保存: {summary_path}")
        except Exception as e:
            logger.warning(f" 保存气体分析汇总文件失败: {e}")

    def _save_temporal_gps_summary_file(self, context):
        """保存时间戳GPS汇总文件"""
        import json
        try:
            temporal_filename = f"temporal_gps_summary_{context.task_id}.json"
            temporal_path = os.path.join(context.output_dir, temporal_filename)
            with open(temporal_path, 'w', encoding='utf-8') as f:
                json.dump(context.temporal_gps_summary, f, indent=2, ensure_ascii=False)
            logger.info(f" 时间戳GPS汇总已保存: {temporal_path}")
        except Exception as e:
            logger.warning(f" 保存GPS汇总文件失败: {e}")

    def _process_final_gas_tracking_data(self, context):
        """
        处理最终气体追踪数据

        Args:
            context: 视频处理上下文

        Returns:
            最终气体追踪汇总结果
        """
        try:
            if not hasattr(self.video_processor, 'gas_tracking_results'):
                logger.debug(f" 没有找到气体追踪结果数据 - 任务ID: {context.task_id}")
                return None

            gas_tracking_results = self.video_processor.gas_tracking_results
            if not gas_tracking_results:
                logger.debug(f" 气体追踪结果为空 - 任务ID: {context.task_id}")
                return None

            # 构建视频信息
            video_info = {
                "fps": context.fps,
                "width": getattr(context, 'frame_width', 1920),
                "height": getattr(context, 'frame_height', 1080),
                "total_frames": context.total_frames,
                "duration_seconds": context.total_frames / context.fps if context.fps > 0 else 0,
                "task_id": context.task_id
            }

            logger.info(f" 开始处理最终气体追踪数据 - 任务ID: {context.task_id}")
            logger.info(f" 总帧数: {len(gas_tracking_results)}")
            logger.info(f" 视频时长: {video_info['duration_seconds']:.2f}秒")

            # 使用GasTrackingFinalizer处理数据
            final_summary = self.gas_tracking_finalizer.process_final_gas_tracking_data(
                gas_tracking_results,
                video_info
            )

            if final_summary.processing_info.get('status') == 'success':
                logger.info(f" 最终气体追踪数据处理成功 - 任务ID: {context.task_id}")
                logger.info(f" 检测到泄漏点: {len(final_summary.final_leakage_points)}")
                logger.info(f" 整体风险等级: {final_summary.risk_assessment.get('overall_risk_level', 'unknown')}")

                # 保存处理结果到文件（可选）
                try:
                    self._save_final_gas_tracking_files(context, final_summary)
                except Exception as save_error:
                    logger.warning(f" 保存最终气体追踪数据文件失败: {save_error}")

                return final_summary
            else:
                logger.warning(f" 最终气体追踪数据处理失败 - 任务ID: {context.task_id}")
                return None

        except Exception as e:
            logger.error(f" 处理最终气体追踪数据失败 - 任务ID: {context.task_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _save_final_gas_tracking_files(self, context, final_summary):
        """保存最终气体追踪数据文件"""
        try:
            import time
            from pathlib import Path

            # 创建专用目录
            final_tracking_dir = os.path.join(context.output_dir, "final_gas_tracking")
            Path(final_tracking_dir).mkdir(parents=True, exist_ok=True)

            timestamp = int(time.time())

            # 保存JSON汇总文件
            json_filename = f"final_gas_summary_{context.task_id}_{timestamp}.json"
            json_path = os.path.join(final_tracking_dir, json_filename)
            self.gas_tracking_finalizer.export_to_json(final_summary, json_path)

            # 保存CSV泄漏点文件
            csv_filename = f"final_leakage_points_{context.task_id}_{timestamp}.csv"
            csv_path = os.path.join(final_tracking_dir, csv_filename)
            self.gas_tracking_finalizer.export_to_csv(final_summary, csv_path)

            logger.info(f" 最终气体追踪数据文件已保存 - 任务ID: {context.task_id}")
            logger.info(f" JSON文件: {json_path}")
            logger.info(f" CSV文件: {csv_path}")

        except Exception as e:
            logger.error(f" 保存最终气体追踪数据文件失败: {e}")
            raise

    def _build_success_message(self, context):
        """构建成功消息"""
        message = {
            'messageType': 'notifySuccess',
            'videoId': context.video_id,
            'taskId': context.task_id,
            'userId': context.user_id,
            'videoPath': context.get_video_output_path(),
            'gasLeakageMode': context.gas_leakage_mode,
            'processingStatus': context.get_processing_summary(),
            'analysisStatus': 'success' # 修复：明确设置分析状态为成功
        }

        # 添加气体分析数据
        if context.has_gas_analysis():
            self._add_gas_analysis_to_message(message, context)

        # 添加时间戳GPS数据
        if context.has_temporal_gps_data():
            self._add_temporal_gps_to_message(message, context)
        
        # 添加增强分析数据
        self._add_enhanced_analysis_to_message(message, context)

        return message

    def _add_gas_analysis_to_message(self, message, context):
        """添加气体分析数据到消息"""
        gas_summary = context.gas_analysis_summary
        message['gasAnalysisSummary'] = gas_summary

        # 提取最终追踪泄漏点数据
        if 'final_tracked_leakage_points' in gas_summary:
            tracked_points = gas_summary['final_tracked_leakage_points']
            message['finalTrackedLeakagePoints'] = tracked_points
            logger.info(f" 发送最终追踪泄漏点数据: {len(tracked_points)} 个泄漏点")

        # 提取关键统计信息
        summary_stats = gas_summary.get('summary_statistics', {})
        if summary_stats:
            message['gasTrackingSummary'] = {
                'uniqueLeakageSources': summary_stats.get('unique_leakage_sources', 0),
                'totalTrackingDuration': summary_stats.get('total_tracking_duration', 0),
                'maxSimultaneousLeaks': summary_stats.get('max_simultaneous_leakages', 0),
                'riskLevel': summary_stats.get('overall_risk_level', 'unknown'),
                'cameraStabilityAssessment': summary_stats.get('camera_stability_assessment', 'unknown')
            }

    def _add_temporal_gps_to_message(self, message, context):
        """添加时间戳GPS数据到消息"""
        gps_summary = context.temporal_gps_summary
        message['temporalGpsSummary'] = gps_summary

        # 提取关键GPS信息到消息顶层（便于Java端快速访问）
        if gps_summary.get('gps_analysis'):
            gps_analysis = gps_summary['gps_analysis']
            message['videoGpsInfo'] = {
                'startPosition': gps_analysis.get('start_position'),
                'endPosition': gps_analysis.get('end_position'),
                'centerPosition': gps_analysis.get('center'),
                'bounds': gps_analysis.get('bounds')
            }

        # 提取时间信息
        if gps_summary.get('temporal_analysis'):
            temporal_analysis = gps_summary['temporal_analysis']
            message['videoTimeInfo'] = {
                'startTime': temporal_analysis.get('start_time'),
                'endTime': temporal_analysis.get('end_time'),
                'startFrame': temporal_analysis.get('start_frame'),
                'endFrame': temporal_analysis.get('end_frame')
            }

        # 水印质量信息
        if gps_summary.get('watermark_analysis'):
            watermark = gps_summary['watermark_analysis']
            message['watermarkQuality'] = {
                'detectionRate': watermark.get('detection_rate', 0),
                'qualityAssessment': watermark.get('quality_assessment', 'unknown')
            }

    def _build_tracking_data_from_point(self, point):
        """从point对象的历史数据构建tracking_data列表"""
        tracking_data = []
        
        # 获取历史数据
        positions_history = getattr(point, 'positions_history', [])
        areas_history = getattr(point, 'areas_history', [])
        confidence_history = getattr(point, 'confidence_history', [])
        frame_numbers = getattr(point, 'frame_numbers', [])
        
        # 如果没有历史数据，使用当前数据创建单个数据点
        if not positions_history:
            bbox = getattr(point, 'bbox', [0, 0, 100, 100])
            center = getattr(point, 'average_position', [50, 50])
            area = getattr(point, 'average_area', 2500)
            confidence = getattr(point, 'average_confidence', 0.5)
            frame_number = getattr(point, 'first_detected_frame', 0)
            
            tracking_data.append({
                'bbox': bbox,
                'center': center,
                'area': area,
                'confidence': confidence,
                'frame_number': frame_number,
                'track_id': getattr(point, 'track_id', 0),
                'class_name': getattr(point, 'class_name', 'gas_leak')
            })
            return tracking_data
        
        # 使用历史数据构建多个数据点
        max_len = max(len(positions_history), len(areas_history), len(confidence_history))
        
        for i in range(max_len):
            # 获取当前索引的数据，如果超出范围则使用最后一个值
            position = positions_history[min(i, len(positions_history) - 1)] if positions_history else [50, 50]
            area = areas_history[min(i, len(areas_history) - 1)] if areas_history else 2500
            confidence = confidence_history[min(i, len(confidence_history) - 1)] if confidence_history else 0.5
            frame_number = frame_numbers[min(i, len(frame_numbers) - 1)] if frame_numbers else i
            
            # 从位置和面积估算bbox
            center_x, center_y = position
            bbox_width = bbox_height = max(10, int(area ** 0.5))  # 假设正方形
            bbox = [
                center_x - bbox_width // 2,
                center_y - bbox_height // 2,
                center_x + bbox_width // 2,
                center_y + bbox_height // 2
            ]
            
            tracking_data.append({
                'bbox': bbox,
                'center': position,
                'area': area,
                'confidence': confidence,
                'frame_number': frame_number,
                'track_id': getattr(point, 'track_id', 0),
                'class_name': getattr(point, 'class_name', 'gas_leak')
            })
        
        return tracking_data
    
    def _add_enhanced_analysis_to_message(self, message, context):
        """添加增强分析数据到消息中"""
        try:
            # 检查是否有气体分析数据
            if not context.has_gas_analysis():
                return
            
            gas_analysis = context.gas_analysis_summary
            final_leakage_points = gas_analysis.get('final_tracked_leakage_points', [])
            
            if not final_leakage_points:
                return
            
            # 使用增强数据分析器分析每个泄漏点
            enhanced_leakage_points = []
            video_leakage_stats = {
                'totalConfirmedLeaks': 0,
                'highRiskLeaks': 0,
                'averageConfidence': 0.0,
                'totalDuration': 0.0,
                'severityDistribution': {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            }
            
            total_confidence = 0.0
            total_duration = 0.0
            
            for point in final_leakage_points:
                # 分析单个泄漏点
                track_id = getattr(point, 'track_id', 0)
                # 构建 tracking_data，使用历史数据构建多个数据点
                tracking_data = self._build_tracking_data_from_point(point)
                enhanced_point = self.enhanced_data_analyzer.analyze_leakage_point(track_id, tracking_data)
                enhanced_leakage_points.append(enhanced_point)
                
                # 统计视频级别数据
                if enhanced_point.is_confirmed_leak:
                    video_leakage_stats['totalConfirmedLeaks'] += 1
                
                risk_level = enhanced_point.risk_assessment.get('level', 'low')
                if risk_level in ['high', 'critical']:
                    video_leakage_stats['highRiskLeaks'] += 1
                
                confidence = enhanced_point.confidence_interval.average_confidence
                total_confidence += confidence
                
                duration = enhanced_point.total_duration_seconds
                total_duration += duration
                
                # 根据severity_score数值确定严重程度级别
                score = enhanced_point.severity_score
                if score >= 0.8:
                    severity = 'critical'
                elif score >= 0.6:
                    severity = 'high'
                elif score >= 0.4:
                    severity = 'medium'
                elif score >= 0.2:
                    severity = 'low'
                else:
                    severity = 'none'
                if severity in video_leakage_stats['severityDistribution']:
                    video_leakage_stats['severityDistribution'][severity] += 1
            
            # 计算平均值
            if len(enhanced_leakage_points) > 0:
                video_leakage_stats['averageConfidence'] = total_confidence / len(enhanced_leakage_points)
                video_leakage_stats['totalDuration'] = total_duration
            
            # 使用增强消息构建器构建增强数据
            enhanced_data = self.enhanced_message_builder.build_enhanced_message(
                enhanced_leakage_points,
                video_leakage_stats,
                context
            )
            
            # 添加增强数据到消息中
            message.update(enhanced_data)
            
        except Exception as e:
            logger.error(f"添加增强分析数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            logger.warning(f"添加增强分析数据失败: {e}")
            # 不影响主流程，只记录警告

    def _log_async_processing_summary(self, context):
        """输出异步处理总结"""
        total_time = context.get_total_elapsed_time()
        summary = context.get_processing_summary()

        logger.info(f" 异步视频处理流程完成 - 任务ID: {context.task_id}")
        logger.info(f" ├─ 逐帧处理: {context.frame_processing_time:.1f}秒")
        if context.should_synthesize_video():
            logger.info(f" ├─ 视频合成: {context.synthesis_time:.1f}秒 ({'成功' if context.synthesis_success else '失败'})")
        logger.info(f" ├─ 汇总生成: ")
        logger.info(f" ├─ 总体耗时: {total_time:.1f}秒")

        # 状态判断
        if context.synthesis_success:
            logger.info(f" └─ 任务状态: 完全成功")
        elif context.is_partial_success():
            logger.info(f" └─ 任务状态: 部分成功（逐帧处理完成，合成失败）")
        else:
            logger.info(f" └─ 任务状态: 处理失败")

        # 数据汇总状态
        if context.has_gas_analysis():
            gas_stats = context.gas_analysis_summary.get('summary_statistics', {})
            sources = gas_stats.get('unique_leakage_sources', 0)
            risk = gas_stats.get('overall_risk_level', 'unknown')
            logger.info(f" 气体分析: {sources} 个泄漏源, 风险等级: {risk}")

        if context.has_temporal_gps_data():
            gps_stats = context.temporal_gps_summary.get('statistics', {})
            gps_frames = gps_stats.get('frames_with_gps', 0)
            logger.info(f" GPS数据: {gps_frames} 帧包含GPS信息")

    def _handle_processing_exception(self, error, context):
        """处理异步处理过程中的异常"""
        logger.error(f' 异步视频处理失败 - 任务ID: {context.task_id}: {str(error)}', exc_info=True)

        # 标记任务失败
        self.task_manager.mark_task_failed(context.task_id, str(error))

        # 构建并发送失败消息
        failure_message = {
            'messageType': 'notifyFailure',
            'videoId': context.video_id,
            'taskId': context.task_id,
            'error': str(error),
            'userId': context.user_id,
            'processingStatus': context.get_processing_summary(),
            'analysisStatus': 'failed', # 修复：明确设置分析状态为失败
            'partialResults': {
                'frameProcessingCompleted': context.frame_processing_time > 0,
                'videoSynthesisCompleted': context.synthesis_success,
                'gasAnalysisCompleted': context.has_gas_analysis(),
                'gpsAnalysisCompleted': context.has_temporal_gps_data()
            }
        }

        try:
            self.event_manager.enqueue_send_message(context.task_id, failure_message)
        except Exception as mq_error:
            logger.warning(f" 发送失败消息到MQ失败: {mq_error}")

    def _cleanup_resources(self, model, cap, temp_frames_dir, context):
        """清理处理资源"""
        logger.debug(f" 开始清理资源 - 任务ID: {context.task_id}")

        try:
            # 释放视频捕获对象
            if cap:
                cap.release()
                logger.debug(f" 视频捕获对象已释放")

            # 移除中断标志
            self.remove_break_flag(context.task_id)

            # 清理视频处理器资源
            if hasattr(self.video_processor, 'cleanup_resources'):
                self.video_processor.cleanup_resources(model, cap, temp_frames_dir)
                logger.debug(f" 视频处理器资源已清理")

            # 清理文件服务缓存
            if hasattr(self.file_service, 'cleanup_cache'):
                self.file_service.cleanup_cache(context.task_id)
                logger.debug(f" 文件服务缓存已清理")

            # 移除任务管理器中的任务
            if hasattr(self.task_manager, 'remove_task'):
                self.task_manager.remove_task(context.task_id)
                logger.debug(f" 任务管理器任务已移除")

        except Exception as cleanup_error:
            logger.warning(f" 资源清理过程中出现警告 - 任务ID: {context.task_id}: {cleanup_error}")

        logger.debug(f" 资源清理完成 - 任务ID: {context.task_id}")

    # 移除旧版本的 _handle_no_detection 方法 - 已统一到 _update_task_progress 中处理

    def _update_task_progress(self, task_id, frame_counter, frame_result, total_leakage, recogDate, recogLat, recogLon,detection_image_path,confidence_threshold):
        """更新任务进度 - 统一的进度通知方法"""
        try:
            progress = self.task_manager.get_task_progress(task_id)
            if progress:
                total_frames = progress.get('totalFrames', 0)
                if total_frames > 0:
                    # 第一帧时输出开始日志
                    if frame_counter == 0 or frame_counter == 1:
                        logger.info(f" 视频处理开始 - 任务ID: {task_id}, 总帧数: {total_frames}")
                        logger.info(f" 开始发送进度更新到MQ...")
                    # 计算处理时间
                    start_time = progress.get('startTime', time.time())
                    current_time = time.time()
                    elapsed_seconds = current_time - start_time
                    progress_data = {
                        'frame_index': int(frame_counter),
                        'threshold_value': float(frame_result.get('max_confidence', 0.0)),
                        'cumulative_leakage': 0.0, # 已移除泄漏量计算
                        'velocity': float(frame_result.get('max_velocity', 0.0)),
                        'progress': float(frame_counter/total_frames),
                        'recog_date': recogDate,
                        'recog_lat': recogLat,
                        'recog_lon': recogLon
                    }
                    self.task_manager.update_task_progress(task_id, **progress_data)

                    # 优化：保留进度显示，简化追踪数据
                    # 提取当前帧的简化追踪信息（只用于前端进度显示）
                    current_frame_tracking_summary = {}
                    if 'leakage_summary' in frame_result and frame_result['leakage_summary']:
                        leakage_summary = frame_result['leakage_summary']
                        current_frame_tracking_summary = {
                            'active_leak_count': leakage_summary.get('active_leak_count', 0),
                            'total_unique_tracks': leakage_summary.get('total_unique_tracks', 0),
                            'current_risk_level': leakage_summary.get('current_risk_level', 'low')
                        }

                    # 如果是气体泄漏模式，添加简化的追踪统计和完整分析数据
                    gas_analysis_summary = None
                    final_tracked_leakage_points = None
                    gas_tracking_summary = None
                    temporal_gps_summary = None

                    if frame_result.get('gas_mode') and frame_result.get('gas_tracking_results'):
                        gas_result = frame_result['gas_tracking_results']
                        # 安全访问gas_result，防止None错误
                        if gas_result is not None:
                            current_frame_tracking_summary.update({
                                'gas_detections_count': len(gas_result.get('detections', [])),
                                'active_tracks_count': len(gas_result.get('tracked_objects', [])),
                                'camera_motion_confidence': gas_result.get('camera_motion', {}).get('confidence', 0) if isinstance(gas_result.get('camera_motion'), dict) else 0
                            })

                        # 获取完整的气体分析汇总（如果有足够的数据）
                        try:
                            if hasattr(self.video_processor, 'get_gas_leakage_analysis_summary') and frame_counter > 10:
                                gas_analysis_summary = self.video_processor.get_gas_leakage_analysis_summary()
                                if gas_analysis_summary and gas_analysis_summary.get('status') == 'success':
                                    final_tracked_leakage_points = gas_analysis_summary.get('final_tracked_leakage_points', [])
                                    gas_tracking_summary = gas_analysis_summary.get('summary_statistics', {})

                            # 获取时间戳GPS汇总
                            if hasattr(self.video_processor, 'get_temporal_gps_summary'):
                                temporal_gps_summary = self.video_processor.get_temporal_gps_summary()
                        except Exception as e:
                            logger.debug(f"获取气体分析汇总失败: {e}")

                    # 检查消息类型
                    is_completion_message = frame_result.get('processing_completed', False)
                    has_detection = frame_result.get('has_detection', False)

                    # Prepare message for queue with camelCase keys - 统一处理有检测和无检测帧
                    queue_message = {
                        # ========== 基础消息字段 ==========
                        'messageType': 'completed' if is_completion_message else 'message',
                        'hasDetection': has_detection, # 明确标识是否有检测
                        'frameIndex': progress_data['frame_index'],
                        'userId': progress['userId'],
                        'videoId': progress['videoId'],
                        'taskId': progress['taskId'],
                        'videoName': progress.get('videoName', progress.get('filename', '')),

                        # ========== 进度相关字段 ==========
                        'progress': progress_data['progress'],
                        'seconds': float(elapsed_seconds),
                        'videoInfos': json.dumps({
                            'total_frames': total_frames,
                            'current_frame': frame_counter,
                            'fps': progress.get('fps', 30),
                            'duration': total_frames / progress.get('fps', 30) if progress.get('fps', 30) > 0 else 0
                        }),
                        'picUrl': detection_image_path,
                        'snapUrl': detection_image_path, # 使用相同的图片URL

                        # ========== 检测参数 ==========
                        'maxConf': float(confidence_threshold),
                        'thresholdValue': progress_data['threshold_value'],
                        'velocity': progress_data['velocity'],

                        # ========== 位置和时间信息 ==========
                        'recogDate': progress_data['recog_date'],
                        'recogLat': progress_data['recog_lat'],
                        'recogLon': progress_data['recog_lon'],

                        # ========== 标签信息 ==========
                        'labelInfoResult': json.dumps(self._convert_numpy_types(frame_result.get('labelInfo', self._get_default_label_info()))),

                        # ========== 视频路径 ==========
                        'videoPath': progress.get('videoPath', ''),

                        # ========== 气体泄漏分析核心字段 ==========
                        'gasLeakageMode': frame_result.get('gas_mode', False),
                        'gasLeakageTrackingData': json.dumps(self._convert_numpy_to_json_serializable(frame_result.get('gas_tracking_results', {}))) if frame_result.get('gas_tracking_results') else None,
                        'gasAnalysisSummary': gas_analysis_summary if gas_analysis_summary else None,
                        'finalTrackedLeakagePoints': final_tracked_leakage_points if final_tracked_leakage_points else None,
                        'gasTrackingSummary': gas_tracking_summary if gas_tracking_summary else None,
                        'temporalGpsSummary': temporal_gps_summary if temporal_gps_summary else None,

                        # ========== 关键统计指标 ==========
                        'analysisStatus': gas_analysis_summary.get('status', 'processing') if gas_analysis_summary else 'processing',
                        'totalFramesAnalyzed': gas_analysis_summary.get('total_frames_analyzed', frame_counter) if gas_analysis_summary else frame_counter,
                        'uniqueLeakageSources': gas_analysis_summary.get('unique_leakage_sources', 0) if gas_analysis_summary else 0,
                        'maxSimultaneousLeakages': gas_analysis_summary.get('max_simultaneous_leakages', 0) if gas_analysis_summary else 0,
                        'leakageFrequency': gas_analysis_summary.get('leakage_frequency', 0.0) if gas_analysis_summary else 0.0,
                        'totalLeakageEvents': gas_analysis_summary.get('total_leakage_events', 0) if gas_analysis_summary else 0,
                        'overallRiskLevel': gas_tracking_summary.get('overall_risk_level', 'low') if gas_tracking_summary else 'low',
                        'cameraStabilityAssessment': gas_analysis_summary.get('camera_motion_statistics', {}).get('stability_assessment', 'unknown') if gas_analysis_summary else 'unknown',
                        'confirmedStableLeaks': gas_tracking_summary.get('confirmed_stable_leaks', 0) if gas_tracking_summary else 0,

                        # ========== 实时进度字段 ==========
                        'cameraMotionData': json.dumps(self._convert_numpy_to_json_serializable(frame_result.get('gas_tracking_results', {}).get('camera_motion', {}))) if frame_result.get('gas_tracking_results') else None,
                        'gasDispersionAnalysis': json.dumps(self._convert_numpy_to_json_serializable(frame_result.get('gas_tracking_results', {}).get('gas_tracking_results', {}))) if frame_result.get('gas_tracking_results') else None,
                        'trackingStatistics': json.dumps(self._convert_numpy_to_json_serializable({
                            'tracked_objects_count': len(frame_result.get('gas_tracking_results', {}).get('tracked_objects', [])),
                            'detections_count': len(frame_result.get('gas_tracking_results', {}).get('detections', [])),
                            'frame_processing_time': frame_result.get('processing_time', 0)
                        })) if frame_result.get('gas_tracking_results') else None,
                        'activeLeakageCount': frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('active_leakage_count', 0) if frame_result.get('gas_tracking_results') else 0,
                        'totalLeakageArea': frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('total_leakage_area', 0.0) if frame_result.get('gas_tracking_results') else 0.0,
                        'riskLevel': frame_result.get('gas_tracking_results', {}).get('gas_dispersion_analysis', {}).get('risk_level', 'low') if frame_result.get('gas_tracking_results') else 'low',
                        'cameraCompensationEnabled': frame_result.get('gas_tracking_results', {}).get('camera_motion', {}).get('confidence', 0) > 0.3 if frame_result.get('gas_tracking_results') else False,
                        'cameraMotionConfidence': frame_result.get('gas_tracking_results', {}).get('camera_motion', {}).get('confidence', 0.0) if frame_result.get('gas_tracking_results') else 0.0,
                        'currentTrackingSummary': json.dumps(self._convert_numpy_to_json_serializable(current_frame_tracking_summary)) if current_frame_tracking_summary else None,

                        # ========== 算法信息字段 ==========
                        'algorithmInfo': json.dumps({
                            'gas_tracker_enabled': frame_result.get('gas_mode', False),
                            'detection_model': frame_result.get('model_info', {}).get('model_name', 'yolov8'),
                            'tracker_type': 'enhanced_gas_leakage_tracker' if frame_result.get('gas_mode') else 'standard',
                            'frame_processing_method': 'sam_segmentation' if frame_result.get('gas_mode') else 'bounding_box'
                        })
                        
                        # 注意：以下字段已移除，因为缺乏实际数据来源或计算逻辑：
                        # - motionPatternDistribution: 只在gas_analysis_summary中有数据，但大部分情况下为空
                        # - shapeFeatures: frame_result.detections中缺少shape_analysis字段
                        # - irregularShapeDetected: 同上，shape_analysis字段不存在
                        # - mainDispersionDirection: gas_tracking_result拼写错误且字段不存在
                        # - byteTrackStatus: tracker_status字段在代码中未找到生成逻辑
                        # - videoGpsInfo/videoTimeInfo/watermarkQuality: 已通过temporalGpsSummary提供完整数据
                    }
                    # 使用线程安全的事件管理器发送消息，自带重试和错误处理
                    def progress_callback(success: bool, error_msg: str):
                        if not success:
                            logger.warning(f"Failed to send progress message for task {task_id}, frame {progress_data['frame_index']}: {error_msg}")

                    self.event_manager.enqueue_send_message(task_id, queue_message, progress_callback)
        except Exception as e:
            logger.error(f"Failed to update task progress: {str(e)}", exc_info=True)
            # 尝试发送失败消息
            try:
                fail_success = self.event_manager.send_fail_message_to_queue(
                    task_id,
                    f"Progress update failed: {str(e)}",
                    f"Frame {progress_data.get('frame_index', 'unknown')}"
                )
                if not fail_success:
                    logger.critical(f"Could not send fail message for task {task_id}")
            except Exception as fail_e:
                logger.critical(f"Exception while sending fail message: {fail_e}")



    def _get_default_label_info(self) -> Dict[str, Any]:
        """获取默认的标签信息结构，兼容Java端接口"""
        return {
            "level": "gas_detection", # 标签层级名称
            "color": [255, 165, 0], # 默认橙色 RGB
            "alert": False, # 默认不触发告警
            "priority": "medium", # 通知优先级
            "threshold": 0.5, # 告警阈值
            "confidence": 0.0 # 默认置信度
        }

    def _convert_numpy_types(self, obj):
        """Convert numpy types to native Python types for JSON serialization"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_numpy_types(item) for item in obj]
        return obj

    def process_image_request(self, data: Dict[str, Any]) -> Response:
        """处理图片请求"""
        try:
            if not data or 'image_path' not in data or 'model_path' not in data:
                raise BadRequest('Missing required parameters')

            # 从MinIO下载图片
            local_image_path = self.file_service.download_file(data['image_path'], str(uuid.uuid4()))

            try:
                # 处理图片
                detection_result, error = self.image_processor.process_image(
                    local_image_path,
                    data['model_path'],
                    float(data.get('confidence_threshold', 0.5))
                )

                if error:
                    raise BadRequest(error)

                # 生成输出文件名并保存
                output_filename = f"preview_{uuid.uuid4()}.png"
                output_path = os.path.join(os.path.dirname(local_image_path), output_filename)
                cv2.imwrite(output_path, detection_result)

                # 上传到MinIO
                minio_output_path = os.path.join(os.path.dirname(data['image_path']), output_filename)
                self.file_service.upload_file(output_path, minio_output_path)

                return jsonify({
                    'status': 'success',
                    'output_path': minio_output_path
                })

            finally:
                # 清理本地临时文件
                self.file_service.cleanup_cache(os.path.basename(local_image_path))

        except BadRequest as e:
            logger.warning(str(e))
            return jsonify({'status': 'error', 'message': str(e)}), 400
        except Exception as e:
            logger.error(f'Error processing image request: {str(e)}', exc_info=True)
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    def get_mq_status(self) -> Dict[str, Any]:
        """获取MQ连接状态 - 简单版"""
        try:
            # 简单的状态检查
            has_publisher = hasattr(self.event_manager, 'publisher_connection') and \
                           self.event_manager.publisher_connection and \
                           self.event_manager.publisher_connection.is_open

            has_consumer = hasattr(self.event_manager, 'consumer_connection') and \
                          self.event_manager.consumer_connection and \
                          self.event_manager.consumer_connection.is_open

            return {
                'timestamp': time.time(),
                'publisher_connected': has_publisher,
                'consumer_connected': has_consumer,
                'overall_status': 'connected' if (has_publisher and has_consumer) else 'disconnected'
            }
        except Exception as e:
            logger.warning(f"Failed to get MQ status: {e}")
            return {
                'timestamp': time.time(),
                'status': 'error',
                'error': str(e)
            }
