import logging
from typing import Dict, Any, Optional, List

from core.managers.label_config_manager import LabelConfigManager

logger = logging.getLogger(__name__)

class LabelService:
    def __init__(self):
        self.task_configs: Dict[str, LabelConfigManager] = {}

    def create_task_config(self, task_id: str, config_data: Optional[Dict] = None) -> LabelConfigManager:
        """为任务创建标签配置"""
        if config_data is None:
            logger.info(f"No config provided for task {task_id}, using default")
            config = LabelConfigManager.create_default()
        else:
            try:
                config = LabelConfigManager(config_data)
                logger.info(f"Created custom label config for task {task_id}: {config}")
            except Exception as e:
                logger.error(f"Error creating label config for task {task_id}: {str(e)}")
                config = LabelConfigManager.create_default()

        self.task_configs[task_id] = config
        return config

    def get_task_config(self, task_id: str) -> LabelConfigManager:
        """获取任务的标签配置"""
        config = self.task_configs.get(task_id)
        if config is None:
            logger.warning(f"No config found for task {task_id}, creating default")
            config = LabelConfigManager.create_default()
            self.task_configs[task_id] = config
        return config

    def remove_task_config(self, task_id: str) -> None:
        """移除任务的标签配置"""
        config = self.task_configs.pop(task_id, None)
        if config:
            logger.info(f"Removed label config for task {task_id}")

    def validate_config(self, config_data: Dict) -> bool:
        """验证配置数据的有效性 - 支持动态层级"""
        try:
            if not isinstance(config_data, dict):
                logger.error("Config data must be a dictionary")
                return False

            if 'label_levels' not in config_data:
                logger.error("Missing 'label_levels' in config")
                return False

            levels = config_data['label_levels']
            if not isinstance(levels, dict):
                logger.error("'label_levels' must be a dictionary")
                return False

            if len(levels) == 0:
                logger.error("At least one label level must be defined")
                return False

            # 验证每个层级的配置
            for level_name, level_data in levels.items():
                if not isinstance(level_name, str) or not level_name.strip():
                    logger.error(f"Invalid level name: {level_name}")
                    return False

                if not isinstance(level_data, dict):
                    logger.error(f"Level data for '{level_name}' must be a dictionary")
                    return False

                # 验证颜色配置
                if 'color' in level_data:
                    color = level_data['color']
                    if not self._validate_color(color):
                        logger.error(f"Invalid color for level '{level_name}': {color}")
                        return False

                # 验证阈值配置
                if 'alert_threshold' in level_data:
                    threshold = level_data['alert_threshold']
                    if not self._validate_threshold(threshold):
                        logger.error(f"Invalid alert_threshold for level '{level_name}': {threshold}")
                        return False

                # 验证标签列表
                if 'labels' in level_data:
                    labels = level_data['labels']
                    if not self._validate_labels(labels):
                        logger.error(f"Invalid labels for level '{level_name}': {labels}")
                        return False

                # 验证通知优先级
                if 'notification_priority' in level_data:
                    priority = level_data['notification_priority']
                    if not self._validate_priority(priority):
                        logger.error(f"Invalid notification_priority for level '{level_name}': {priority}")
                        return False

            logger.info(f"Config validation passed for {len(levels)} levels")
            return True

        except Exception as e:
            logger.error(f"Error validating config: {str(e)}")
            return False

    def _validate_color(self, color) -> bool:
        """验证颜色配置"""
        if isinstance(color, (list, tuple)):
            if len(color) >= 3:
                try:
                    return all(isinstance(c, int) and 0 <= c <= 255 for c in color[:3])
                except (TypeError, ValueError):
                    return False
        return False

    def _validate_threshold(self, threshold) -> bool:
        """验证阈值配置"""
        try:
            threshold_float = float(threshold)
            return 0.0 <= threshold_float <= 1.0
        except (TypeError, ValueError):
            return False

    def _validate_labels(self, labels) -> bool:
        """验证标签列表"""
        if isinstance(labels, str):
            return True # 字符串会被解析为逗号分隔的标签
        elif isinstance(labels, list):
            return all(isinstance(label, str) for label in labels)
        return False

    def _validate_priority(self, priority) -> bool:
        """验证通知优先级"""
        valid_priorities = {'low', 'medium', 'high', 'critical'}
        return isinstance(priority, str) and priority.lower() in valid_priorities

    def process_detection(self, task_id: str, label: str, confidence: float) -> Dict[str, Any]:
        """处理检测结果"""
        config = self.get_task_config(task_id)
        level = config.get_level_for_label(label)
        should_alert = config.should_alert(label, confidence)

        result = {
            'level': level.name,
            'color': level.color,
            'alert': should_alert,
            'priority': level.notification_priority,
            'threshold': level.alert_threshold,
            'confidence': confidence
        }

        if should_alert:
            logger.info(f"Alert triggered for task {task_id}: {label} ({confidence:.3f}) - Level: {level.name}")

        return result

    def get_task_levels(self, task_id: str) -> Dict[str, Dict[str, Any]]:
        """获取任务的所有层级配置"""
        config = self.get_task_config(task_id)
        return {
            name: {
                'labels': level.labels,
                'color': level.color,
                'alert_threshold': level.alert_threshold,
                'notification_priority': level.notification_priority
            }
            for name, level in config.get_all_levels().items()
        }

    def get_active_tasks(self) -> List[str]:
        """获取所有活跃任务ID"""
        return list(self.task_configs.keys())

    def cleanup_expired_tasks(self, active_task_ids: List[str]) -> None:
        """清理已过期的任务配置"""
        expired_tasks = set(self.task_configs.keys()) - set(active_task_ids)
        for task_id in expired_tasks:
            self.remove_task_config(task_id)

        if expired_tasks:
            logger.info(f"Cleaned up {len(expired_tasks)} expired task configs")

    def get_default_label_config(self) -> LabelConfigManager:
        """获取默认标签配置"""
        return LabelConfigManager.create_default()
