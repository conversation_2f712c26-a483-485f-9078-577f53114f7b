import logging
import os
import shutil
from abc import ABC, abstractmethod
from typing import Optional
from minio import Minio

logger = logging.getLogger(__name__)

class FileServiceInterface(ABC):
    """文件服务抽象接口"""

    @abstractmethod
    def download_file(self, remote_path: str, task_id: str) -> str:
        """从远程存储下载文件到本地缓存"""
        pass

    @abstractmethod
    def upload_file(self, local_path: str, remote_path: str) -> None:
        """将本地文件上传到远程存储"""
        pass

    @abstractmethod
    def cleanup_cache(self, task_id: str) -> None:
        """清理指定任务的缓存文件"""
        pass

    @abstractmethod
    def get_local_output_path(self, remote_path: str, task_id: str) -> str:
        """根据远程路径生成本地输出路径"""
        pass

    @abstractmethod
    def file_exists(self, remote_path: str) -> bool:
        """检查远程文件是否存在"""
        pass

class MinioFileService(FileServiceInterface):
    """MinIO文件服务实现"""

    def __init__(self, config):
        self.config = config
        self.minio_client = Minio(
            endpoint=config.minio_url,
            access_key=config.access_key,
            secret_key=config.secret_key,
            secure=config.secure
        )
        # Use absolute path for cache directory
        self.cache_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'video_cache'))
        os.makedirs(self.cache_dir, exist_ok=True)

    def download_file(self, remote_path: str, task_id: str) -> str:
        """从MinIO下载文件到本地缓存"""
        try:
            bucket_name = self.config.bucket_name
            # Fix object_name construction
            object_name = remote_path.replace('\\', '/').lstrip('/')
            local_path = os.path.join(self.cache_dir, f"{task_id}_{os.path.basename(remote_path)}")

            if not os.path.exists(local_path):
                logger.info(f"Downloading file from MinIO: {remote_path} to {local_path}")
                self.minio_client.fget_object(bucket_name, object_name, local_path)
            else:
                logger.info(f"Using cached file: {local_path}")

            return local_path
        except Exception as e:
            logger.error(f"Error downloading file from MinIO: {str(e)}")
            raise

    def upload_file(self, local_path: str, remote_path: str) -> None:
        """将本地文件上传到MinIO"""
        try:
            bucket_name = self.config.bucket_name
            # Fix object_name construction
            object_name = remote_path.replace('\\', '/').lstrip('/')
            logger.info(f"Uploading file to MinIO: {local_path} -> {remote_path}")
            self.minio_client.fput_object(bucket_name, object_name, local_path)
        except Exception as e:
            logger.error(f"Error uploading file to MinIO: {str(e)}")
            raise

    def cleanup_cache(self, task_id: str) -> None:
        """清理指定任务的缓存文件"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.startswith(f"{task_id}_"):
                    file_path = os.path.join(self.cache_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Cleaned up cached file: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up cache: {str(e)}")

    def get_local_output_path(self, remote_path: str, task_id: str) -> str:
        """根据MinIO路径生成本地输出路径"""
        return os.path.join(self.cache_dir, f"{task_id}_output_{os.path.basename(remote_path)}")

    def file_exists(self, remote_path: str) -> bool:
        """检查MinIO中文件是否存在"""
        try:
            bucket_name = self.config.bucket_name
            object_name = remote_path.replace('\\', '/').lstrip('/')
            self.minio_client.stat_object(bucket_name, object_name)
            return True
        except Exception:
            return False

class NASFileService(FileServiceInterface):
    """本地NAS文件服务实现"""

    def __init__(self, config):
        self.config = config
        self.nas_root = config.nas_root_path
        # Use absolute path for cache directory
        self.cache_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'video_cache'))
        os.makedirs(self.cache_dir, exist_ok=True)

        # 验证NAS根路径是否存在
        if not os.path.exists(self.nas_root):
            logger.warning(f"NAS root path does not exist: {self.nas_root}")

    def download_file(self, remote_path: str, task_id: str) -> str:
        """从NAS复制文件到本地缓存"""
        try:
            # 构建完整的NAS文件路径
            nas_file_path = self._get_nas_file_path(remote_path)
            local_path = os.path.join(self.cache_dir, f"{task_id}_{os.path.basename(remote_path)}")

            if not os.path.exists(local_path):
                if not os.path.exists(nas_file_path):
                    raise FileNotFoundError(f"File not found on NAS: {nas_file_path}")

                logger.info(f"Copying file from NAS: {nas_file_path} to {local_path}")
                shutil.copy2(nas_file_path, local_path)
            else:
                logger.info(f"Using cached file: {local_path}")

            return local_path
        except Exception as e:
            logger.error(f"Error downloading file from NAS: {str(e)}")
            raise

    def upload_file(self, local_path: str, remote_path: str) -> None:
        """将本地文件上传到NAS"""
        try:
            nas_file_path = self._get_nas_file_path(remote_path)

            # 确保目标目录存在
            os.makedirs(os.path.dirname(nas_file_path), exist_ok=True)

            logger.info(f"Uploading file to NAS: {local_path} -> {nas_file_path}")
            shutil.copy2(local_path, nas_file_path)
        except Exception as e:
            logger.error(f"Error uploading file to NAS: {str(e)}")
            raise

    def cleanup_cache(self, task_id: str) -> None:
        """清理指定任务的缓存文件"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.startswith(f"{task_id}_"):
                    file_path = os.path.join(self.cache_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Cleaned up cached file: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up cache: {str(e)}")

    def get_local_output_path(self, remote_path: str, task_id: str) -> str:
        """根据NAS路径生成本地输出路径"""
        return os.path.join(self.cache_dir, f"{task_id}_output_{os.path.basename(remote_path)}")

    def file_exists(self, remote_path: str) -> bool:
        """检查NAS中文件是否存在"""
        try:
            nas_file_path = self._get_nas_file_path(remote_path)
            return os.path.exists(nas_file_path)
        except Exception:
            return False

    def _get_nas_file_path(self, remote_path: str) -> str:
        """将远程路径转换为NAS本地路径"""
        # 处理路径映射：
        # Java 传递: /app/output/...
        # 实际文件位置: /data/nas/app/output/...

        # 如果路径已经是 /data/nas 开头，直接使用
        if remote_path.startswith('/data/nas/'):
            nas_full_path = remote_path
        # 如果路径是 /app/output 开头，需要映射到 /data/nas/app/output
        elif remote_path.startswith('/app/output/'):
            # 保留完整的路径，拼接到 /data/nas
            relative_path = remote_path.lstrip('/') # 去掉开头的 /
            nas_full_path = os.path.join('/data/nas', relative_path)
        elif remote_path.startswith('app/output/'):
            # 处理没有开头斜杠的情况
            nas_full_path = os.path.join('/data/nas', remote_path)
        else:
            # 其他情况，移除开头的斜杠，拼接到 nas_root_path
            relative_path = remote_path.lstrip('/')
            relative_path = relative_path.replace('\\', os.sep).replace('/', os.sep)
            nas_full_path = os.path.join(self.nas_root, relative_path)

        # 标准化路径分隔符
        nas_full_path = nas_full_path.replace('\\', os.sep).replace('/', os.sep)

        # 添加调试日志
        logger.info(f"Path mapping: {remote_path} -> {nas_full_path}")
        return nas_full_path

class FileServiceFactory:
    """文件服务工厂类"""

    @staticmethod
    def create_file_service(config) -> FileServiceInterface:
        """根据配置创建对应的文件服务"""
        storage_type = getattr(config, 'storage_type', 'nas').lower()

        if storage_type == 'nas':
            logger.info("Creating NAS file service")
            return NASFileService(config)
        elif storage_type == 'minio':
            logger.info("Creating MinIO file service")
            return MinioFileService(config)
        else:
            logger.warning(f"Unknown storage type: {storage_type}, defaulting to NAS")
            return NASFileService(config)