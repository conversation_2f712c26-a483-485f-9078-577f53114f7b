FROM nvidia/cuda:11.8.0-cudnn8-devel-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

ENV ENVIRONMENT=production
ENV ENABLE_CUDA=true
ENV MAX_WORKERS=16
ENV PYTHONUNBUFFERED=1

ENV CUDA_HOME=/usr/local/cuda
ENV PATH=/usr/local/cuda/bin:/usr/local/bin:$PATH
ENV LD_LIBRARY_PATH=/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:$LD_LIBRARY_PATH
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

ENV FLAGS_allocator_strategy=auto_growth
ENV FLAGS_fraction_of_gpu_memory_to_use=0.6
ENV FLAGS_conv_workspace_size_limit=512

ENV OPENCV_FFMPEG_CAPTURE_OPTIONS="rtsp_transport;tcp"
ENV OPENCV_FFMPEG_WRITER_OPTIONS="preset;ultrafast"
ENV FFMPEG_CAPTURE_OPTIONS="rtsp_transport;tcp"

# 更新包列表并安装基础工具
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    wget \
    curl \
    gnupg \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 和基础依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3.10 \
    python3-pip \
    python3.10-venv \
    python3.10-dev \
    python3-setuptools \
    python3-wheel \
    && rm -rf /var/lib/apt/lists/*

# 强制创建 Python 和 pip 的符号链接
RUN ln -sf /usr/bin/python3.10 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip && \
    ln -sf /usr/bin/python3.10 /usr/local/bin/python && \
    ln -sf /usr/bin/pip3 /usr/local/bin/pip && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1

RUN pip install --upgrade pip setuptools wheel

# 安装系统依赖包
RUN apt-get update && apt-get install -y --no-install-recommends \
    mesa-utils \
    libglib2.0-0 \
    libblas-dev \
    libopenblas-dev \
    coreutils \
    util-linux \
    dos2unix \
    yasm \
    nasm \
    pkg-config \
    git \
    build-essential \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# 验证CUDA和cuDNN已经安装
RUN nvcc --version && \
    python -c "import os; print('CUDA_HOME:', os.environ.get('CUDA_HOME', 'Not set'))" && \
    ls -la /usr/local/cuda/lib64/libcudnn* || echo "cuDNN libs not found in expected location"

# 编译并安装 Cisco openh264
WORKDIR /tmp
RUN wget -q https://github.com/cisco/openh264/archive/refs/tags/v2.3.1.tar.gz && \
    tar -xf v2.3.1.tar.gz && \
    cd openh264-2.3.1 && \
    make -j$(nproc) && make install && \
    ldconfig

# 编译并安装 x265（从 GitHub）
WORKDIR /tmp
RUN git clone https://github.com/videolan/x265.git && \
    cd x265/build/linux && \
    cmake -G "Unix Makefiles" -DCMAKE_INSTALL_PREFIX=/usr/local ../../source && \
    make -j$(nproc) && make install && \
    ldconfig

# 编译并安装 FFmpeg 启用 libopenh264 和 libx265
WORKDIR /tmp
RUN apt-get update && apt-get install -y --no-install-recommends \
    libx264-dev libvpx-dev libfdk-aac-dev libmp3lame-dev libopus-dev && \
    wget -q https://ffmpeg.org/releases/ffmpeg-6.0.tar.bz2 && \
    tar -xf ffmpeg-6.0.tar.bz2 && \
    cd ffmpeg-6.0 && \
    ./configure --prefix=/usr/local \
                --pkg-config-flags="--static" \
                --extra-cflags="-I/usr/local/include" \
                --extra-ldflags="-L/usr/local/lib" \
                --enable-gpl \
                --enable-nonfree \
                --enable-libopenh264 \
                --enable-libx264 \
                --enable-libx265 \
                --enable-libvpx \
                --enable-libfdk-aac \
                --enable-libmp3lame \
                --enable-libopus \
                && \
    make -j$(nproc) && make install && \
    ldconfig && \
    ffmpeg -version && \
    echo "=== Checking for libopenh264 support ===" && \
    (ffmpeg -encoders | grep -i openh264 || echo "libopenh264 encoder not found") && \
    echo "=== Checking for libx265 support ===" && \
    (ffmpeg -encoders | grep -i libx265 || echo "libx265 encoder not found")

# 安装 OpenCV 系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgtk-3-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libwebp-dev \
    libopenjp2-7-dev \
    libatlas-base-dev \
    gfortran \
    libeigen3-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements-gpu.txt /app/

RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir --ignore-installed numpy==1.24.3 scipy==1.10.1 Pillow==10.0.1
RUN pip install --no-cache-dir --ignore-installed setuptools==68.0.0 wheel==0.40.0 Cython==0.29.36
RUN pip install --no-cache-dir --ignore-installed -r requirements-gpu.txt

# 安装 PyAV 模块以验证 FFmpeg
RUN pip install --no-cache-dir av==10.0.0

# 验证 PyAV 和 H.264 支持
RUN python3 -c "import av; print('✅ PyAV version:', av.__version__); \
    container = av.open('/tmp/test.mp4', 'w'); \
    stream = container.add_stream('h264', rate=30); \
    stream.width = 640; stream.height = 480; \
    print('✅ H.264 encoder created successfully'); \
    container.close(); print('✅ PyAV H.264 support verified')"

RUN mkdir -p /app/video_cache /app/logs /data/nas && \
    chmod 777 /app/video_cache /app/logs /data/nas

COPY . /app

RUN dos2unix /app/start.sh && chmod +x /app/start.sh

RUN python3 --version && python --version && which python && which python3

HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD python3 -c "print('Health check OK')" || exit 1

EXPOSE 9020

CMD ["/app/start.sh"]
