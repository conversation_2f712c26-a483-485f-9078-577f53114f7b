# Ultralytics YOLO ByteTrack配置文件 - 气体泄漏检测优化版
# 基于官方bytetrack.yaml格式，针对气体泄漏检测进行优化
# 只包含ultralytics识别的标准参数ultralytics识别的标准参数

tracker_type: bytetrack # tracker type, ['botsort', 'bytetrack']
track_high_thresh: 0.5 # threshold for the first association (降低以适应气体检测)
track_low_thresh: 0.2 # threshold for the second association (提高以减少误检)
new_track_thresh: 0.4 # threshold for init new track if the detection does not match any tracks
track_buffer: 100 # buffer to calculate the time when to remove tracks (增加以适应气体持续性)
match_thresh: 0.6 # threshold for matching tracks (放宽匹配条件)
min_box_area: 100 # threshold for min box areas (提高最小检测面积)
# mot20: False # for tracker evaluation(not used for now)
