# 统一ByteTrack追踪器配置文件
# Unified ByteTrack Configuration
# ================================
#
# 支持通用目标追踪和气体泄漏检测的统一配置
# Supports both general object tracking and gas leakage detection

# 配置模式选择 (general | gas_leakage | auto)
mode: gas_leakage  # auto: 根据gas_leakage_mode参数自动切换

# 追踪器类型
tracker_type: bytetrack

# === 通用配置 (General Configuration) ===
general:
  # 核心参数
  track_high_thresh: 0.6
  track_low_thresh: 0.1
  new_track_thresh: 0.7
  track_buffer: 30
  match_thresh: 0.8
  
  # 视频参数
  frame_rate: 30
  
  # 高级参数
  min_box_area: 10
  max_time_lost: 30
  
  # 稳定性参数
  stability_frames: 5
  min_area: 100
  max_area: 50000
  confidence_thresh: 0.4
  
  # 性能优化
  fast_mode: true
  use_gpu: true
  batch_size: 1
  
  # 可视化参数
  trail_length: 30
  trail_thickness: 2
  bbox_thickness: 2
  show_track_id: true
  show_confidence: true
  show_class_name: true
  
  # 调试参数
  verbose: false
  save_tracking_results: false
  tracking_results_path: "./tracking_results"
  
  # 场景参数
  indoor_mode: false
  outdoor_mode: true
  fixed_camera: true
  mobile_camera: false
  
  # 多类别追踪
  multi_class_tracking: true
  class_specific_params:
    default:
      track_high_thresh: 0.6
      track_low_thresh: 0.1
      min_box_area: 10
      track_buffer: 30

# === 气体泄漏检测配置 (Gas Leakage Configuration) ===
gas_leakage:
  # 核心参数 - 针对气体特性优化
  track_high_thresh: 0.5      # 降低高置信度阈值
  track_low_thresh: 0.2       # 提高低置信度阈值
  new_track_thresh: 0.4       # 降低新轨迹创建阈值
  track_buffer: 50            # 增加轨迹缓冲
  match_thresh: 0.6           # 放宽匹配条件
  
  # 视频参数
  frame_rate: 30
  
  # 高级参数 - 气体特性
  min_box_area: 100           # 提高最小检测面积
  max_time_lost: 40           # 增加最大丢失时间
  
  # 气体专用参数
  gas_stability_frames: 8
  gas_min_area: 200
  gas_max_area: 100000
  gas_confidence_thresh: 0.3
  shape_change_tolerance: 0.4
  motion_prediction_weight: 0.3
  
  # 无人机镜头位移补偿
  enable_camera_compensation: true
  camera_motion_confidence_thresh: 0.3
  max_displacement_compensation: 50
  motion_smoothing_alpha: 0.3
  
  # 性能优化
  fast_mode: true
  use_gpu: true
  batch_size: 1
  memory_efficient: true
  
  # 检测优化
  nms_thresh: 0.7
  multi_scale: true
  detection_enhancement: true
  
  # 可视化参数 - 气体特化
  trail_length: 25
  trail_thickness: 2
  bbox_thickness: 3
  show_track_id: true
  show_confidence: true
  show_class_name: true
  show_gas_properties: true
  show_motion_direction: true
  
  # 输出格式
  output_format: "enhanced_json"
  include_contour_data: true
  include_motion_analysis: true
  include_dispersion_analysis: true
  
  # 调试参数
  debug_mode: false
  save_intermediate: false
  verbose_logging: false
  
  # 特殊处理
  handle_occlusion: true
  handle_deformation: true
  handle_split_merge: true
  split_detection_thresh: 0.3
  merge_detection_thresh: 0.7
  
  # 多类别追踪 - 气体优化
  multi_class_tracking: true
  class_specific_params:
    gas_leak:
      track_high_thresh: 0.5
      track_low_thresh: 0.2
      min_box_area: 100
      track_buffer: 40
    default:
      track_high_thresh: 0.5
      track_low_thresh: 0.2
      min_box_area: 50
      track_buffer: 35

# === 高级参数调优 ===
advanced:
  # 卡尔曼滤波器
  kalman_noise_std: 0.1
  
  # 匈牙利算法
  hungarian_cost_thresh: 0.5
  
  # 轨迹平滑
  trajectory_smoothing: 0.3
  
  # 气体模式特定调优
  gas_mode_overrides:
    kalman_noise_std: 0.15      # 气体运动更随机
    trajectory_smoothing: 0.4   # 更强的平滑
    hungarian_cost_thresh: 0.4  # 放宽成本阈值

# === 模式切换规则 ===
mode_selection:
  # 自动模式选择参数
  auto_detection_keywords:
    - "gas"
    - "leak"
    - "leakage"
  
  # 强制模式参数映射
  mode_mapping:
    general: "general"
    gas: "gas_leakage"
    gas_leakage: "gas_leakage"
    leakage: "gas_leakage"
    normal: "general"
    default: "general" 