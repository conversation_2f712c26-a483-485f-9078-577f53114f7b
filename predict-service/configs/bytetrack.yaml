# ByteTrack追踪器配置文件
# ================================
# 
# ByteTrack是一种高效的多目标追踪算法，专门针对实时应用优化。
# 它通过两步关联策略和低分检测恢复机制，实现了优秀的追踪性能。
#
# 主要特点：
# - 高效的数据关联算法
# - 处理遮挡和快速运动
# - 适用于实时应用
# - 支持多种目标类型

# 追踪器类型
tracker_type: bytetrack

# === 核心参数 ===
# 高置信度阈值：用于高质量轨迹创建
track_high_thresh: 0.6

# 低置信度阈值：用于轨迹恢复
track_low_thresh: 0.1

# 新轨迹创建阈值：创建新轨迹所需的最低置信度
new_track_thresh: 0.7

# 轨迹缓冲：轨迹消失后保持的帧数
track_buffer: 30

# 匹配阈值：用于数据关联的IoU阈值
match_thresh: 0.8

# === 视频参数 ===
# 帧率：用于速度和运动预测
frame_rate: 30

# === 高级参数 ===
# 最小轨迹长度：轨迹被认为有效的最小长度
min_box_area: 10

# 最大轨迹数量：同时追踪的最大目标数
max_time_lost: 30

# === 泄漏检测专用参数 ===
# 针对泄漏检测场景的优化参数

# 泄漏区域稳定性阈值
leak_stability_frames: 5

# 泄漏区域最小面积（像素）
leak_min_area: 100

# 泄漏区域最大面积（像素）
leak_max_area: 50000

# 泄漏检测置信度阈值
leak_confidence_thresh: 0.4

# === 性能优化参数 ===
# 针对实时处理的优化

# 启用快速模式（略微降低准确性以提高速度）
fast_mode: true

# 启用GPU加速（如果可用）
use_gpu: true

# 批处理大小（用于批量处理）
batch_size: 1

# === 可视化参数 ===
# 轨迹可视化设置

# 轨迹历史长度
trail_length: 30

# 轨迹线宽度
trail_thickness: 2

# 边界框线宽度
bbox_thickness: 2

# 显示轨迹ID
show_track_id: true

# 显示置信度
show_confidence: true

# 显示类别名称
show_class_name: true

# === 调试参数 ===
# 用于调试和监控

# 启用详细日志
verbose: false

# 保存追踪结果
save_tracking_results: false

# 追踪结果保存路径
tracking_results_path: "./tracking_results"

# === 高级调优参数 ===
# 专业用户可以调整的参数

# 卡尔曼滤波器噪声参数
kalman_noise_std: 0.1

# 匈牙利算法成本阈值
hungarian_cost_thresh: 0.5

# 轨迹平滑参数
trajectory_smoothing: 0.3

# === 场景特定参数 ===
# 针对不同场景的优化

# 室内场景优化
indoor_mode: false

# 户外场景优化
outdoor_mode: true

# 固定摄像头模式
fixed_camera: true

# 移动摄像头模式
mobile_camera: false

# === 多类别追踪参数 ===
# 支持多类别目标追踪

# 启用多类别追踪
multi_class_tracking: true

# 类别特定参数
class_specific_params:
  # 泄漏类别（假设class_id=0）
  leak:
    track_high_thresh: 0.5
    track_low_thresh: 0.1
    min_box_area: 50
    track_buffer: 40
  
  # 其他类别的默认参数
  default:
    track_high_thresh: 0.6
    track_low_thresh: 0.1
    min_box_area: 10
    track_buffer: 30 