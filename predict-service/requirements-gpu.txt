# =============================================================================
# Python 3.10 + CUDA 11.8 优化版本依赖文件
# PaddlePaddle使用CPU版本，避免GPU设备属性重复注册问题
# =============================================================================

--extra-index-url https://download.pytorch.org/whl/cu118
# matching image transforms
albumentations==1.4.10

# ============== 基础数值计算库（必须最先安装） ==============
numpy==1.24.4
scipy==1.10.1
Pillow==10.0.1

# ============== 编译工具依赖 ==============
setuptools==68.0.0
wheel==0.40.0
Cython==0.29.36

# ============== 深度学习框架（GPU版本） ==============
torch==2.1.0+cu118
torchvision==0.16.0+cu118
torchaudio==2.1.0+cu118

# ============== PaddlePaddle框架（CPU版本） ==============
paddleocr==2.9.1
paddlepaddle-gpu==2.6.2


# ============== 计算机视觉 ==============
opencv-python==*********

# ============== Web框架 ==============
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7

# ============== 数据处理 ==============
pandas==2.0.3
openpyxl==3.1.2
xlrd==2.0.1
xlwt==1.3.0

# ============== 图像处理 ==============
imageio==2.31.1
scikit-image==0.21.0


# ============== 机器学习 ==============
scikit-learn==1.3.2

# ============== 网络请求 ==============
requests==2.31.0
urllib3==2.0.4
httpx==0.24.1

# ============== 日志和配置 ==============
PyYAML==6.0.2
python-dotenv==1.0.0

# ============== 数据验证 ==============
marshmallow==3.20.1

# ============== 异步处理 ==============
celery==5.3.1
redis==4.6.0

# ============== 其他工具 ==============
tqdm==4.65.0
psutil==5.9.5

# ============== FFmpeg Python绑定（仅用于辅助功能） ==============
ffmpeg-python==0.2.0

# ============== 多媒体处理 ==============
moviepy==1.0.3

# ============== 数据库 ==============
SQLAlchemy==2.0.19

# ============== 时间处理 ==============
python-dateutil==2.8.2

# ============== JSON处理 ==============
ujson==5.8.0

# ============== 文件处理 ==============
python-magic==0.4.27

# ============== 字符编码检测 ==============
chardet==5.1.0

# ============== 系统监控 ==============
# GPUtil==1.4.0  # GPU监控工具，CPU模式下不需要

# ============== 文本处理 ==============
jieba==0.42.1

# ============== 加密 ==============
cryptography==41.0.3

# ============== 序列化 ==============
pickle-mixin==1.0.2
dill==0.3.7

# ============== 文件监控 ==============
watchdog==3.0.0

# ============== 线性分配算法 ==============
lap==0.4.0

# ============== 模型分析工具 ==============
thop==0.1.1-2209072238

# ============== 深度学习工具 ==============
ultralytics==8.0.196

# ============== 数据可视化 ==============
matplotlib==3.7.1
seaborn==0.12.2

# ============== 消息队列 ==============
pika==1.3.2

# ============== 系统工具 ==============
pathlib2==2.3.7

# ============== 存储服务 ==============
minio==7.1.17

# ============== 测试工具 ==============
pytest==7.4.0
pytest-cov==4.1.0

av==10.0.0
lapx>=0.5.2

kombu>=5.3.0
