#!/bin/bash

# 配置参数
REPO_ADDRESS="your-private-repo-address"
IMAGE_NAME="image-name"
TAG="tag"
USERNAME="username"
PASSWORD="password"

# 构建 Docker 镜像
echo "Building Docker image..."
docker build -t $REPO_ADDRESS/$IMAGE_NAME:$TAG .

# 检查镜像是否构建成功
if [ $? -eq 0 ]; then
    echo "Image built successfully."
else
    echo "Image build failed."
    exit 1
fi

# 登录到 Docker 仓库
echo "Logging into Docker repository..."
echo $PASSWORD | docker login $REPO_ADDRESS --username $USERNAME --password-stdin

# 检查登录是否成功
if [ $? -eq 0 ]; then
    echo "Logged in successfully."
else
    echo "Login failed."
    exit 1
fi

# 推送镜像到仓库
echo "Pushing image to repository..."
docker push $REPO_ADDRESS/$IMAGE_NAME:$TAG

# 检查推送是否成功
if [ $? -eq 0 ]; then
    echo "Image pushed successfully."
else
    echo "Image push failed."
    exit 1
fi
