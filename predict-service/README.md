# Predict Service

## 概述
Predict Service 是一个基于 Flask 的微服务，专为视频和图像处理提供机器学习能力。它提供了健壮的任务管理、事件处理和通过消息队列系统实现的实时进度跟踪。

## 技术原理与算法

### 1. 光流分析 (Optical Flow Analysis)
光流分析是一种用于检测视频连续帧之间物体运动的计算机视觉技术，在气体泄漏检测中用于分析气体移动模式。

#### 核心算法
- **Farnebäck 算法**: 密集光流计算，使用多项式展开创建完整的流场
- **Lucas-Kanade 方法**: 稀疏光流，用于追踪特定特征点
- **Horn-Schunck 方法**: 全局光流估计方法

#### 实现公式
- Singleton pattern implementation
- RabbitMQ connection management
- Message publishing and subscription handling
- Retry mechanism and error handling

#### 3. Task Manager (task_manager.py)
- Video processing task lifecycle management
- Task progress tracking
- Status update mechanism
- Automatic expired task cleanup

#### 4. Service Layer (services/)

##### Video Service (video_service.py)
- Core video processing orchestration
- Manages multiple processors and services
- Thread pool for concurrent processing
- Real-time progress tracking and updates
- Integration with MinIO for storage
- Support for task interruption

##### Frame Processor (frame_processor.py)
- Frame-by-frame video analysis
- Object detection and mask processing
- Optical flow computation
- Confidence mapping
- Leak detection and velocity calculation

##### Image Processor (image_processor.py)
- Static image analysis
- Object detection and classification
- Integration with ML models

##### OCR Processor (ocr_processor.py)
- Text recognition in images
- GPU acceleration support
- Text extraction and processing

##### MinIO Service (minio_service.py)
- Object storage integration
- File upload and download
- Temporary URL generation

##### Label Service (label_service.py)
- Label configuration management
- Detection classification
- Color mapping for visualizations

### Directory Structure
```
predict-service/
├── app.py                  # Main application entry
├── config.ini             # Configuration file
├── config_manager.py      # Configuration management
├── event_manager.py       # Event handling system
├── task_manager.py        # Task management
├── services/
│   ├── video_service.py   # Video processing orchestration
│   ├── frame_processor.py # Frame analysis
│   ├── image_processor.py # Image processing
│   ├── ocr_processor.py   # Text recognition
│   ├── minio_service.py   # Storage service
│   └── label_service.py   # Label management
├── models/               # ML models directory
├── utils/               # Utility functions
└── requirements.txt     # Python dependencies
```

## Core Functionalities

### 1. Video Processing Flow
1. Request reception and validation
2. Task initialization and configuration
3. Frame-by-frame processing:
   - Object detection
   - Mask generation
   - Optical flow computation
   - Leak detection
4. Progress tracking and status updates
5. Results storage and notification

### 2. Task Management Flow
1. Task record creation
2. Progress updates
3. Status monitoring
4. Completion/failure handling

### 3. Image Processing Flow
1. Image validation and preprocessing
2. Object detection and classification
3. OCR processing (if required)
4. Results generation and storage

## Key Features

### 1. Reliability
- Thread-safe operations with locks
- Robust retry mechanisms
- Comprehensive error handling
- Detailed logging system

### 2. Scalability
- Modular design
- Configuration-driven architecture
- CPU/GPU processing support
- Containerized deployment ready

### 3. Monitoring
- Detailed task progress tracking
- Complete logging system
- Automated resource cleanup
- Real-time status updates

## Configuration

### Environment Setup
1. CPU Environment:
```bash
pip install -r requirements-cpu.txt
```

2. GPU Environment:
```bash
pip install -r requirements.txt
```

### Configuration File (config.ini)
Key configurations:
```ini
[rabbitmq]
host=localhost
port=5672
username=guest
password=guest
virtual_host=transfer

[service]
output_dir=/app/output
model_path=models/best.pt
```

## Deployment

### Docker Deployment
```bash
# Build the Docker image
docker build -t predict-service .

# Run the container
docker run -d \
  -p 9020:9020 \
  -v /path/to/output:/app/output \
  predict-service
```

### Production Deployment
- Uses Gunicorn as WSGI server
- Supports Docker Compose integration
- Includes health check mechanisms

## API Documentation

### 1. Process Video
```http
POST /process-video
Content-Type: application/json

{
  "taskId": "string",
  "videoId": "string",
  "userId": "string",
  "label_config": {
    // Label configuration object
  }
}
```

### 2. Process Image
```http
POST /process-image
Content-Type: application/json

{
  "imageId": "string",
  "userId": "string"
}
```

## Development Guidelines

### 1. Setup Development Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux
.\venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements-dev.txt
```

### 2. Running Tests
```bash
python -m pytest tests/
```

### 3. Code Style
- Follow PEP 8 guidelines
- Use type hints
- Document all public functions and classes

## Future Improvements

1. Testing
- Add comprehensive unit tests
- Implement integration tests
- Add performance benchmarks

2. Monitoring
- Add performance metrics collection
- Implement detailed system monitoring
- Enhanced error tracking

3. Documentation
- Add API documentation generation
- Improve inline code documentation
- Create development guidelines

4. Features
- Implement health check endpoints
- Add support for more video formats
- Enhance error handling granularity
- Add batch processing capabilities

## Troubleshooting

### Common Issues

1. RabbitMQ Connection
```python
# Check connection status
rabbitmq_client.check_connection()
```

2. Task Status
```python
# Get task status
task_manager.get_task_progress(task_id)
```

3. Resource Usage
- Monitor GPU memory usage
- Check disk space for video storage
- Monitor RabbitMQ queue size

## License
See LICENSE file for details.
