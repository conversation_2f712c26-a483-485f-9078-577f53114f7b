# Ultralytics YOLO 🚀, AGPL-3.0 license
# COCO128 dataset https://www.kaggle.com/ultralytics/coco128 (first 128 label from COCO train2017) by Ultralytics
# Documentation: https://docs.ultralytics.com/datasets/detect/coco/
# Example usage: yolo train data=coco128.yaml
# parent
# ├── ultralytics
# └── datasets
#     └── coco128  ← downloads here (7 MB)

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: /Users/<USER>/PycharmProjects/uav-detector/uav-detector-yolov8/ultralytics/ultralytics/data/datasets/smoke # dataset root dir
train: label # train label (relative to 'path') 128 label
val: label # val label (relative to 'path') 128 label
test: # test label (optional)

# Classes
names:
  0: gas

