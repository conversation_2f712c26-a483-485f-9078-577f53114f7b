import os
import logging
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入配置管理器以获取日志配置
    from core.managers.config_manager import ConfigManager

    # 获取配置管理器实例并读取日志配置
    config_manager = ConfigManager()
    log_config = config_manager.logging

    # 根据配置文件设置日志级别
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level = log_level_map.get(log_config['level'].upper(), logging.INFO)

    print(f"Gunicorn日志级别设置为: {log_config['level']} ({log_level})")

except Exception as e:
    # 如果配置加载失败，使用默认的INFO级别
    log_level = logging.INFO
    print(f"配置加载失败，使用默认日志级别INFO: {e}")

# 配置日志
logging.basicConfig(
    level=log_level,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger('gunicorn')

# 绑定地址和端口
bind = '0.0.0.0:9020'

# Worker进程数量 - 使用单个worker以避免GPU内存冲突
workers = 1

# 超时设置 - 增加以适配模型加载时间
timeout = 600
worker_timeout = 600
keepalive = 5

# 内存和进程管理
max_requests = 100
max_requests_jitter = 10
preload_app = True # 预加载应用，避免重复加载模型

# 日志配置
loglevel = 'debug' # 增加日志级别以便调试
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
errorlog = '/app/logs/error.log'
accesslog = '/app/logs/access.log'
capture_output = True

# 进程名称
proc_name = 'iflight-predict-service'

# Worker类型
worker_class = 'sync'
worker_connections = 10

# 临时目录
tmp_upload_dir = '/tmp'

def on_starting(server):
    """服务器启动时的回调函数"""
    logger.info("Starting Gunicorn server...")
    logger.info(f"Python version: {os.popen('python --version').read().strip()}")
    logger.info(f"CUDA version: {os.popen('nvidia-smi | grep CUDA').read().strip()}")
    logger.info(f"GPU info: {os.popen('nvidia-smi').read().strip()}")
    logger.info(f"Environment variables: {dict(os.environ)}")

def post_fork(server, worker):
    """Worker进程创建后的回调函数"""
    logger.info(f"Worker {worker.pid} started")

def pre_fork(server, worker):
    """Worker进程创建前的回调函数"""
    logger.info(f"About to fork worker {worker.pid}")

def pre_exec(server):
    """执行新程序前的回调函数"""
    logger.info("About to exec new program")

def when_ready(server):
    """服务器准备就绪时的回调函数"""
    logger.info("Server is ready. Spawning workers")
