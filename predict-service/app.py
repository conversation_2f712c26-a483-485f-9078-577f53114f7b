#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超级安全的预测服务 - 防止Windows访问冲突
专门解决 0xC0000005 访问违例问题
"""

# Step 1: 在所有导入之前设置关键环境变量防止访问冲突
import os
import sys
from typing import Dict, Any

# 安全GPU模式环境变量设置（替换强制CPU模式）
print("设置安全GPU环境变量...")
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['VECLIB_MAXIMUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

# PyTorch线程安全设置
os.environ['TORCH_NUM_THREADS'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1' # 启用CUDA设备边检查

# OpenCV线程限制
os.environ['OPENCV_LOG_LEVEL'] = 'ERROR'

# GPU安全模式设置（不再强制CPU）
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128' # 限制CUDA内存分片大小
os.environ['CUDA_LAUNCH_BLOCKING'] = '1' # 同步CUDA操作，便于调试
# 注释掉强制CPU的设置
# os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
# os.environ['ENABLE_CUDA'] = 'false'

# PaddleOCR稳定性设置
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.3' # 增加GPU内存使用比例
os.environ['PP_OCR_VERSION'] = 'PP-OCRv3'

# Windows内存管理
os.environ['PYTHONMALLOC'] = 'debug'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'

print("环境变量设置完成")

# Step 2: 启用崩溃追踪
import faulthandler

faulthandler.enable(all_threads=True)
print("崩溃追踪已启用")

# Step 3: 安全导入和设置
import warnings
import gc
import logging
import signal
import psutil
from datetime import datetime

# 设置警告过滤
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 现在安全导入PyTorch和OpenCV
print("安全导入关键库...")
try:
    import torch

    # 立即设置PyTorch线程安全
    if hasattr(torch, 'set_num_threads'):
        torch.set_num_threads(1)
    print(f"PyTorch {torch.__version__} 导入成功")
except Exception as e:
    print(f"PyTorch导入失败: {e}")
    sys.exit(1)

try:
    import cv2

    # 立即设置OpenCV线程安全
    cv2.setNumThreads(1)
    cv2.setUseOptimized(True)
    print(f"OpenCV {cv2.__version__} 导入成功")
except Exception as e:
    print(f"OpenCV导入失败: {e}")
    sys.exit(1)

# 其他安全导入
import numpy as np
from flask import Flask, request, jsonify, send_file
from ultralytics import YOLO
from werkzeug.utils import secure_filename
import tempfile
import zipfile
from pathlib import Path
import time
import uuid

print("所有关键库导入成功")

# 先导入配置管理器以获取日志配置
from core.managers.config_manager import ConfigManager

# 获取配置管理器实例并读取日志配置
config_manager = ConfigManager()
log_config = config_manager.logging

# 根据配置文件设置日志级别
log_level_map = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}
log_level = log_level_map.get(log_config['level'].upper(), logging.INFO)

# 配置日志
logging.basicConfig(
    level=log_level,  # 从配置文件读取日志级别
    format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

print(f"日志级别设置为: {log_config['level']} ({log_level})")

# 导入UUID日志工具并配置
try:
    from utils.logging_utils import configure_root_logger_with_uuid, UUIDLogger
    # 配置根日志器以支持UUID
    uuid_filter = configure_root_logger_with_uuid()
    print("UUID日志系统配置成功")
except ImportError as e:
    print(f"UUID日志工具导入失败: {e}")
    uuid_filter = None

# 设置第三方库日志级别 - 根据配置的日志级别调整
# 如果配置的是DEBUG级别，第三方库使用INFO；否则使用WARNING
third_party_level = logging.INFO if log_level == logging.DEBUG else logging.WARNING

for logger_name in ['pika', 'pika.adapters', 'pika.connection', 'pika.channel', 'urllib3', 'requests', 'ppocr', 'paddleocr']:
    logging.getLogger(logger_name).setLevel(third_party_level)

# 特别处理ultralytics的日志
ultralytics_logger = logging.getLogger('ultralytics')
ultralytics_logger.setLevel(third_party_level)

print(f"第三方库日志级别设置为: {logging.getLevelName(third_party_level)}")

logger = logging.getLogger(__name__)

# 导入处理器和服务
try:
    from core.processors.ocr_processor import OCRProcessor
    from core.processors.video_processor import VideoProcessor
    from utils.video_utils import create_video_from_frames
    from utils.cuda_utils import safe_cuda_empty_cache

    logger.info("基础处理器导入成功")
except ImportError as e:
    logger.error(f"基础处理器导入失败: {e}")
    logger.exception("基础处理器导入详细错误:")
    sys.exit(1)

# 增强模式处理器暂不可用
enhanced_mode_available = False
logger.info("使用基础模式处理器")

# 增强处理器相关导入已移除

try:
    from core.managers.config_manager import ConfigManager
    from core.managers.task_manager import TaskManager
    from core.managers.event_manager import EventManager

    logger.info("管理器导入成功")
except ImportError as e:
    logger.error(f"管理器导入失败: {e}")
    logger.exception("管理器导入详细错误:")
    sys.exit(1)

try:
    from services.video_service import VideoService
    from services.label_service import LabelService

    logger.info("服务导入成功")
except ImportError as e:
    logger.error(f"服务导入失败: {e}")
    logger.exception("服务导入详细错误:")
    sys.exit(1)

logger.info("所有模块导入成功")


# 内存监控类
class MemoryMonitor:
    def __init__(self, threshold_mb=800):
        self.process = psutil.Process()
        self.threshold_mb = threshold_mb
        self.initial_memory = self.get_memory_usage()

    def get_memory_usage(self):
        return self.process.memory_info().rss / 1024 / 1024

    def check_and_cleanup(self):
        current_memory = self.get_memory_usage()
        if current_memory > self.threshold_mb:
            logger.info(f"内存使用超过阈值 ({current_memory:.1f}MB > {self.threshold_mb}MB)，执行垃圾回收...")
            gc.collect()
            # 清理PyTorch缓存
            safe_cuda_empty_cache()
            new_memory = self.get_memory_usage()
            logger.info(f"垃圾回收后内存: {new_memory:.1f}MB")
        return current_memory


# 应用状态管理
class ApplicationState:
    def __init__(self):
        """初始化统一应用状态"""
        # 环境配置
        self.environment = os.getenv('ENVIRONMENT', 'dev')
        self.video_synthesis_method = 'opencv' if self.environment == 'dev' else 'ffmpeg'
        logger.info(f"环境配置: {self.environment}, 视频合成方法: {self.video_synthesis_method}")

        # 基础处理器
        self.ocr_processor = None
        self.video_processor = None

        # 增强处理器相关变量已移除

        # 高级服务
        self.config = None
        self.task_manager = None
        self.event_manager = None
        self.label_service = None
        self.video_service = None

        # 状态标志
        self.initialized = False
        self.tracking_enabled = True

        # 初始化状态
        self._model_initialized = False
        self._model_failed = False
        self._initialization_attempts = 0
        self._max_attempts = 3

    def safe_initialize_processors(self):
        """安全初始化所有处理器"""
        try:
            logger.info("开始初始化处理器...")

            gc.collect()

            # 初始化OCR处理器
            logger.info("初始化OCR处理器...")
            self.ocr_processor = OCRProcessor()
            logger.info("OCR处理器初始化成功")

            # 初始化视频处理器（包含内置的检测功能）
            logger.info("初始化视频处理器...")
            self._fallback_to_basic_video()

            self._model_initialized = True
            logger.info("所有处理器初始化完成")
            return True

        except Exception as e:
            logger.error(f"处理器初始化失败: {e}")
            logger.exception("详细错误信息:")
            return False



    def _fallback_to_basic_video(self):
        """初始化基础视频处理器"""
        try:
            # VideoProcessor 现在支持内置的 YOLOv8 MOT 功能
            self.video_processor = VideoProcessor(
                config=None, # 使用内置配置
                ocr_processor=self.ocr_processor,
                gas_leakage_mode=True # 启用气体泄漏追踪模式
            )
            logger.info("使用基础视频处理器（内置YOLOv8 MOT功能）")
        except Exception as e:
            logger.error(f"基础视频处理器初始化失败: {e}")
            raise

    def select_processor(self, task_type: str = 'general'):
        """
        根据任务类型选择最合适的处理器

        Args:
            task_type: 任务类型 ('gas_leakage', 'general_detection', 'video_analysis')

        Returns:
            选中的处理器实例
        """
        logger.info("选择视频处理器")
        return self.video_processor

    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            'environment': self.environment,
            'video_synthesis_method': self.video_synthesis_method,
            'tracking_enabled': self.tracking_enabled,
            'processors': {
                'video_processor': self.video_processor is not None,
                'ocr_processor': self.ocr_processor is not None
            }
        }

    def initialize_advanced_services(self):
        """初始化高级服务"""
        try:
            logger.info("开始初始化高级服务...")

            if not all([self.ocr_processor, self.video_processor]):
                logger.error("基础处理器未初始化，无法启动高级服务")
                return False

            # 初始化配置管理器
            logger.info("初始化配置管理器...")
            self.config = ConfigManager()
            logger.info("配置管理器初始化成功")

            # 初始化任务管理器
            logger.info("初始化任务管理器...")
            self.task_manager = TaskManager()
            logger.info("任务管理器初始化成功")

            # 初始化事件管理器
            logger.info("初始化事件管理器...")
            self.event_manager = EventManager(self.config)
            logger.info("事件管理器初始化成功")

            # 初始化标签服务
            logger.info("初始化标签服务...")
            self.label_service = LabelService()
            logger.info("标签服务初始化成功")

            # 初始化视频服务
            logger.info("初始化视频服务...")
            self.video_service = VideoService(
                config=self.config,
                label_service=self.label_service,
                ocr_processor=self.ocr_processor
            )
            logger.info("视频服务初始化成功")

            logger.info("系统运行在基础模式 - 所有服务已启用")
            return True

        except Exception as e:
            logger.warning(f"高级服务初始化失败: {e}")
            logger.exception("详细错误信息:")
            return False

    def cleanup_resources(self):
        """清理资源"""
        try:
            processors = [
                self.ocr_processor,
                self.video_processor
            ]

            for processor in processors:
                if processor and hasattr(processor, 'cleanup'):
                    processor.cleanup()

            gc.collect()
            safe_cuda_empty_cache()
            logger.info("统一资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")


# 全局实例
memory_monitor = MemoryMonitor()
app_state = ApplicationState()
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 2000 * 1024 * 1024 # 500MB


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在清理资源...")
    app_state.cleanup_resources()
    sys.exit(0)


# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


# ==================== API端点 ====================

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        memory_usage = memory_monitor.get_memory_usage()

        # 基础状态
        status = {
            'status': 'healthy',
            'memory_usage_mb': round(memory_usage, 1),
            'processors_initialized': all([
                app_state.ocr_processor,
                app_state.video_processor
            ]),
            'tracking_enabled': app_state.tracking_enabled
        }

        # 高级服务状态
        advanced_services = all([
            app_state.config,
            app_state.task_manager,
            app_state.event_manager,
            app_state.label_service,
            app_state.video_service
        ])

        status['advanced_services'] = advanced_services

        # RabbitMQ基本状态检查
        if app_state.event_manager:
            try:
                # 检查连接状态
                pub_connected = (app_state.event_manager.publisher_connection and
                                 not app_state.event_manager.publisher_connection.is_closed)
                con_connected = (app_state.event_manager.consumer_connection and
                                 not app_state.event_manager.consumer_connection.is_closed)
                status['rabbitmq_health'] = {
                    'publisher_connected': pub_connected,
                    'consumer_connected': con_connected,
                    'basic_health': pub_connected and con_connected
                }
            except Exception as e:
                status['rabbitmq_health'] = {'error': str(e)}

        # 处理器状态
        status['processor_features'] = {
            'yolov8_mot': enhanced_mode_available,
            'video_processing': app_state.video_processor is not None,
            'tracking_available': app_state.tracking_enabled
        }

        # 模式描述
        mode_info = {
            'mode': '基础模式',
            'features': '基础检测 + 视频处理'
        }
        status['mode_info'] = mode_info

        return jsonify(status)

    except Exception as e:
        logger.error(f'Health check failed: {str(e)}', exc_info=True)
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500


@app.route('/memory', methods=['GET'])
def memory_status():
    """内存状态端点"""
    try:
        memory_usage = memory_monitor.check_and_cleanup()
        return jsonify({
            'current_memory_mb': round(memory_usage, 1),
            'threshold_mb': memory_monitor.threshold_mb,
            'initial_memory_mb': round(memory_monitor.initial_memory, 1)
        })
    except Exception as e:
        logger.error(f'Memory status failed: {str(e)}', exc_info=True)
        return jsonify({'error': str(e)}), 500


@app.route('/rabbitmq-status', methods=['GET'])
def rabbitmq_status():
    """RabbitMQ连接状态和基本信息"""
    try:
        if not app_state.event_manager:
            return jsonify({'error': 'Event manager not initialized'}), 503

        # 基本连接状态检查
        pub_connected = (app_state.event_manager.publisher_connection and
                         not app_state.event_manager.publisher_connection.is_closed)
        con_connected = (app_state.event_manager.consumer_connection and
                         not app_state.event_manager.consumer_connection.is_closed)

        status_data = {
            'publisher_connected': pub_connected,
            'consumer_connected': con_connected,
            'overall_health': pub_connected and con_connected,
            'config': {
                'host': app_state.event_manager.config.rabbitmq['host'],
                'port': app_state.event_manager.config.rabbitmq['port'],
                'virtual_host': app_state.event_manager.config.rabbitmq['virtual_host']
            }
        }

        return jsonify({
            'status': 'success',
            'rabbitmq': status_data,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f'RabbitMQ status check failed: {str(e)}', exc_info=True)
        return jsonify({'error': str(e)}), 500


@app.route('/predict', methods=['POST'])
def predict():
    """预测端点"""
    try:
        # 检查处理器状态
        if not all([app_state.ocr_processor, app_state.video_processor]):
            return jsonify({'error': '处理器未初始化'}), 500

        if 'video' not in request.files:
            return jsonify({'error': '未找到视频文件'}), 400

        video_file = request.files['video']
        if video_file.filename == '':
            return jsonify({'error': '未选择文件'}), 400

        # 保存上传的视频文件
        filename = secure_filename(video_file.filename)
        temp_dir = tempfile.mkdtemp()
        input_path = os.path.join(temp_dir, filename)
        video_file.save(input_path)

        logger.info(f"开始预测分析视频: {filename}")
        start_time = datetime.now()

        # 分析视频
        result = app_state.video_processor.analyze_video(input_path)

        processing_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"预测分析完成，耗时: {processing_time:.2f}秒")

        # 清理临时文件
        os.unlink(input_path)
        os.rmdir(temp_dir)

        # 检查内存
        memory_usage = memory_monitor.check_and_cleanup()
        logger.info(f"预测完成，当前内存使用: {memory_usage:.1f}MB")

        return jsonify({
            'status': 'success',
            'processing_time': processing_time,
            'result': result
        })

    except Exception as e:
        logger.error(f"预测过程中出错: {e}")
        logger.exception("详细错误信息:")
        return jsonify({'error': str(e)}), 500


@app.route('/process-video', methods=['POST'])
def process_video():
    # 生成请求UUID用于日志追踪
    request_uuid = str(uuid.uuid4())
    
    # 设置UUID到日志系统
    if uuid_filter:
        uuid_filter.set_uuid(request_uuid)
    
    try:
        logger.info(f"开始处理视频请求 - UUID: {request_uuid}")
        result = process_video_json()
        logger.info(f"视频请求处理完成 - UUID: {request_uuid}")
        return result
    except Exception as e:
        logger.error(f'视频处理端点错误: {str(e)}', exc_info=True)
        return jsonify(
            {'status': 'error', 'message': f'Error: {str(e)}', 'detailed_error': str(type(e).__name__), 'uuid': request_uuid}), 500
    finally:
        # 清除UUID
        if uuid_filter:
            uuid_filter.clear_uuid()


def process_video_json():
    """处理JSON格式的视频请求（高级服务）"""
    data = request.get_json()

    if not data:
        return jsonify({
            'status': 'error',
            'message': 'No JSON data provided'
        }), 400

    # 获取当前请求的UUID
    from utils.logging_utils import get_current_uuid
    request_uuid = get_current_uuid()
    
    # 检查气体泄漏模式参数
    # 首先从配置文件读取默认值，然后从请求数据中覆盖
    config_default = getattr(app_state.config, 'gas_leakage_mode', True) # 配置文件默认为True
    gas_leakage_mode = data.get('gasLeakageMode', data.get('gas_leakage_mode', config_default))
    if gas_leakage_mode:
        logger.info(f"接收到气体泄漏追踪请求 - 任务ID: {data.get('taskId', 'unknown')}")

    # 处理标签配置
    label_config = data.pop('label_config', None)

    # 验证标签配置（如果label_service可用）
    if label_config and app_state.label_service:
        if not app_state.label_service.validate_config(label_config):
            return jsonify({
                'status': 'error',
                'message': 'Invalid label configuration',
                'details': 'Please check the label levels, colors, thresholds, and notification priorities'
            }), 400

        logger.info(f"Received custom label config for task {data.get('taskId', 'unknown')}")
    else:
        logger.info(f"No label config provided for task {data.get('taskId', 'unknown')}, will use default")

    # 创建任务配置（如果label_service可用）
    if 'taskId' in data and app_state.label_service:
        created_config = app_state.label_service.create_task_config(data['taskId'], label_config)
        logger.info(f"Task config created: {created_config}")

    return app_state.video_service.process_video_request(data, request_uuid=request_uuid)



# ==================== 完整模式服务端点 ====================

@app.route('/process-image', methods=['POST'])
def process_image():
    """处理图片的API端点"""
    if not app_state.video_service:
        return jsonify({'error': '高级服务未初始化'}), 503

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No JSON data provided'
            }), 400

        return app_state.video_service.process_image_request(data)

    except Exception as e:
        logger.error(f'Error in process_image endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/tasks', methods=['GET'])
def get_tasks():
    """获取任务列表"""
    if not app_state.task_manager:
        return jsonify({'error': '任务管理器未初始化'}), 503

    try:
        user_id = request.args.get('user_id')
        if user_id:
            tasks = app_state.task_manager.get_user_tasks(user_id)
        else:
            tasks = app_state.task_manager.get_all_tasks()

        return jsonify({
            'status': 'success',
            'tasks': tasks,
            'count': len(tasks)
        })

    except Exception as e:
        logger.error(f'Error in get_tasks endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/tasks/<task_id>', methods=['GET'])
def get_task_progress(task_id):
    """获取特定任务的进度"""
    if not app_state.task_manager:
        return jsonify({'error': '任务管理器未初始化'}), 503

    try:
        progress = app_state.task_manager.get_task_progress(task_id)
        if progress:
            return jsonify({
                'status': 'success',
                'task': progress
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Task not found'
            }), 404

    except Exception as e:
        logger.error(f'Error in get_task_progress endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/label-config/validate', methods=['POST'])
def validate_label_config():
    """验证标签配置的有效性"""
    if not app_state.label_service:
        return jsonify({'error': '标签服务未初始化'}), 503

    try:
        config_data = request.get_json()
        if not config_data:
            return jsonify({
                'status': 'error',
                'message': 'No configuration data provided'
            }), 400

        is_valid = app_state.label_service.validate_config(config_data)

        if is_valid:
            return jsonify({
                'status': 'success',
                'message': 'Configuration is valid',
                'level_count': len(config_data.get('label_levels', {}))
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Configuration validation failed'
            }), 400

    except Exception as e:
        logger.error(f'Error in validate_label_config endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/label-config/tasks/<task_id>', methods=['GET'])
def get_task_label_config(task_id):
    """获取特定任务的标签配置"""
    if not app_state.label_service:
        return jsonify({'error': '标签服务未初始化'}), 503

    try:
        levels = app_state.label_service.get_task_levels(task_id)
        return jsonify({
            'status': 'success',
            'task_id': task_id,
            'label_levels': levels
        })

    except Exception as e:
        logger.error(f'Error in get_task_label_config endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/label-config/tasks/<task_id>', methods=['PUT'])
def update_task_label_config(task_id):
    """更新特定任务的标签配置"""
    if not app_state.label_service:
        return jsonify({'error': '标签服务未初始化'}), 503

    try:
        config_data = request.get_json()
        if not config_data:
            return jsonify({
                'status': 'error',
                'message': 'No configuration data provided'
            }), 400

        # 验证配置
        if not app_state.label_service.validate_config(config_data):
            return jsonify({
                'status': 'error',
                'message': 'Invalid label configuration'
            }), 400

        # 更新配置
        config = app_state.label_service.create_task_config(task_id, config_data)

        return jsonify({
            'status': 'success',
            'message': f'Label configuration updated for task {task_id}',
            'config_summary': str(config)
        })

    except Exception as e:
        logger.error(f'Error in update_task_label_config endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/label-config/default', methods=['GET'])
def get_default_label_config():
    """获取默认标签配置"""
    if not app_state.label_service:
        return jsonify({'error': '标签服务未初始化'}), 503

    try:
        default_config = app_state.label_service.get_default_label_config()
        return jsonify({
            'status': 'success',
            'default_config': default_config.to_dict(),
            'description': 'This is the default label configuration used when no custom config is provided'
        })

    except Exception as e:
        logger.error(f'Error in get_default_label_config endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


@app.route('/label-config/tasks', methods=['GET'])
def list_task_configs():
    """列出所有活跃任务的标签配置"""
    if not app_state.label_service:
        return jsonify({'error': '标签服务未初始化'}), 503

    try:
        active_tasks = app_state.label_service.get_active_tasks()
        task_configs = {}
        for task_id in active_tasks:
            task_configs[task_id] = app_state.label_service.get_task_levels(task_id)

        return jsonify({
            'status': 'success',
            'active_tasks': len(active_tasks),
            'task_configs': task_configs
        })

    except Exception as e:
        logger.error(f'Error in list_task_configs endpoint: {str(e)}', exc_info=True)
        return jsonify({'status': 'error', 'message': 'Internal server error'}), 500


# ==================== 增强功能API端点 ====================
# 注意：增强功能已集成到 /process-video 端点中，通过 gasLeakageMode 参数启用

# 注意：追踪配置和统计功能已集成到主要接口中，通过gasLeakageMode参数控制

# 注意：追踪器重置功能已集成到视频处理流程中，每次处理时自动重置

# ==================== 应用启动 ====================

if __name__ == '__main__':
    try:
        logger.info("启动超级安全模式预测服务...")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"OpenCV版本: {cv2.__version__}")
        logger.info(f"初始内存使用: {memory_monitor.initial_memory:.1f}MB")

        # 再次确认线程安全设置
        if hasattr(torch, 'set_num_threads'):
            torch.set_num_threads(1)
        cv2.setNumThreads(1)
        logger.info("线程安全设置确认完成")

        # 初始化基础处理器（必需）
        if not app_state.safe_initialize_processors():
            logger.error("基础处理器初始化失败，退出程序")
            sys.exit(1)

        # 初始化高级服务（必需，不再是可选）
        logger.info("初始化高级服务（完整模式必需）...")
        advanced_services = app_state.initialize_advanced_services()
        if advanced_services:
            logger.info("高级服务初始化成功 - 完整模式已启用")
        else:
            logger.error("高级服务初始化失败")
            logger.error("用户要求完整模式，无法使用基础模式，退出程序")
            logger.error("请检查环境配置、依赖库或系统资源后重新启动")
            sys.exit(1)

        # 检查内存
        memory_usage = memory_monitor.check_and_cleanup()
        logger.info(f"初始化完成，当前内存使用: {memory_usage:.1f}MB")

        logger.info("预测服务启动成功！（完整模式）")
        logger.info("访问 http://localhost:9020/health 检查服务状态")
        logger.info("完整模式端点列表:")
        logger.info(" - GET /health - 健康检查")
        logger.info(" - GET /memory - 内存状态")
        logger.info(" - POST /predict - 视频预测分析")
        logger.info(" - POST /process-video - 视频处理（支持JSON和文件上传，气体泄漏模式通过gasLeakageMode参数启用）")
        logger.info(" - POST /process-image - 图片处理")
        logger.info(" - GET /tasks - 任务列表")
        logger.info(" - GET /tasks/<id> - 任务进度")
        logger.info(" - POST /label-config/validate - 验证标签配置")
        logger.info(" - GET /label-config/default - 获取默认标签配置")
        logger.info(" - GET /label-config/tasks - 列出任务配置")
        logger.info(" - GET /label-config/tasks/<id> - 获取任务标签配置")
        logger.info(" - PUT /label-config/tasks/<id> - 更新任务标签配置")

        # 使用安全的Flask启动参数
        logger.info("启动Flask应用 (超级安全模式)...")
        app.run(
            host='0.0.0.0',
            port=9020,
            debug=True, # 开启Debug模式
            threaded=True,
            use_reloader=False, # 禁用重载器避免多进程问题
            processes=1 # 单进程模式
        )

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        logger.exception("详细错误信息:")
        # 启动失败时也清理资源
        try:
            app_state.cleanup_resources()
        except Exception as cleanup_error:
            logger.error(f"清理资源时出错: {cleanup_error}")
        sys.exit(1)
    finally:
        # 确保资源总是被清理
        try:
            app_state.cleanup_resources()
            logger.info("服务已安全关闭")
        except Exception as e:
            logger.error(f"最终清理时出错: {e}")

        # 最后的内存清理
        try:
            gc.collect()
            safe_cuda_empty_cache()
        except Exception:
            pass
