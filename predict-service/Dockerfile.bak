FROM nvidia/cuda:11.8.0-cudnn8-devel-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=America/New_York

# 设置应用环境变量
ENV ENVIRONMENT=production
ENV ENABLE_CUDA=true
ENV MAX_WORKERS=16
ENV PYTHONUNBUFFERED=1

# 安装必要的软件包和视频编解码器支持
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    python3.10-venv \
    mesa-utils \
    libglib2.0-0 \
    libblas-dev \
    libopenblas-dev \
    coreutils \
    util-linux \
    # 视频处理相关依赖
    ffmpeg \
    libx264-dev \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libswresample-dev \
    libavfilter-dev \
    libavdevice-dev \
    # OpenCV依赖
    libgtk-3-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libgstreamer-plugins-bad1.0-dev \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    # 额外编解码器支持
    x264 \
    x265 \
    libopencore-amrnb-dev \
    libopencore-amrwb-dev \
    libtheora-dev \
    libvorbis-dev \
    libxvidcore-dev \
    libx265-dev \
    yasm \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 创建并激活虚拟环境
RUN python3.10 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 设置环境变量
ENV PATH=/usr/local/cuda/bin:/usr/local/bin:$PATH
ENV LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH

WORKDIR /app

# 复制 requirements 文件
COPY requirements.txt /app/

# 安装 Python 依赖
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 创建必要的目录
RUN mkdir -p /app/video_cache /app/logs /app/output && \
    chmod 777 /app/video_cache /app/logs /app/output

# 复制应用程序代码
COPY . /app

# 安装 dos2unix 并转换脚本格式
RUN apt-get update && apt-get install -y dos2unix && \
    dos2unix /app/start.sh && \
    chmod +x /app/start.sh

# 启动命令
CMD ["/app/start.sh"]
