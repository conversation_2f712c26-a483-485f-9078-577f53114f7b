from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import requests
import json
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)
router = APIRouter()

# 数据模型
class GasComponent(BaseModel):
    type: str
    name: str
    ratio: float
    enabled: bool

class EnvironmentalParams(BaseModel):
    windSpeed: float
    windDirection: float
    temperature: float
    humidity: float
    pressure: float

class GasDetectionParams(BaseModel):
    expectedGases: List[GasComponent]
    sensitivity: str
    confidenceThreshold: int

class SafetyParams(BaseModel):
    safetyDistance: float
    alertThreshold: float
    toxicityLevel: str
    emergencyContact: str

class CFDSimulationParams(BaseModel):
    domainSize: List[float]
    gridResolution: str
    maxIterations: int
    convergenceTolerance: float
    priority: str

class OGIParameters(BaseModel):
    environmental: EnvironmentalParams
    gasDetection: GasDetectionParams
    safety: SafetyParams
    cfdSimulation: CFDSimulationParams

class OGIAnalysisRequest(BaseModel):
    videoPath: str
    parameters: OGIParameters
    taskId: str
    userId: Optional[int] = None
    organizationId: Optional[int] = None

class OGIAnalysisResponse(BaseModel):
    taskId: str
    status: str
    message: str

# 模拟OGI检测服务
class OGIDetectionService:
    async def extract_video_frames(self, video_path: str):
        """提取视频帧"""
        # 模拟帧提取
        logger.info(f"Extracting frames from: {video_path}")
        return ["frame1", "frame2", "frame3"] # 模拟帧数据

    async def detect_gas_leakage(self, frames: List[str], gas_params: GasDetectionParams):
        """检测气体泄漏"""
        logger.info("Detecting gas leakage in frames")

        # 模拟检测结果
        enabled_gases = [gas for gas in gas_params.expectedGases if gas.enabled]

        return {
            "gases": [
                {
                    "type": gas.type,
                    "name": gas.name,
                    "concentration": gas.ratio * 100, # 模拟浓度
                    "confidence": 0.85 + (gas.ratio / 100) * 0.1
                }
                for gas in enabled_gases
            ],
            "leakage_points": [
                {
                    "x": 100.0,
                    "y": 200.0,
                    "z": 10.0,
                    "emission_rate": 0.001,
                    "gas_type": enabled_gases[0].type if enabled_gases else "unknown",
                    "confidence": 0.8
                }
            ],
            "confidence": 0.85,
            "regions": ["region1", "region2"],
            "processed_frames": len(frames),
            "detection_rate": 0.6
        }

    async def test_detection(self, video_path: str, gas_types: List[str]):
        """测试检测功能"""
        return {
            "gases": [{"type": gas_type, "confidence": 0.8} for gas_type in gas_types],
            "confidence": 0.8,
            "processing_time": 2.5
        }

# 创建OGI检测服务实例
ogi_service = OGIDetectionService()

@router.post("/analyze-video", response_model=OGIAnalysisResponse)
async def analyze_ogi_video(
    request: OGIAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """
    分析OGI视频并进行气体检测
    """
    try:
        logger.info(f"Starting OGI video analysis for task: {request.taskId}")

        # 验证请求参数
        if not request.videoPath or not request.parameters:
            raise HTTPException(status_code=400, detail="Missing required parameters")

        # 开始后台视频分析任务
        background_tasks.add_task(
            process_ogi_video_async,
            request
        )

        return OGIAnalysisResponse(
            taskId=request.taskId,
            status="STARTED",
            message="OGI video analysis started successfully"
        )

    except Exception as e:
        logger.error(f"Error starting OGI analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def process_ogi_video_async(request: OGIAnalysisRequest):
    """
    异步处理OGI视频分析
    """
    try:
        logger.info(f"Processing OGI video: {request.videoPath}")

        # 1. 视频预处理和帧提取
        frames = await ogi_service.extract_video_frames(request.videoPath)
        logger.info(f"Extracted {len(frames)} frames from video")

        # 2. AI气体检测
        detection_results = await ogi_service.detect_gas_leakage(
            frames,
            request.parameters.gasDetection
        )

        # 3. 构造检测结果
        gas_detection_result = {
            "taskId": request.taskId,
            "detectedGases": detection_results.get("gases", []),
            "leakagePoints": detection_results.get("leakage_points", []),
            "confidenceScore": detection_results.get("confidence", 0.0),
            "detectionRegions": len(detection_results.get("regions", [])),
            "videoAnalysis": {
                "totalFrames": len(frames),
                "processedFrames": detection_results.get("processed_frames", 0),
                "detectionRate": detection_results.get("detection_rate", 0.0)
            }
        }

        # 4. 调用predict-open-foam进行CFD仿真
        cfd_result = await call_predict_openfoam(request, gas_detection_result)

        # 5. 合并结果并返回
        final_result = {
            "gasDetection": gas_detection_result,
            "cfdSimulation": cfd_result,
            "status": "COMPLETED",
            "completedAt": datetime.now().isoformat()
        }

        logger.info(f"OGI analysis completed for task: {request.taskId}")
        return final_result

    except Exception as e:
        logger.error(f"Error in OGI video processing: {str(e)}")
        return {
            "taskId": request.taskId,
            "status": "FAILED",
            "error": str(e)
        }


async def call_predict_openfoam(request: OGIAnalysisRequest, gas_detection_result: Dict[str, Any]):
    """
    调用predict-open-foam进行CFD仿真
    """
    try:
        logger.info(f"Calling predict-open-foam for task: {request.taskId}")

        # 构造CFD仿真请求
        cfd_request = {
            "taskId": request.taskId,
            "gasDetectionResult": gas_detection_result,
            "environmentalParams": {
                "windSpeed": request.parameters.environmental.windSpeed,
                "windDirection": request.parameters.environmental.windDirection,
                "temperature": request.parameters.environmental.temperature,
                "humidity": request.parameters.environmental.humidity,
                "pressure": request.parameters.environmental.pressure
            },
            "safetyParams": {
                "safetyDistance": request.parameters.safety.safetyDistance,
                "alertThreshold": request.parameters.safety.alertThreshold,
                "toxicityLevel": request.parameters.safety.toxicityLevel,
                "emergencyContact": request.parameters.safety.emergencyContact
            },
            "cfdParams": {
                "domainSize": request.parameters.cfdSimulation.domainSize,
                "gridResolution": request.parameters.cfdSimulation.gridResolution,
                "maxIterations": request.parameters.cfdSimulation.maxIterations,
                "convergenceTolerance": request.parameters.cfdSimulation.convergenceTolerance,
                "priority": request.parameters.cfdSimulation.priority
            }
        }

        # 调用predict-open-foam服务
        predict_openfoam_url = "http://localhost:5000/api/simulations"

        response = requests.post(
            predict_openfoam_url,
            json=cfd_request,
            headers={"Content-Type": "application/json"},
            timeout=300 # 5分钟超时
        )

        if response.status_code == 200:
            cfd_result = response.json()
            logger.info(f"CFD simulation completed for task: {request.taskId}")
            return cfd_result
        else:
            logger.error(f"CFD service error: {response.status_code} - {response.text}")
            return {
                "status": "FAILED",
                "error": f"CFD service error: {response.status_code}"
            }

    except requests.RequestException as e:
        logger.error(f"Error calling predict-open-foam: {str(e)}")
        return {
            "status": "FAILED",
            "error": f"CFD service unavailable: {str(e)}"
        }
    except Exception as e:
        logger.error(f"Unexpected error in CFD call: {str(e)}")
        return {
            "status": "FAILED",
            "error": f"Unexpected error: {str(e)}"
        }


@router.get("/analysis/{task_id}/status")
async def get_analysis_status(task_id: str):
    """
    获取分析任务状态
    """
    try:
        return {
            "taskId": task_id,
            "status": "RUNNING",
            "progress": 75,
            "message": "CFD simulation in progress"
        }
    except Exception as e:
        logger.error(f"Error getting analysis status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-gas-detection")
async def test_gas_detection(video_path: str, gas_types: List[str]):
    """
    测试气体检测功能
    """
    try:
        logger.info(f"Testing gas detection for video: {video_path}")

        result = await ogi_service.test_detection(video_path, gas_types)

        return {
            "status": "SUCCESS",
            "detectedGases": result.get("gases", []),
            "confidence": result.get("confidence", 0.0),
            "processingTime": result.get("processing_time", 0.0)
        }

    except Exception as e:
        logger.error(f"Error in test gas detection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))