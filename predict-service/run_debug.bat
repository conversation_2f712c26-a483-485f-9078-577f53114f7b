@echo off
chcp 65001
echo 🚀 启动增强调试模式的 Python 应用程序
echo ===============================================

REM 设置时间戳
set datetime=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set datetime=%datetime: =0%

echo 📅 启动时间: %datetime%
echo 📁 当前目录: %cd%

REM 🚀 步骤1: 设置 Python 故障处理器环境变量
echo 🔍 设置 Python 调试环境变量...
set PYTHONFAULTHANDLER=1
set PYTHONDEBUG=1
set PYTHONVERBOSE=1
set PYTHONASYNCIODEBUG=1
set PYTHONDEV=1
echo ✅ Python 调试变量已设置

REM 🚀 步骤2: 设置 CUDA 和 GPU 相关环境变量 (禁用以避免冲突)
echo 🔧 禁用 CUDA/GPU 以避免访问冲突...
set CUDA_VISIBLE_DEVICES=-1
set ENABLE_CUDA=false
set CUDA_LAUNCH_BLOCKING=1
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
echo ✅ CUDA 已禁用

REM 🚀 步骤3: 设置内存和多线程相关环境变量
echo 🧠 设置内存和多线程优化...
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set NUMBA_NUM_THREADS=1
set OPENBLAS_NUM_THREADS=1
set VECLIB_MAXIMUM_THREADS=1
set KMP_DUPLICATE_LIB_OK=TRUE
set MKL_THREADING_LAYER=sequential
echo ✅ 多线程冲突预防已设置

REM 🚀 步骤4: 设置 OpenCV 和视频处理相关环境变量
echo 🎬 设置 OpenCV 和视频处理环境...
set OPENCV_DNN_BACKEND_INFERENCE_ENGINE_TYPE=CPU
set OPENCV_DNN_TARGET=CPU
set OPENCV_LOG_LEVEL=ERROR
set FFMPEG_LOG_LEVEL=error
echo ✅ OpenCV 优化已设置

REM 🚀 步骤5: 设置内存调试相关环境变量
echo 🔍 设置内存调试环境...
set MALLOC_CHECK_=2
set PYTHONMALLOC=debug
set PYTHONMALLOCSTATS=1
echo ✅ 内存调试已启用

REM 🚀 步骤6: 设置 PaddlePaddle 相关环境变量
echo 🏊 设置 PaddlePaddle 环境...
set FLAGS_allocator_strategy=auto_growth
set FLAGS_fraction_of_gpu_memory_to_use=0.1
set FLAGS_eager_delete_tensor_gb=0.0
set CPU_NUM=1
echo ✅ PaddlePaddle 优化已设置

REM 🚀 步骤7: 显示所有关键环境变量
echo.
echo 📋 当前关键环境变量:
echo    PYTHONFAULTHANDLER: %PYTHONFAULTHANDLER%
echo    CUDA_VISIBLE_DEVICES: %CUDA_VISIBLE_DEVICES%
echo    OMP_NUM_THREADS: %OMP_NUM_THREADS%
echo    KMP_DUPLICATE_LIB_OK: %KMP_DUPLICATE_LIB_OK%
echo    ENABLE_CUDA: %ENABLE_CUDA%

REM 🚀 步骤8: 检查 Python 版本和依赖
echo.
echo 🐍 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python 未找到或无法运行
    pause
    exit /b 1
)

echo.
echo 📦 检查关键依赖包版本...
python -c "import torch; print(f'PyTorch: {torch.__version__}')" 2>nul || echo ⚠️  PyTorch 未安装
python -c "import cv2; print(f'OpenCV: {cv2.__version__}')" 2>nul || echo ⚠️  OpenCV 未安装
python -c "import numpy; print(f'NumPy: {numpy.__version__}')" 2>nul || echo ⚠️  NumPy 未安装

echo.
echo 🚀 启动调试增强版应用程序...
echo 📁 崩溃日志将保存到当前目录
echo 🔍 如果程序崩溃，请查看生成的 crash_*.log 文件
echo.

REM 🚀 步骤9: 运行调试版本的应用程序
python debug_app.py

REM 🚀 步骤10: 检查退出状态
set exit_code=%errorlevel%
echo.
echo 📊 程序退出代码: %exit_code%

if %exit_code% neq 0 (
    echo ❌ 程序异常退出 (代码: %exit_code%)
    echo 🔍 请检查以下文件获取更多信息:
    echo    - crash_*.log (C级别崩溃信息)
    echo    - debug_*.log (Python级别调试信息)
    echo.
    
    REM 显示最新的崩溃日志文件
    echo 📄 最新的日志文件:
    dir /b /o-d crash_*.log debug_*.log 2>nul | head -n 3
    
    echo.
    echo 💡 建议的下一步:
    echo    1. 查看上述日志文件的详细内容
    echo    2. 使用 Windows 事件查看器检查系统日志
    echo    3. 运行 "python -c \"import faulthandler; faulthandler.dump_traceback()\"" 测试 faulthandler
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
echo 按任意键退出...
pause >nul 