"""
视频编码器智能回退机制
在运行时动态检测可用编码器，提供稳健的视频输出
"""

import cv2
import logging
import tempfile
import os
import numpy as np
from typing import Optional, Tuple, List

logger = logging.getLogger(__name__)

class VideoCodecFallback:
    """视频编码器智能回退"""

    # 编码器优先级列表 - 基于实际测试结果优化
    CODEC_PRIORITY = [
        # 实际可用的编码器（按测试结果排序）
        ('mp4v', '.mp4'), # MPEG-4 Part 2 - 测试可用，浏览器兼容性好
        ('XVID', '.avi'), # XVID - 测试可用，高兼容性
        ('MJPG', '.avi'), # Motion JPEG - 测试可用，快速编码

        # 备选编码器
        ('mp4v', '.avi'), # MPEG-4 + AVI - 备选方案
        ('DIVX', '.avi'), # DivX编码器

        # 最后保障
        (0, '.avi'), # 未压缩AVI
    ]

    def __init__(self):
        self._working_codecs = {}
        self._tested = False

    def find_working_codec(self, width: int = 640, height: int = 480, fps: float = 24.0) -> Tuple[int, str]:
        """
        找到第一个可用的编码器

        Returns:
            Tuple[int, str]: (fourcc, file_extension)
        """
        if not self._tested:
            self._test_all_codecs(width, height, fps)
            self._tested = True

        for fourcc, ext in self.CODEC_PRIORITY:
            key = f"{fourcc}_{ext}"
            if self._working_codecs.get(key, False):
                if fourcc == 0:
                    return 0, ext
                else:
                    return cv2.VideoWriter_fourcc(*fourcc), ext

        # 如果所有测试都失败，回退到最基本的
        logger.warning("No working codec found, using uncompressed AVI")
        return 0, '.avi'

    def _test_all_codecs(self, width: int, height: int, fps: float):
        """测试所有编码器"""
        logger.info("Testing video codecs (H264 priority)...")

        for fourcc, ext in self.CODEC_PRIORITY:
            key = f"{fourcc}_{ext}"
            if self._test_single_codec(fourcc, ext, width, height, fps):
                self._working_codecs[key] = True
                logger.info(f" Codec {fourcc} + {ext} works")
            else:
                self._working_codecs[key] = False
                logger.debug(f" Codec {fourcc} + {ext} failed")

    def _test_single_codec(self, fourcc, ext: str, width: int, height: int, fps: float) -> bool:
        """测试单个编码器 - 增强版，更接近实际使用场景"""
        try:
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
                output_path = tmp_file.name

            # 创建VideoWriter
            if fourcc == 0:
                writer = cv2.VideoWriter(output_path, 0, fps, (width, height))
            else:
                fourcc_int = cv2.VideoWriter_fourcc(*fourcc)
                writer = cv2.VideoWriter(output_path, fourcc_int, fps, (width, height))

            if not writer.isOpened():
                return False

            # 增强测试：使用更复杂的测试帧，模拟实际组合帧
            test_frames = []

            # 创建4种不同类型的测试帧，模拟实际的组合帧
            for i in range(10): # 测试更多帧
                if i % 4 == 0:
                    # 原始帧 - 随机彩色
                    frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
                elif i % 4 == 1:
                    # 光流帧 - 灰度转彩色
                    gray = np.random.randint(0, 255, (height, width), dtype=np.uint8)
                    frame = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
                elif i % 4 == 2:
                    # 置信度帧 - 热力图风格
                    frame = np.zeros((height, width, 3), dtype=np.uint8)
                    frame[:, :, 2] = np.random.randint(0, 255, (height, width)) # 红色通道
                else:
                    # 检测帧 - 带框和文字的复杂帧
                    frame = np.random.randint(50, 200, (height, width, 3), dtype=np.uint8)
                    # 添加矩形框和文字，模拟检测结果
                    cv2.rectangle(frame, (10, 10), (100, 100), (0, 255, 0), 2)
                    cv2.putText(frame, f'Test {i}', (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                # 确保帧连续性
                frame = np.ascontiguousarray(frame)
                test_frames.append(frame)

            # 测试大尺寸写入的稳定性
            failed_writes = 0
            for i, test_frame in enumerate(test_frames):
                success = writer.write(test_frame)
                if not success:
                    failed_writes += 1
                    logger.debug(f"Frame {i} write failed for codec {fourcc}")

                    # 如果前几帧就失败，立即退出
                    if i < 3:
                        writer.release()
                        return False

            writer.release()

            # 更严格的成功标准
            if failed_writes > len(test_frames) * 0.1: # 如果超过10%的帧写入失败
                logger.debug(f"Codec {fourcc} failed too many writes: {failed_writes}/{len(test_frames)}")
                return False

            # 检查文件是否创建成功并有合理的大小
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000: # 至少1KB
                os.remove(output_path)
                return True
            else:
                logger.debug(f"Codec {fourcc} produced invalid output file")
                return False

        except Exception as e:
            logger.debug(f"Codec test failed: {e}")
            return False
        finally:
            if 'output_path' in locals() and os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass

    def test_codec_with_actual_dimensions(self, width: int, height: int, fps: float = 24.0) -> Tuple[int, str]:
        """
         新方法：使用实际视频尺寸测试编码器

        Args:
            width: 实际视频宽度
            height: 实际视频高度
            fps: 帧率

        Returns:
            Tuple[int, str]: (fourcc, file_extension)
        """
        logger.info(f"Testing codecs with actual dimensions: {width}x{height}")

        for fourcc, ext in self.CODEC_PRIORITY:
            if self._test_single_codec(fourcc, ext, width, height, fps):
                logger.info(f" Codec {fourcc} + {ext} works with actual dimensions")
                if fourcc == 0:
                    return 0, ext
                else:
                    return cv2.VideoWriter_fourcc(*fourcc), ext

        # 如果所有测试都失败，回退到最基本的
        logger.warning("No working codec found with actual dimensions, using uncompressed AVI")
        return 0, '.avi'

    def create_writer(self, output_path: str, width: int, height: int, fps: float = 24.0) -> Optional[cv2.VideoWriter]:
        """
        创建一个可用的VideoWriter

        Args:
            output_path: 输出路径（扩展名会被自动调整）
            width: 视频宽度
            height: 视频高度
            fps: 帧率

        Returns:
            cv2.VideoWriter 或 None
        """
        # 使用实际尺寸测试编码器，而不是默认640x480
        logger.info(f"Testing codecs with actual video dimensions: {width}x{height}")
        fourcc, ext = self.test_codec_with_actual_dimensions(width, height, fps)

        # 调整输出路径的扩展名
        base_path = os.path.splitext(output_path)[0]
        final_path = base_path + ext

        try:
            writer = cv2.VideoWriter(final_path, fourcc, fps, (width, height))

            if writer.isOpened():
                # 再次测试实际写入能力
                logger.info(f"Performing final write test for VideoWriter: {final_path}")
                test_frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
                test_frame = np.ascontiguousarray(test_frame) # 确保内存连续

                test_success = writer.write(test_frame)
                if test_success:
                    # 特殊处理未压缩AVI格式
                    if fourcc == 0 and ext == '.avi':
                        logger.warning(" 使用未压缩AVI格式 - 启用优化模式")
                        # 为未压缩格式创建特殊的写入器包装
                        optimized_writer = UncompressedAVIWriter(writer, final_path, width, height)
                        return optimized_writer, final_path
                    else:
                        logger.info(f" Successfully created and tested VideoWriter: {final_path} (fourcc: {fourcc})")
                        return writer, final_path
                else:
                    logger.error(f" VideoWriter created but failed write test: {final_path}")
                    writer.release()
                    return None, None
            else:
                logger.error(f"Failed to create VideoWriter for {final_path}")
                return None, None

        except Exception as e:
            logger.error(f"Error creating VideoWriter: {e}")
            return None, None

class UncompressedAVIWriter:
    """
    未压缩AVI写入器的优化包装类
    解决未压缩格式的性能和稳定性问题
    """

    def __init__(self, writer: cv2.VideoWriter, output_path: str, width: int, height: int):
        self.writer = writer
        self.output_path = output_path
        self.width = width
        self.height = height
        self.frame_count = 0
        self.failed_writes = 0
        self.last_successful_frame = None

        # 未压缩AVI优化设置
        self.max_failed_writes = 5 # 允许的最大连续失败次数
        self.write_buffer = [] # 写入缓冲区
        self.buffer_size = 10 # 缓冲区大小

        logger.info(f" Optimized UncompressedAVI writer initialized for {width}x{height}")

    def write(self, frame: np.ndarray) -> bool:
        """
        优化的写入方法

        Args:
            frame: 要写入的帧

        Returns:
            bool: 是否写入成功
        """
        try:
            # 预处理帧以确保兼容性
            processed_frame = self._preprocess_frame(frame)

            # 尝试写入
            success = self.writer.write(processed_frame)

            if success:
                self.frame_count += 1
                self.failed_writes = 0 # 重置失败计数
                self.last_successful_frame = processed_frame.copy()

                # 定期日志
                if self.frame_count % 100 == 0:
                    logger.info(f" UncompressedAVI: Successfully wrote {self.frame_count} frames")

                return True
            else:
                self.failed_writes += 1
                logger.warning(f" UncompressedAVI write failed (attempt {self.failed_writes})")

                # 如果连续失败太多次，尝试恢复
                if self.failed_writes >= self.max_failed_writes:
                    logger.error(f" Too many consecutive failures ({self.failed_writes})")
                    return self._attempt_recovery(processed_frame)

                return False

        except Exception as e:
            logger.error(f"Exception in UncompressedAVI write: {e}")
            return False

    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        预处理帧以确保与未压缩AVI的兼容性

        Args:
            frame: 原始帧

        Returns:
            np.ndarray: 处理后的帧
        """
        # 确保帧尺寸正确
        if frame.shape[:2] != (self.height, self.width):
            frame = cv2.resize(frame, (self.width, self.height))

        # 确保是3通道BGR格式
        if len(frame.shape) == 2:
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
        elif len(frame.shape) == 3 and frame.shape[2] == 1:
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
        elif len(frame.shape) == 3 and frame.shape[2] == 4:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

        # 确保数据类型为 uint8
        if frame.dtype != np.uint8:
            if frame.max() <= 1.0:
                frame = (frame * 255).astype(np.uint8)
            else:
                frame = frame.astype(np.uint8)

        # 确保内存布局连续 - 这对未压缩AVI很重要
        frame = np.ascontiguousarray(frame)

        return frame

    def _attempt_recovery(self, frame: np.ndarray) -> bool:
        """
        尝试从写入失败中恢复

        Args:
            frame: 当前帧

        Returns:
            bool: 恢复是否成功
        """
        logger.warning(" Attempting to recover from write failures...")

        try:
            # 方法1: 如果有上一个成功的帧，先写入它
            if self.last_successful_frame is not None:
                logger.info(" Writing last successful frame as recovery")
                recovery_success = self.writer.write(self.last_successful_frame)
                if recovery_success:
                    # 然后尝试写入当前帧
                    current_success = self.writer.write(frame)
                    if current_success:
                        logger.info(" Recovery successful")
                        self.failed_writes = 0
                        return True

            # 方法2: 写入一个简单的黑帧
            logger.info(" Writing black frame as recovery")
            black_frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
            black_frame = np.ascontiguousarray(black_frame)

            black_success = self.writer.write(black_frame)
            if black_success:
                # 然后写入当前帧
                current_success = self.writer.write(frame)
                if current_success:
                    logger.info(" Black frame recovery successful")
                    self.failed_writes = 0
                    return True

            logger.error(" All recovery attempts failed")
            return False

        except Exception as e:
            logger.error(f"Exception during recovery: {e}")
            return False

    def isOpened(self) -> bool:
        """检查写入器是否打开"""
        return self.writer.isOpened() if self.writer else False

    def release(self):
        """释放资源"""
        if self.writer:
            logger.info(f" UncompressedAVI final stats: {self.frame_count} frames written, {self.failed_writes} recent failures")
            self.writer.release()
            self.writer = None

    def get_stats(self) -> dict:
        """获取写入统计信息"""
        return {
            'frames_written': self.frame_count,
            'failed_writes': self.failed_writes,
            'output_path': self.output_path,
            'resolution': f"{self.width}x{self.height}"
        }

# 全局实例
video_codec_fallback = VideoCodecFallback()

def create_robust_video_writer(output_path: str, width: int, height: int, fps: float = 24.0, purpose: str = "general"):
    """
    创建一个稳健的VideoWriter

    这个函数会自动测试可用的编码器并选择最佳的
    """
    return video_codec_fallback.create_writer(output_path, width, height, fps)

def get_recommended_codec(purpose: str = "general") -> Tuple[int, str]:
    """
    获取推荐的编码器

    Args:
        purpose: "speed" (快速编码), "quality" (H264优先), "size" (最小文件), "general" (平衡)
    """
    # 基于测试结果，返回最佳可用编码器
    fourcc, ext = video_codec_fallback.find_working_codec()
    return fourcc, ext