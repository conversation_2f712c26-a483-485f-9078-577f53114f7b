#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
提供UUID日志支持和自定义日志格式化功能
"""

import logging
import threading
import uuid
from typing import Optional


class UUIDLogFilter(logging.Filter):
    """
    UUID日志过滤器
    为日志记录添加UUID支持，便于追踪特定请求的完整处理流程
    """
    
    def __init__(self):
        super().__init__()
        self._local = threading.local()
    
    def set_uuid(self, request_uuid: str) -> None:
        """
        设置当前线程的UUID
        
        Args:
            request_uuid: 请求的唯一标识符
        """
        self._local.uuid = request_uuid
    
    def get_uuid(self) -> Optional[str]:
        """
        获取当前线程的UUID
        
        Returns:
            当前线程的UUID，如果未设置则返回None
        """
        return getattr(self._local, 'uuid', None)
    
    def clear_uuid(self) -> None:
        """
        清除当前线程的UUID
        """
        if hasattr(self._local, 'uuid'):
            delattr(self._local, 'uuid')
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        为日志记录添加UUID字段
        
        Args:
            record: 日志记录对象
            
        Returns:
            总是返回True，允许所有日志记录通过
        """
        # 优先从实例获取UUID，如果没有则从全局线程本地存储获取
        uuid_value = self.get_uuid() or get_current_uuid()
        # 如果没有UUID，则生成一个新的UUID
        if not uuid_value:
            uuid_value = str(uuid.uuid4())
            # 将新生成的UUID设置到当前线程，以便后续日志使用相同的UUID
            self.set_uuid(uuid_value)
        record.uuid = uuid_value
        return True


class UUIDLogger:
    """
    UUID日志管理器
    提供便捷的UUID日志操作接口
    """
    
    def __init__(self, logger_name: str = __name__):
        """
        初始化UUID日志管理器
        
        Args:
            logger_name: 日志器名称
        """
        self.logger = logging.getLogger(logger_name)
        self.uuid_filter = UUIDLogFilter()
        
        # 为logger添加UUID过滤器
        self.logger.addFilter(self.uuid_filter)
    
    def generate_uuid(self) -> str:
        """
        生成新的UUID
        
        Returns:
            新生成的UUID字符串
        """
        return str(uuid.uuid4())
    
    def set_uuid(self, request_uuid: str) -> None:
        """
        设置当前请求的UUID
        
        Args:
            request_uuid: 请求的唯一标识符
        """
        self.uuid_filter.set_uuid(request_uuid)
    
    def get_uuid(self) -> Optional[str]:
        """
        获取当前请求的UUID
        
        Returns:
            当前请求的UUID
        """
        return self.uuid_filter.get_uuid()
    
    def clear_uuid(self) -> None:
        """
        清除当前请求的UUID
        """
        self.uuid_filter.clear_uuid()
    
    def info(self, message: str, *args, **kwargs) -> None:
        """
        记录INFO级别日志
        
        Args:
            message: 日志消息
            *args: 格式化参数
            **kwargs: 其他参数
        """
        self.logger.info(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        """
        记录ERROR级别日志
        
        Args:
            message: 日志消息
            *args: 格式化参数
            **kwargs: 其他参数
        """
        self.logger.error(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """
        记录WARNING级别日志
        
        Args:
            message: 日志消息
            *args: 格式化参数
            **kwargs: 其他参数
        """
        self.logger.warning(message, *args, **kwargs)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """
        记录DEBUG级别日志
        
        Args:
            message: 日志消息
            *args: 格式化参数
            **kwargs: 其他参数
        """
        self.logger.debug(message, *args, **kwargs)
    
    @classmethod
    def set_uuid(cls, uuid_value: str) -> None:
        """
        类方法：设置当前线程的UUID（用于异步线程）
        
        Args:
            uuid_value: 要设置的UUID值
        """
        set_current_uuid(uuid_value)
    
    @classmethod
    def clear_uuid(cls) -> None:
        """
        类方法：清除当前线程的UUID（用于异步线程）
        """
        clear_current_uuid()


def setup_uuid_logging(logger_name: str = None) -> UUIDLogger:
    """
    设置UUID日志系统
    
    Args:
        logger_name: 日志器名称，默认为调用模块名
        
    Returns:
        配置好的UUID日志管理器
    """
    if logger_name is None:
        logger_name = __name__
    
    return UUIDLogger(logger_name)


def configure_root_logger_with_uuid():
    """
    配置根日志器以支持UUID日志记录
    
    Returns:
        UUIDLogFilter: 创建的UUID过滤器实例
    """
    root_logger = logging.getLogger()
    
    # 创建UUID过滤器
    uuid_filter = UUIDLogFilter()
    
    # 添加过滤器到根日志器和所有handler
    root_logger.addFilter(uuid_filter)
    
    # 更新所有现有handler的格式以包含UUID，并添加过滤器
    for handler in root_logger.handlers:
        # 添加过滤器到handler（这是关键！）
        handler.addFilter(UUIDLogFilter())
        
        if hasattr(handler, 'setFormatter'):
            # 检查当前格式是否已包含uuid字段
            current_formatter = handler.formatter
            if current_formatter and hasattr(current_formatter, '_fmt'):
                current_format = current_formatter._fmt
                # 如果格式中不包含uuid，则更新格式
                if '%(uuid)s' not in current_format:
                    # 在现有格式中插入UUID字段
                    if '[%(levelname)7s]' in current_format:
                        new_format = current_format.replace(
                            '[%(levelname)7s]',
                            '[%(levelname)7s] [UUID:%(uuid)s]'
                        )
                    else:
                        # 如果找不到预期的格式，使用默认的UUID格式
                        new_format = '[%(asctime)s] [%(levelname)7s] [UUID:%(uuid)s] %(name)s:%(lineno)d - %(message)s'
                    
                    formatter = logging.Formatter(
                        new_format,
                        datefmt='%Y-%m-%d %H:%M:%S'
                    )
                    handler.setFormatter(formatter)
            else:
                # 如果没有现有格式，使用默认的UUID格式
                formatter = logging.Formatter(
                    '[%(asctime)s] [%(levelname)7s] [UUID:%(uuid)s] %(name)s:%(lineno)d - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                handler.setFormatter(formatter)
    
    return uuid_filter


# 创建线程本地存储
_thread_local = threading.local()


def get_current_uuid():
    """
    获取当前线程的UUID
    
    Returns:
        str: 当前线程的UUID，如果没有设置则返回None
    """
    return getattr(_thread_local, 'uuid', None)


def set_current_uuid(uuid_value):
    """
    设置当前线程的UUID
    
    Args:
        uuid_value: 要设置的UUID值
    """
    _thread_local.uuid = uuid_value


def clear_current_uuid():
    """
    清除当前线程的UUID
    """
    if hasattr(_thread_local, 'uuid'):
        delattr(_thread_local, 'uuid')