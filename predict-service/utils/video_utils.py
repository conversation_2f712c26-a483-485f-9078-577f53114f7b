#!/usr/bin/env python
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import os
from typing import List, Tuple, Optional
import logging
from typing import Any

logger = logging.getLogger(__name__)

def annotate_frame(frame: np.ndarray, text: str, position: Tuple[int, int],
                  font=cv2.FONT_HERSHEY_SIMPLEX, font_scale=1,
                  color=(255, 255, 255), thickness=2):
    """在帧上添加文本注释"""
    cv2.putText(frame, text, position, font, font_scale, color, thickness)

def compute_confidence_map(frame: np.ndarray, results: Any) -> np.ndarray:
    """计算置信度图"""
    confidence_map = np.zeros_like(frame)
    if results and hasattr(results, 'boxes'):
        boxes = results.boxes
        if boxes and boxes.conf is not None:
            confidences = boxes.conf.cpu().numpy()
            for box, conf in zip(boxes.xyxy.cpu().numpy(), confidences):
                x1, y1, x2, y2 = map(int, box[:4])
                color = (0, int(255 * conf), 0)
                cv2.rectangle(confidence_map, (x1, y1), (x2, y2), color, -1)
    return confidence_map

def apply_heatmap(confidence_map: np.ndarray, frame: np.ndarray,
                 alpha: float = 0.6) -> np.ndarray:
    """应用置信度热力图效果"""
    # 将置信度图转换为彩色热力图
    confidence_normalized = (confidence_map * 255).astype(np.uint8)
    heatmap = cv2.applyColorMap(confidence_normalized, cv2.COLORMAP_JET)

    # 创建掩码，只在有置信度的区域应用热力图
    mask = confidence_map > 0

    # 将热力图叠加到原始帧上
    result = frame.copy()
    result[mask] = cv2.addWeighted(frame[mask], 1-alpha, heatmap[mask], alpha, 0)

    return result

def create_video_from_frames(frames: List[np.ndarray], output_path: str, fps: float = 30.0) -> bool:
    """
    从帧列表创建视频文件

    Args:
        frames (List[np.ndarray]): 图像帧列表
        output_path (str): 输出视频路径
        fps (float): 帧率

    Returns:
        bool: 是否成功创建视频
    """
    if not frames:
        logger.error("No frames provided")
        return False

    try:
        # 获取第一帧的尺寸
        height, width = frames[0].shape[:2]

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        if not out.isOpened():
            logger.error(f"Failed to create video writer for {output_path}")
            return False

        # 写入所有帧
        for frame in frames:
            out.write(frame)

        out.release()

        # 验证文件是否创建成功
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"Video created successfully: {output_path}")
            return True
        else:
            logger.error(f"Failed to create video file: {output_path}")
            return False

    except Exception as e:
        logger.error(f"Error creating video: {e}")
        return False


def get_video_info(video_path: str) -> Optional[dict]:
    """
    获取视频文件信息

    Args:
        video_path (str): 视频文件路径

    Returns:
        Optional[dict]: 视频信息字典，失败时返回None
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Cannot open video: {video_path}")
            return None

        info = {
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'codec': int(cap.get(cv2.CAP_PROP_FOURCC))
        }

        info['duration_seconds'] = info['frame_count'] / info['fps'] if info['fps'] > 0 else 0

        cap.release()
        return info

    except Exception as e:
        logger.error(f"Error getting video info: {e}")
        return None


def extract_frames(video_path: str, max_frames: Optional[int] = None) -> List[np.ndarray]:
    """
    从视频中提取帧

    Args:
        video_path (str): 视频文件路径
        max_frames (Optional[int]): 最大提取帧数，None表示提取所有帧

    Returns:
        List[np.ndarray]: 帧列表
    """
    frames = []

    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Cannot open video: {video_path}")
            return frames

        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frames.append(frame.copy())
            frame_count += 1

            if max_frames and frame_count >= max_frames:
                break

        cap.release()
        logger.info(f"Extracted {len(frames)} frames from {video_path}")

    except Exception as e:
        logger.error(f"Error extracting frames: {e}")

    return frames


def resize_frame(frame: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    """
    调整帧大小

    Args:
        frame (np.ndarray): 输入帧
        target_size (Tuple[int, int]): 目标大小 (width, height)

    Returns:
        np.ndarray: 调整后的帧
    """
    try:
        return cv2.resize(frame, target_size)
    except Exception as e:
        logger.error(f"Error resizing frame: {e}")
        return frame


def validate_video_file(video_path: str) -> bool:
    """
    验证视频文件是否有效

    Args:
        video_path (str): 视频文件路径

    Returns:
        bool: 文件是否有效
    """
    try:
        if not os.path.exists(video_path):
            logger.error(f"Video file does not exist: {video_path}")
            return False

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Cannot open video file: {video_path}")
            return False

        # 尝试读取第一帧
        ret, frame = cap.read()
        cap.release()

        if not ret or frame is None:
            logger.error(f"Cannot read frames from video: {video_path}")
            return False

        return True

    except Exception as e:
        logger.error(f"Error validating video file: {e}")
        return False
