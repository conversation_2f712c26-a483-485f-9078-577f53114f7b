#!/usr/bin/env python3
"""
VideoWriter诊断工具
帮助识别和解决VideoWriter间歇性失败的问题
"""

import cv2
import numpy as np
import os
import logging
import tempfile
import time
from typing import Dict, List, Tuple, Any

logger = logging.getLogger(__name__)

class VideoWriterDiagnostics:
    """VideoWriter诊断工具"""

    def __init__(self):
        self.test_results = {}
        self.failed_cases = []

    def comprehensive_test(self, width: int, height: int, fps: float = 24.0) -> Dict[str, Any]:
        """
        对指定尺寸进行全面的VideoWriter测试

        Args:
            width: 视频宽度
            height: 视频高度
            fps: 帧率

        Returns:
            Dict: 详细的测试结果
        """
        logger.info(f" Starting comprehensive VideoWriter test for {width}x{height} @ {fps}fps")

        results = {
            'dimensions': f"{width}x{height}",
            'fps': fps,
            'system_info': self._get_system_info(),
            'codec_tests': {},
            'stress_tests': {},
            'memory_tests': {},
            'recommendations': []
        }

        # 测试不同编码器
        codecs_to_test = [
            ('H264', '.mp4'),
            ('h264', '.mp4'),
            ('X264', '.mp4'),
            ('mp4v', '.mp4'),
            ('XVID', '.avi'),
            ('MJPG', '.avi'),
            ('MJPG', '.mp4'),
            (0, '.avi') # 未压缩
        ]

        for codec, ext in codecs_to_test:
            results['codec_tests'][f"{codec}_{ext}"] = self._test_codec_thoroughly(
                codec, ext, width, height, fps
            )

        # 压力测试
        working_codecs = [k for k, v in results['codec_tests'].items() if v['success']]
        if working_codecs:
            best_codec = working_codecs[0]
            codec_info = results['codec_tests'][best_codec]
            results['stress_tests'] = self._stress_test_codec(
                codec_info['fourcc'], codec_info['ext'], width, height, fps
            )

        # 内存测试
        results['memory_tests'] = self._memory_usage_test(width, height, fps)

        # 生成建议
        results['recommendations'] = self._generate_recommendations(results)

        return results

    def _test_codec_thoroughly(self, codec, ext: str, width: int, height: int, fps: float) -> Dict[str, Any]:
        """彻底测试单个编码器"""
        result = {
            'codec': codec,
            'ext': ext,
            'success': False,
            'fourcc': None,
            'creation_time': 0,
            'write_performance': {},
            'file_size': 0,
            'errors': []
        }

        start_time = time.time()

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
                output_path = tmp_file.name

            # 获取fourcc
            if codec == 0:
                fourcc = 0
            else:
                fourcc = cv2.VideoWriter_fourcc(*codec)

            result['fourcc'] = fourcc

            # 创建VideoWriter
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not writer.isOpened():
                result['errors'].append("VideoWriter failed to open")
                return result

            result['creation_time'] = time.time() - start_time

            # 测试不同类型的帧写入
            frame_types = [
                ('solid_color', self._create_solid_frame(height, width)),
                ('random_noise', self._create_noise_frame(height, width)),
                ('gradient', self._create_gradient_frame(height, width)),
                ('complex_scene', self._create_complex_frame(height, width)),
                ('text_overlay', self._create_text_frame(height, width))
            ]

            write_times = []
            failed_writes = 0

            for frame_type, test_frame in frame_types:
                # 测试每种帧类型写入10次
                type_times = []
                type_failures = 0

                for i in range(10):
                    write_start = time.time()
                    success = writer.write(test_frame)
                    write_time = time.time() - write_start

                    if success:
                        type_times.append(write_time)
                    else:
                        type_failures += 1
                        failed_writes += 1

                result['write_performance'][frame_type] = {
                    'avg_write_time': np.mean(type_times) if type_times else 0,
                    'max_write_time': np.max(type_times) if type_times else 0,
                    'failures': type_failures,
                    'success_rate': (10 - type_failures) / 10
                }

                write_times.extend(type_times)

            writer.release()

            # 检查输出文件
            if os.path.exists(output_path):
                result['file_size'] = os.path.getsize(output_path)
                os.remove(output_path)

                if result['file_size'] > 0 and failed_writes < 25: # 允许一些失败
                    result['success'] = True
            else:
                result['errors'].append("Output file was not created")

            # 性能统计
            if write_times:
                result['avg_write_time'] = np.mean(write_times)
                result['write_time_std'] = np.std(write_times)
                result['total_failures'] = failed_writes
                result['overall_success_rate'] = (50 - failed_writes) / 50

        except Exception as e:
            result['errors'].append(f"Exception during test: {str(e)}")

        finally:
            if 'output_path' in locals() and os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass

        return result

    def _stress_test_codec(self, fourcc, ext: str, width: int, height: int, fps: float) -> Dict[str, Any]:
        """对可用编码器进行压力测试"""
        result = {
            'continuous_write_test': {},
            'large_file_test': {},
            'concurrent_writers_test': {}
        }

        # 连续写入测试
        try:
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
                output_path = tmp_file.name

            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if writer.isOpened():
                # 写入1000帧
                test_frame = self._create_complex_frame(height, width)
                failures = 0

                for i in range(1000):
                    if not writer.write(test_frame):
                        failures += 1
                        if failures > 50: # 如果失败太多就停止
                            break

                writer.release()

                result['continuous_write_test'] = {
                    'frames_attempted': i + 1,
                    'failures': failures,
                    'success_rate': (i + 1 - failures) / (i + 1),
                    'file_size': os.path.getsize(output_path) if os.path.exists(output_path) else 0
                }

                if os.path.exists(output_path):
                    os.remove(output_path)

        except Exception as e:
            result['continuous_write_test']['error'] = str(e)

        return result

    def _memory_usage_test(self, width: int, height: int, fps: float) -> Dict[str, Any]:
        """内存使用测试"""
        result = {}

        try:
            import psutil
            process = psutil.Process()

            # 基准内存使用
            baseline_memory = process.memory_info().rss / 1024 / 1024 # MB

            # 创建多个VideoWriter
            writers = []
            memory_usage = []

            for i in range(5):
                with tempfile.NamedTemporaryFile(suffix='.avi', delete=False) as tmp_file:
                    output_path = tmp_file.name

                fourcc = cv2.VideoWriter_fourcc('M', 'J', 'P', 'G')
                writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

                if writer.isOpened():
                    writers.append((writer, output_path))
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_usage.append(current_memory - baseline_memory)

            # 清理
            for writer, path in writers:
                writer.release()
                if os.path.exists(path):
                    os.remove(path)

            result = {
                'baseline_memory_mb': baseline_memory,
                'memory_per_writer_mb': np.mean(memory_usage) if memory_usage else 0,
                'max_memory_usage_mb': max(memory_usage) if memory_usage else 0,
                'writers_created': len(writers)
            }

        except ImportError:
            result['error'] = "psutil not available for memory testing"
        except Exception as e:
            result['error'] = str(e)

        return result

    def _create_solid_frame(self, height: int, width: int) -> np.ndarray:
        """创建纯色帧"""
        frame = np.full((height, width, 3), (100, 150, 200), dtype=np.uint8)
        return np.ascontiguousarray(frame)

    def _create_noise_frame(self, height: int, width: int) -> np.ndarray:
        """创建噪声帧"""
        frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        return np.ascontiguousarray(frame)

    def _create_gradient_frame(self, height: int, width: int) -> np.ndarray:
        """创建渐变帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        for i in range(height):
            frame[i, :, 0] = int(255 * i / height) # 红色渐变
        for j in range(width):
            frame[:, j, 1] = int(255 * j / width) # 绿色渐变
        return np.ascontiguousarray(frame)

    def _create_complex_frame(self, height: int, width: int) -> np.ndarray:
        """创建复杂场景帧"""
        frame = np.random.randint(50, 200, (height, width, 3), dtype=np.uint8)

        # 添加几何形状
        cv2.rectangle(frame, (10, 10), (width//4, height//4), (0, 255, 0), 2)
        cv2.circle(frame, (width//2, height//2), min(width, height)//8, (255, 0, 0), 3)

        # 添加文字
        cv2.putText(frame, 'Test Frame', (width//4, height//2),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        return np.ascontiguousarray(frame)

    def _create_text_frame(self, height: int, width: int) -> np.ndarray:
        """创建文字帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)

        # 添加多行文字
        lines = ['Line 1', 'Line 2', 'Line 3', 'Line 4']
        for i, line in enumerate(lines):
            y = 50 + i * 40
            cv2.putText(frame, line, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        return np.ascontiguousarray(frame)

    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'opencv_version': cv2.__version__,
            'python_version': __import__('sys').version
        }

        try:
            import platform
            info['platform'] = platform.platform()
            info['architecture'] = platform.architecture()
        except:
            pass

        return info

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """根据测试结果生成建议"""
        recommendations = []

        # 分析编码器测试结果
        working_codecs = [k for k, v in results['codec_tests'].items() if v['success']]
        failed_codecs = [k for k, v in results['codec_tests'].items() if not v['success']]

        if not working_codecs:
            recommendations.append(" 没有编码器通过测试，可能需要重新安装OpenCV或系统编码器库")
        elif len(working_codecs) < 3:
            recommendations.append(" 只有少数编码器可用，建议检查系统编码器库")

        # 分析性能
        if working_codecs:
            best_codec = min(working_codecs,
                           key=lambda x: results['codec_tests'][x].get('avg_write_time', float('inf')))
            recommendations.append(f" 推荐使用编码器: {best_codec}")

        # 分析失败率
        high_failure_codecs = [
            k for k, v in results['codec_tests'].items()
            if v['success'] and v.get('overall_success_rate', 1.0) < 0.9
        ]

        if high_failure_codecs:
            recommendations.append(f" 这些编码器成功率较低: {high_failure_codecs}")

        return recommendations

def run_diagnostics(width: int, height: int, fps: float = 24.0) -> Dict[str, Any]:
    """运行VideoWriter诊断"""
    diagnostics = VideoWriterDiagnostics()
    return diagnostics.comprehensive_test(width, height, fps)

if __name__ == "__main__":
    # 测试常见尺寸
    test_sizes = [
        (640, 480),
        (1280, 720),
        (1920, 1080),
        (2560, 1440) # 2x1280x720，模拟组合帧
    ]

    for width, height in test_sizes:
        print(f"\n{'='*50}")
        print(f"Testing {width}x{height}")
        print(f"{'='*50}")

        results = run_diagnostics(width, height)

        print(f"System: {results['system_info']}")
        print(f"Working codecs: {[k for k, v in results['codec_tests'].items() if v['success']]}")
        print("Recommendations:")
        for rec in results['recommendations']:
            print(f" {rec}")