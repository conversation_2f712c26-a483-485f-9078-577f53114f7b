#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CUDA工具模块 - 提供安全的CUDA缓存清理功能
避免循环导入问题
"""

import logging
import torch

logger = logging.getLogger(__name__)


def safe_cuda_empty_cache():
    """
    全局安全CUDA缓存清理函数，避免内存对齐错误
    
    功能：
    - 安全地清理CUDA缓存，避免"misaligned address"错误
    - 捕获并处理各种CUDA运行时错误
    - 提供详细的错误日志记录
    - 确保程序在清理失败时能继续运行
    
    异常处理：
    - RuntimeError: 特别处理"misaligned address"错误
    - Exception: 捕获所有其他异常
    - 所有异常都不会向上抛出，只记录警告日志
    """
    try:
        if torch.cuda.is_available():
            # 先同步CUDA操作，确保所有操作完成
            torch.cuda.synchronize()
            # 然后清理缓存
            torch.cuda.empty_cache()
    except RuntimeError as e:
        if "misaligned address" in str(e).lower():
            logger.warning(f"CUDA内存对齐错误，跳过缓存清理: {e}")
            # 不抛出异常，继续执行
            pass
        else:
            logger.warning(f"CUDA缓存清理失败: {e}")
            # 其他CUDA错误也不抛出，避免中断流程
            pass
    except Exception as e:
        logger.warning(f"缓存清理异常: {e}")
        pass


def safe_cuda_reset():
    """
    安全重置CUDA状态
    
    功能：
    - 重置CUDA内存统计
    - 清理CUDA缓存
    - 处理重置过程中的各种错误
    """
    try:
        if torch.cuda.is_available():
            # 重置内存统计
            torch.cuda.reset_peak_memory_stats()
            # 清理缓存
            safe_cuda_empty_cache()
            logger.info("CUDA状态重置完成")
    except Exception as e:
        logger.warning(f"CUDA状态重置失败: {e}")
        pass


def get_cuda_memory_info():
    """
    获取CUDA内存使用信息
    
    返回:
        dict: 包含内存使用信息的字典，如果CUDA不可用则返回None
    """
    try:
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated()
            reserved = torch.cuda.memory_reserved()
            return {
                'allocated_mb': allocated / 1024 / 1024,
                'reserved_mb': reserved / 1024 / 1024,
                'device_name': torch.cuda.get_device_name(0)
            }
    except Exception as e:
        logger.warning(f"获取CUDA内存信息失败: {e}")
        return None