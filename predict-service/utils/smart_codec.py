"""
智能视频编码器选择和优化工具
根据使用场景自动选择最优编码器
"""

import cv2
import logging
from typing import Optional, List, Tuple, Dict
import time
import os

logger = logging.getLogger(__name__)

class SmartCodecSelector:
    """智能编码器选择器"""

    # 编码器性能特征 (编码速度倍数, 文件大小倍数, 兼容性分数)
    CODEC_CHARACTERISTICS = {
        'MJPG': (5.0, 8.0, 10), # 最快编码，大文件，最佳兼容性
        'mp4v': (3.0, 3.0, 9), # 快速编码，中等文件，很好兼容性
        'XVID': (2.5, 2.5, 8), # 中等编码，中等文件，好兼容性
        'h264': (1.0, 1.0, 10), # 基准编码器，基准文件大小，最佳兼容性
        'H264': (1.0, 1.0, 10), # 同上
        'X264': (1.0, 1.0, 9), # 同上但兼容性稍低
        'H265': (0.3, 0.5, 6), # 慢编码，小文件，兼容性一般
        'avc1': (1.2, 1.1, 7), # 略快，略大，兼容性一般
    }

    def __init__(self):
        self._tested_codecs = {}
        self._performance_cache = {}

    def get_recommendation(self,
                         use_case: str = 'general',
                         priority: str = 'balanced',
                         file_size_mb: Optional[float] = None) -> Dict:
        """
        获取编码器推荐

        Args:
            use_case: 'realtime', 'storage', 'streaming', 'temp', 'general'
            priority: 'speed', 'size', 'quality', 'balanced'
            file_size_mb: 预期文件大小(MB)，用于选择优化策略

        Returns:
            Dict: 包含推荐编码器和配置的字典
        """

        recommendations = {
            'realtime': {
                'speed': ('MJPG', {'fps_limit': 30, 'preset': 'ultrafast'}),
                'balanced': ('mp4v', {'fps_limit': 25, 'preset': 'fast'}),
                'quality': ('h264', {'fps_limit': 20, 'preset': 'medium'}),
                'size': ('h264', {'fps_limit': 15, 'preset': 'slow'})
            },
            'storage': {
                'speed': ('mp4v', {'preset': 'fast'}),
                'balanced': ('h264', {'preset': 'medium'}),
                'quality': ('h264', {'preset': 'slow', 'crf': 18}),
                'size': ('H265', {'preset': 'slow', 'crf': 23})
            },
            'streaming': {
                'speed': ('h264', {'preset': 'ultrafast', 'tune': 'zerolatency'}),
                'balanced': ('h264', {'preset': 'fast', 'tune': 'zerolatency'}),
                'quality': ('h264', {'preset': 'medium'}),
                'size': ('h264', {'preset': 'slow'})
            },
            'temp': {
                'speed': ('MJPG', {'quality': 50}),
                'balanced': ('MJPG', {'quality': 70}),
                'quality': ('mp4v', {'quality': 80}),
                'size': ('mp4v', {'quality': 60})
            }
        }

        # 根据文件大小调整策略
        if file_size_mb and file_size_mb > 100: # 大文件优先压缩
            if priority == 'balanced':
                priority = 'size'
        elif file_size_mb and file_size_mb < 10: # 小文件优先速度
            if priority == 'balanced':
                priority = 'speed'

        codec, config = recommendations.get(use_case, recommendations['general']).get(
            priority, recommendations['general']['balanced']
        )

        return {
            'codec': codec,
            'fourcc': cv2.VideoWriter_fourcc(*codec),
            'config': config,
            'fallback_list': self._get_fallback_codecs(codec, use_case)
        }

    def _get_fallback_codecs(self, primary_codec: str, use_case: str) -> List[str]:
        """获取备选编码器列表"""
        fallback_map = {
            'realtime': ['MJPG', 'mp4v', 'XVID', 'h264'],
            'storage': ['h264', 'mp4v', 'XVID', 'MJPG'],
            'streaming': ['h264', 'mp4v', 'MJPG', 'XVID'],
            'temp': ['MJPG', 'mp4v', 'XVID', 'h264']
        }

        base_list = fallback_map.get(use_case, fallback_map['storage'])

        # 确保主编码器在列表最前面
        if primary_codec in base_list:
            base_list.remove(primary_codec)
        return [primary_codec] + base_list

    def test_codec_performance(self, codec: str, test_duration: float = 1.0) -> Dict:
        """
        测试编码器性能

        Args:
            codec: 编码器名称
            test_duration: 测试持续时间(秒)

        Returns:
            Dict: 性能测试结果
        """
        if codec in self._performance_cache:
            return self._performance_cache[codec]

        try:
            # 创建测试视频
            import tempfile
            import numpy as np

            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
                output_path = tmp_file.name

            fourcc = cv2.VideoWriter_fourcc(*codec)
            fps = 24.0
            size = (640, 480)

            writer = cv2.VideoWriter(output_path, fourcc, fps, size)

            if not writer.isOpened():
                return {'success': False, 'error': 'Failed to initialize VideoWriter'}

            # 生成测试帧
            test_frame = np.random.randint(0, 255, (size[1], size[0], 3), dtype=np.uint8)

            # 性能测试
            start_time = time.time()
            frames_written = 0
            target_frames = int(fps * test_duration)

            for i in range(target_frames):
                if writer.write(test_frame):
                    frames_written += 1
                else:
                    break

            encoding_time = time.time() - start_time
            writer.release()

            # 计算指标
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            encoding_fps = frames_written / encoding_time if encoding_time > 0 else 0

            result = {
                'success': True,
                'codec': codec,
                'encoding_fps': encoding_fps,
                'file_size_bytes': file_size,
                'encoding_time': encoding_time,
                'frames_written': frames_written,
                'efficiency_score': encoding_fps / (file_size / 1024 / 1024) if file_size > 0 else 0
            }

            # 清理测试文件
            if os.path.exists(output_path):
                os.remove(output_path)

            self._performance_cache[codec] = result
            return result

        except Exception as e:
            logger.error(f"Codec performance test failed for {codec}: {e}")
            return {'success': False, 'error': str(e)}

    def find_best_codec_for_scenario(self,
                                   target_fps: float = 24.0,
                                   target_size_mb: Optional[float] = None,
                                   max_encoding_time: Optional[float] = None) -> str:
        """
        为特定场景找到最佳编码器

        Args:
            target_fps: 目标帧率
            target_size_mb: 目标文件大小(MB)
            max_encoding_time: 最大编码时间(秒)

        Returns:
            str: 最佳编码器名称
        """
        candidates = ['MJPG', 'mp4v', 'h264', 'XVID']
        best_codec = 'h264' # 默认
        best_score = 0

        for codec in candidates:
            perf = self.test_codec_performance(codec, test_duration=0.5)

            if not perf['success']:
                continue

            score = 0

            # 帧率评分
            if perf['encoding_fps'] >= target_fps:
                score += 10
            else:
                score += (perf['encoding_fps'] / target_fps) * 10

            # 文件大小评分
            if target_size_mb:
                estimated_size_mb = perf['file_size_bytes'] / 1024 / 1024
                if estimated_size_mb <= target_size_mb:
                    score += 10
                else:
                    score += max(0, 10 - (estimated_size_mb - target_size_mb))

            # 编码时间评分
            if max_encoding_time:
                if perf['encoding_time'] <= max_encoding_time:
                    score += 5
                else:
                    score += max(0, 5 - (perf['encoding_time'] - max_encoding_time))

            if score > best_score:
                best_score = score
                best_codec = codec

        logger.info(f"Best codec for scenario: {best_codec} (score: {best_score})")
        return best_codec

# 全局实例
smart_codec = SmartCodecSelector()

def get_optimal_codec_for_ai_processing() -> Dict:
    """为AI处理场景获取最优编码器配置"""
    return smart_codec.get_recommendation(
        use_case='realtime',
        priority='speed',
        file_size_mb=50 # 假设中等大小文件
    )

def get_optimal_codec_for_output() -> Dict:
    """为输出文件获取最优编码器配置"""
    return smart_codec.get_recommendation(
        use_case='storage',
        priority='balanced'
    )

def get_optimal_codec_for_temp() -> Dict:
    """为临时文件获取最优编码器配置"""
    return smart_codec.get_recommendation(
        use_case='temp',
        priority='speed'
    )