#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志配置模块
提供统一的日志配置功能，确保所有模块都使用配置文件中的日志级别
"""

import logging
import sys
import os
from typing import Optional


def setup_logging_from_config(config_manager=None, logger_name: Optional[str] = None) -> logging.Logger:
    """
    根据配置文件设置日志系统
    
    Args:
        config_manager: 配置管理器实例，如果为None则尝试创建
        logger_name: 日志器名称，如果为None则使用调用模块名
        
    Returns:
        配置好的日志器实例
    """
    if config_manager is None:
        try:
            from core.managers.config_manager import ConfigManager
            config_manager = ConfigManager()
        except Exception as e:
            print(f"无法加载配置管理器，使用默认日志配置: {e}")
            return _setup_default_logging(logger_name)
    
    try:
        log_config = config_manager.logging
        
        # 根据配置文件设置日志级别
        log_level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        log_level = log_level_map.get(log_config['level'].upper(), logging.INFO)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if log_config.get('console', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(log_level)
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if log_config.get('file'):
            try:
                # 确保日志目录存在
                log_dir = os.path.dirname(log_config['file'])
                if log_dir:
                    os.makedirs(log_dir, exist_ok=True)
                
                file_handler = logging.FileHandler(log_config['file'], encoding='utf-8')
                file_handler.setFormatter(formatter)
                file_handler.setLevel(log_level)
                root_logger.addHandler(file_handler)
            except Exception as e:
                print(f"无法创建文件日志处理器: {e}")
        
        # 设置第三方库日志级别
        _setup_third_party_loggers(log_level)
        
        # 获取指定的日志器
        if logger_name:
            logger = logging.getLogger(logger_name)
        else:
            logger = logging.getLogger(__name__)
        
        logger.info(f"日志系统配置完成 - 级别: {log_config['level']}, 控制台: {log_config.get('console', True)}, 文件: {log_config.get('file', 'None')}")
        
        return logger
        
    except Exception as e:
        print(f"日志配置失败，使用默认配置: {e}")
        return _setup_default_logging(logger_name)


def _setup_default_logging(logger_name: Optional[str] = None) -> logging.Logger:
    """
    设置默认日志配置
    
    Args:
        logger_name: 日志器名称
        
    Returns:
        配置好的日志器实例
    """
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] [%(levelname)7s] %(name)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置第三方库日志级别
    _setup_third_party_loggers(logging.INFO)
    
    if logger_name:
        return logging.getLogger(logger_name)
    else:
        return logging.getLogger(__name__)


def _setup_third_party_loggers(base_level: int):
    """
    设置第三方库的日志级别
    
    Args:
        base_level: 基础日志级别
    """
    # 如果配置的是DEBUG级别，第三方库使用INFO；否则使用WARNING
    third_party_level = logging.INFO if base_level == logging.DEBUG else logging.WARNING
    
    # 需要调整日志级别的第三方库
    third_party_loggers = [
        'pika', 'pika.adapters', 'pika.connection', 'pika.channel',
        'urllib3', 'urllib3.connectionpool', 'requests', 'requests.packages.urllib3',
        'ultralytics', 'ultralytics.utils', 'ultralytics.engine',
        'PIL', 'PIL.PngImagePlugin', 'PIL.Image',
        'matplotlib', 'matplotlib.font_manager',
        'werkzeug', 'werkzeug.serving',
        'ppocr', 'paddleocr'  # PaddleOCR 相关日志
    ]
    
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(third_party_level)
    
    # 特别处理一些特别吵闹的日志器
    very_quiet_loggers = [
        'sentry_sdk', 'urllib3.connectionpool'
    ]
    
    for logger_name in very_quiet_loggers:
        logging.getLogger(logger_name).setLevel(logging.CRITICAL + 1)


def get_logger(name: str = None) -> logging.Logger:
    """
    获取配置好的日志器
    
    Args:
        name: 日志器名称，如果为None则使用调用模块名
        
    Returns:
        日志器实例
    """
    if name is None:
        # 获取调用者的模块名
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')
    
    return logging.getLogger(name)


def set_log_level(level: str):
    """
    动态设置日志级别
    
    Args:
        level: 日志级别字符串 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
    """
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    log_level = log_level_map.get(level.upper(), logging.INFO)
    
    # 设置根日志器级别
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 设置所有处理器的级别
    for handler in root_logger.handlers:
        handler.setLevel(log_level)
    
    # 重新设置第三方库日志级别
    _setup_third_party_loggers(log_level)
    
    print(f"日志级别已动态设置为: {level}")
