#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)


class TrackerConfigLoader:
    """
    追踪器配置加载器

    支持：
    1. 统一配置文件的动态模式切换
    2. 传统的分离配置文件
    3. 运行时参数覆盖
    """

    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置加载器

        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.unified_config_path = self.config_dir / "unified_bytetrack.yaml"
        self.general_config_path = self.config_dir / "bytetrack.yaml"
        self.gas_config_path = self.config_dir / "gas_leakage_bytetrack.yaml"

        # 缓存加载的配置
        self._unified_config = None
        self._config_cache = {}

    def load_config(self,
                   mode: Optional[str] = None,
                   gas_leakage_mode: bool = False,
                   task_name: Optional[str] = None,
                   overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        加载追踪器配置

        Args:
            mode: 强制指定模式 ('general', 'gas_leakage', 'auto')
            gas_leakage_mode: 是否为气体泄漏模式
            task_name: 任务名称（用于自动检测）
            overrides: 运行时参数覆盖

        Returns:
            配置字典
        """
        try:
            # 优先使用统一配置文件
            if self.unified_config_path.exists():
                return self._load_unified_config(mode, gas_leakage_mode, task_name, overrides)
            else:
                # 回退到传统分离配置文件
                return self._load_separate_config(gas_leakage_mode, overrides)

        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            return self._get_default_config(gas_leakage_mode)

    def _load_unified_config(self,
                           mode: Optional[str],
                           gas_leakage_mode: bool,
                           task_name: Optional[str],
                           overrides: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从统一配置文件加载配置
        """
        # 加载统一配置
        if self._unified_config is None:
            with open(self.unified_config_path, 'r', encoding='utf-8') as f:
                self._unified_config = yaml.safe_load(f)

        config = self._unified_config.copy()

        # 确定运行模式
        target_mode = self._determine_mode(mode, gas_leakage_mode, task_name, config)

        # 提取对应模式的配置
        if target_mode == 'gas_leakage':
            mode_config = config.get('gas_leakage', {})
            logger.info(" 使用气体泄漏检测配置")
        else:
            mode_config = config.get('general', {})
            logger.info(" 使用通用追踪配置")

        # 合并高级参数
        advanced_config = config.get('advanced', {})
        if target_mode == 'gas_leakage' and 'gas_mode_overrides' in advanced_config:
            # 应用气体模式的高级参数覆盖
            gas_overrides = advanced_config['gas_mode_overrides']
            for key, value in gas_overrides.items():
                if key not in mode_config: # 只覆盖未在模式配置中明确设置的参数
                    mode_config[key] = value

        # 应用基础高级参数
        for key, value in advanced_config.items():
            if key != 'gas_mode_overrides' and key not in mode_config:
                mode_config[key] = value

        # 添加元数据
        mode_config['_mode'] = target_mode
        mode_config['_config_source'] = 'unified'
        mode_config['tracker_type'] = config.get('tracker_type', 'bytetrack')

        # 应用运行时覆盖
        if overrides:
            mode_config.update(overrides)
            logger.info(f"应用运行时参数覆盖: {list(overrides.keys())}")

        return mode_config

    def _load_separate_config(self,
                            gas_leakage_mode: bool,
                            overrides: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从分离的配置文件加载配置
        """
        if gas_leakage_mode and self.gas_config_path.exists():
            config_path = self.gas_config_path
            logger.info(" 加载气体泄漏专用配置文件")
        elif self.general_config_path.exists():
            config_path = self.general_config_path
            logger.info(" 加载通用追踪配置文件")
        else:
            logger.warning("未找到配置文件，使用默认配置")
            return self._get_default_config(gas_leakage_mode)

        # 缓存配置
        cache_key = str(config_path)
        if cache_key not in self._config_cache:
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config_cache[cache_key] = yaml.safe_load(f)

        config = self._config_cache[cache_key].copy()

        # 添加元数据
        config['_mode'] = 'gas_leakage' if gas_leakage_mode else 'general'
        config['_config_source'] = 'separate'

        # 应用运行时覆盖
        if overrides:
            config.update(overrides)
            logger.info(f"应用运行时参数覆盖: {list(overrides.keys())}")

        return config

    def _determine_mode(self,
                       mode: Optional[str],
                       gas_leakage_mode: bool,
                       task_name: Optional[str],
                       config: Dict[str, Any]) -> str:
        """
        确定运行模式
        """
        # 1. 显式指定模式优先级最高
        if mode and mode in ['general', 'gas_leakage']:
            logger.info(f"使用显式指定模式: {mode}")
            return mode

        # 2. gas_leakage_mode参数
        if gas_leakage_mode:
            logger.info("根据gas_leakage_mode参数选择气体泄漏模式")
            return 'gas_leakage'

        # 3. 基于任务名称的自动检测
        if task_name:
            mode_selection = config.get('mode_selection', {})
            keywords = mode_selection.get('auto_detection_keywords', [])

            task_name_lower = task_name.lower()
            for keyword in keywords:
                if keyword.lower() in task_name_lower:
                    logger.info(f"根据任务名称 '{task_name}' 自动选择气体泄漏模式")
                    return 'gas_leakage'

        # 4. 检查配置文件的默认模式
        file_mode = config.get('mode', 'auto')
        if file_mode != 'auto':
            mode_mapping = config.get('mode_selection', {}).get('mode_mapping', {})
            mapped_mode = mode_mapping.get(file_mode, file_mode)
            if mapped_mode in ['general', 'gas_leakage']:
                logger.info(f"使用配置文件默认模式: {mapped_mode}")
                return mapped_mode

        # 5. 默认为通用模式
        logger.info("使用默认通用模式")
        return 'general'

    def _get_default_config(self, gas_leakage_mode: bool) -> Dict[str, Any]:
        """
        获取默认配置
        """
        if gas_leakage_mode:
            config = {
                'tracker_type': 'bytetrack',
                'track_high_thresh': 0.5,
                'track_low_thresh': 0.2,
                'new_track_thresh': 0.4,
                'track_buffer': 50,
                'match_thresh': 0.6,
                'frame_rate': 30,
                'min_box_area': 100,
                'max_time_lost': 40,
                'gas_confidence_thresh': 0.3,
                'enable_camera_compensation': True,
                '_mode': 'gas_leakage',
                '_config_source': 'default'
            }
            logger.info(" 使用默认气体泄漏配置")
        else:
            config = {
                'tracker_type': 'bytetrack',
                'track_high_thresh': 0.6,
                'track_low_thresh': 0.1,
                'new_track_thresh': 0.7,
                'track_buffer': 30,
                'match_thresh': 0.8,
                'frame_rate': 30,
                'min_box_area': 10,
                'max_time_lost': 30,
                '_mode': 'general',
                '_config_source': 'default'
            }
            logger.info(" 使用默认通用配置")

        return config

    def get_config_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取配置信息
        """
        return {
            'mode': config.get('_mode', 'unknown'),
            'source': config.get('_config_source', 'unknown'),
            'tracker_type': config.get('tracker_type', 'bytetrack'),
            'track_high_thresh': config.get('track_high_thresh'),
            'track_low_thresh': config.get('track_low_thresh'),
            'match_thresh': config.get('match_thresh'),
            'track_buffer': config.get('track_buffer')
        }

    def save_config_to_file(self, config: Dict[str, Any], output_path: str):
        """
        将配置保存到文件
        """
        # 移除元数据
        clean_config = {k: v for k, v in config.items() if not k.startswith('_')}

        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(clean_config, f, default_flow_style=False,
                     allow_unicode=True, sort_keys=False)

        logger.info(f"配置已保存到: {output_path}")

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置的有效性
        """
        required_keys = [
            'tracker_type', 'track_high_thresh', 'track_low_thresh',
            'match_thresh', 'track_buffer'
        ]

        for key in required_keys:
            if key not in config:
                logger.error(f"配置缺少必需参数: {key}")
                return False

        # 检查数值范围
        if not (0.0 <= config.get('track_high_thresh', 0) <= 1.0):
            logger.error("track_high_thresh 必须在 0.0-1.0 范围内")
            return False

        if not (0.0 <= config.get('track_low_thresh', 0) <= 1.0):
            logger.error("track_low_thresh 必须在 0.0-1.0 范围内")
            return False

        if config.get('track_high_thresh', 0) <= config.get('track_low_thresh', 1):
            logger.error("track_high_thresh 必须大于 track_low_thresh")
            return False

        logger.info("配置验证通过")
        return True


# 全局配置加载器实例
_global_loader = None


def get_tracker_config_loader(config_dir: str = "configs") -> TrackerConfigLoader:
    """
    获取全局配置加载器实例
    """
    global _global_loader
    if _global_loader is None:
        _global_loader = TrackerConfigLoader(config_dir)
    return _global_loader


def load_tracker_config(mode: Optional[str] = None,
                       gas_leakage_mode: bool = False,
                       task_name: Optional[str] = None,
                       overrides: Optional[Dict[str, Any]] = None,
                       config_dir: str = "configs") -> Dict[str, Any]:
    """
    便捷函数：加载追踪器配置
    """
    loader = get_tracker_config_loader(config_dir)
    return loader.load_config(mode, gas_leakage_mode, task_name, overrides)


# 使用示例
if __name__ == "__main__":
    # 示例1: 自动模式选择
    config1 = load_tracker_config(gas_leakage_mode=True)
    print("Gas leakage config:", config1.get('_mode'))

    # 示例2: 显式指定模式
    config2 = load_tracker_config(mode='general')
    print("General config:", config2.get('_mode'))

    # 示例3: 基于任务名称自动检测
    config3 = load_tracker_config(task_name="gas_leak_detection_task")
    print("Auto-detected config:", config3.get('_mode'))

    # 示例4: 运行时参数覆盖
    config4 = load_tracker_config(
        gas_leakage_mode=True,
        overrides={'track_high_thresh': 0.4, 'custom_param': 'test'}
    )
    print("Override config:", config4.get('track_high_thresh'))