#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF报告数据构建器
PDF Report Data Builder
========================

为Java端PDF生成器提供结构化数据
"""

import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import numpy as np
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class PdfReportData:
    """PDF报告数据结构"""
    video_info: Dict[str, Any]  # 视频基本信息
    leakage_summary: Dict[str, Any]  # 泄漏汇总
    leakage_details: List[Dict]  # 详细泄漏点信息
    gps_trajectory: Optional[Dict]  # GPS轨迹（如有）
    risk_assessment: Dict[str, Any]  # 风险评估
    charts_data: Dict[str, Any]  # 图表数据
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class PDFDataBuilder:
    """PDF报告数据构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def build_pdf_report_data(
        self,
        video_info: Dict[str, Any],
        leakage_statistics: Any,
        leakage_points: List[Any],
        gps_data: Optional[Dict] = None,
        overall_risk_level: str = "unknown",
        confidence_intervals: Optional[Dict] = None,
        time_segments_analysis: Optional[Dict] = None,
        dispersion_behavior_summary: Optional[Dict] = None
    ) -> PdfReportData:
        """构建PDF报告数据
        
        Args:
            video_info: 视频基本信息
            leakage_statistics: 泄漏统计数据
            leakage_points: 泄漏点列表
            gps_data: GPS数据
            overall_risk_level: 整体风险等级
            confidence_intervals: 置信度区间
            time_segments_analysis: 时间段分析
            dispersion_behavior_summary: 扩散行为汇总
            
        Returns:
            PdfReportData: PDF报告数据
        """
        try:
            # 构建视频信息
            video_info_data = self._build_video_info(video_info)
            
            # 构建泄漏汇总
            leakage_summary = self._build_leakage_summary(
                leakage_statistics, overall_risk_level
            )
            
            # 构建泄漏点详情
            leakage_details = self._build_leakage_details(leakage_points)
            
            # 构建GPS轨迹
            gps_trajectory = self._build_gps_trajectory(gps_data)
            
            # 构建风险评估
            risk_assessment = self._build_risk_assessment(
                overall_risk_level, leakage_points
            )
            
            # 构建图表数据
            charts_data = self._build_charts_data(
                leakage_points, confidence_intervals, 
                time_segments_analysis, dispersion_behavior_summary
            )
            
            return PdfReportData(
                video_info=video_info_data,
                leakage_summary=leakage_summary,
                leakage_details=leakage_details,
                gps_trajectory=gps_trajectory,
                risk_assessment=risk_assessment,
                charts_data=charts_data
            )
            
        except Exception as e:
            self.logger.error(f"构建PDF报告数据失败: {e}")
            raise
    
    def _build_video_info(self, video_info: Dict[str, Any]) -> Dict[str, Any]:
        """构建视频基本信息"""
        return {
            "file_name": video_info.get("file_name", "unknown"),
            "duration_seconds": video_info.get("duration_seconds", 0),
            "fps": video_info.get("fps", 30),
            "resolution": {
                "width": video_info.get("width", 1920),
                "height": video_info.get("height", 1080)
            },
            "total_frames": video_info.get("total_frames", 0),
            "processing_date": datetime.now().isoformat(),
            "file_size_mb": video_info.get("file_size_mb", 0)
        }
    
    def _build_leakage_summary(self, leakage_statistics: Any, overall_risk_level: str) -> Dict[str, Any]:
        """构建泄漏汇总信息"""
        if hasattr(leakage_statistics, '__dict__'):
            stats = leakage_statistics.__dict__
        else:
            stats = leakage_statistics or {}
        
        return {
            "total_leakage_points": stats.get("total_leakage_points", 0),
            "unique_leakage_sources": stats.get("unique_leakage_sources", 0),
            "average_confidence": stats.get("average_confidence", 0.0),
            "total_detection_time": stats.get("total_detection_time_seconds", 0),
            "overall_risk_level": overall_risk_level,
            "detection_coverage": {
                "frames_with_detection": stats.get("frames_with_detection", 0),
                "total_frames": stats.get("total_frames_processed", 0),
                "coverage_percentage": stats.get("detection_coverage_percentage", 0.0)
            },
            "confidence_distribution": stats.get("confidence_distribution", {})
        }
    
    def _build_leakage_details(self, leakage_points: List[Any]) -> List[Dict]:
        """构建详细泄漏点信息"""
        details = []
        
        for i, point in enumerate(leakage_points):
            if hasattr(point, '__dict__'):
                point_data = point.__dict__
            else:
                point_data = point
            
            detail = {
                "point_id": i + 1,
                "track_id": point_data.get("track_id", f"track_{i+1}"),
                "class_name": point_data.get("class_name", "gas_leak"),
                "confidence_stats": point_data.get("confidence_stats", {}),
                "temporal_info": point_data.get("temporal_info", {}),
                "spatial_info": point_data.get("spatial_info", {}),
                "risk_assessment": point_data.get("risk_assessment", {}),
                "stability_analysis": point_data.get("stability_analysis", {}),
                "dispersion_behavior": point_data.get("dispersion_behavior", {}),
                "trajectory_summary": point_data.get("trajectory_summary", {})
            }
            details.append(detail)
        
        return details
    
    def _build_gps_trajectory(self, gps_data: Optional[Dict]) -> Optional[Dict]:
        """构建GPS轨迹数据"""
        if not gps_data:
            return None
        
        return {
            "has_gps_data": True,
            "trajectory_points": gps_data.get("trajectory_points", []),
            "start_location": gps_data.get("start_location", {}),
            "end_location": gps_data.get("end_location", {}),
            "total_distance_km": gps_data.get("total_distance_km", 0.0),
            "average_speed_kmh": gps_data.get("average_speed_kmh", 0.0),
            "altitude_range": gps_data.get("altitude_range", {})
        }
    
    def _build_risk_assessment(self, overall_risk_level: str, leakage_points: List[Any]) -> Dict[str, Any]:
        """构建风险评估数据"""
        # 统计各风险等级的数量
        risk_distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        
        for point in leakage_points:
            if hasattr(point, '__dict__'):
                point_data = point.__dict__
            else:
                point_data = point
            
            risk_level = point_data.get("risk_assessment", {}).get("risk_level", "low")
            if risk_level in risk_distribution:
                risk_distribution[risk_level] += 1
        
        return {
            "overall_risk_level": overall_risk_level,
            "risk_distribution": risk_distribution,
            "total_points": len(leakage_points),
            "high_risk_points": risk_distribution["high"] + risk_distribution["critical"],
            "risk_score": self._calculate_risk_score(risk_distribution),
            "recommendations": self._generate_risk_recommendations(overall_risk_level, risk_distribution)
        }
    
    def _build_charts_data(self, leakage_points: List[Any], confidence_intervals: Optional[Dict],
                          time_segments_analysis: Optional[Dict], 
                          dispersion_behavior_summary: Optional[Dict]) -> Dict[str, Any]:
        """构建图表数据"""
        charts = {}
        
        # 泄漏点时间线图数据
        charts["timeline_chart"] = self._build_timeline_chart_data(leakage_points)
        
        # 置信度分布图数据
        charts["confidence_distribution_chart"] = self._build_confidence_chart_data(
            leakage_points, confidence_intervals
        )
        
        # GPS轨迹图数据（如有）
        charts["gps_trajectory_chart"] = self._build_gps_chart_data(leakage_points)
        
        # 风险等级饼图数据
        charts["risk_level_pie_chart"] = self._build_risk_pie_chart_data(leakage_points)
        
        # 扩散行为图表数据
        charts["dispersion_behavior_chart"] = self._build_dispersion_chart_data(
            dispersion_behavior_summary
        )
        
        # 时间段分析图表数据
        charts["time_segments_chart"] = self._build_time_segments_chart_data(
            time_segments_analysis
        )
        
        return charts
    
    def _build_timeline_chart_data(self, leakage_points: List[Any]) -> Dict[str, Any]:
        """构建时间线图表数据"""
        timeline_data = []
        
        for point in leakage_points:
            if hasattr(point, '__dict__'):
                point_data = point.__dict__
            else:
                point_data = point
            
            temporal_info = point_data.get("temporal_info", {})
            timeline_data.append({
                "track_id": point_data.get("track_id", "unknown"),
                "start_time": temporal_info.get("start_time_seconds", 0),
                "end_time": temporal_info.get("end_time_seconds", 0),
                "duration": temporal_info.get("duration_seconds", 0),
                "confidence": point_data.get("confidence_stats", {}).get("average", 0)
            })
        
        return {
            "chart_type": "timeline",
            "data": timeline_data,
            "x_axis": "time_seconds",
            "y_axis": "track_id",
            "title": "泄漏点时间线"
        }
    
    def _build_confidence_chart_data(self, leakage_points: List[Any], 
                                   confidence_intervals: Optional[Dict]) -> Dict[str, Any]:
        """构建置信度分布图表数据"""
        if confidence_intervals:
            # 使用提供的置信度区间数据
            intervals_data = []
            for interval, data in confidence_intervals.items():
                intervals_data.append({
                    "interval": interval,
                    "count": data.get("frame_count", 0),
                    "percentage": data.get("percentage", 0.0)
                })
        else:
            # 从泄漏点数据中计算置信度分布
            confidence_values = []
            for point in leakage_points:
                if hasattr(point, '__dict__'):
                    point_data = point.__dict__
                else:
                    point_data = point
                
                avg_confidence = point_data.get("confidence_stats", {}).get("average", 0)
                confidence_values.append(avg_confidence)
            
            # 创建置信度区间
            intervals_data = self._create_confidence_intervals(confidence_values)
        
        return {
            "chart_type": "bar",
            "data": intervals_data,
            "x_axis": "interval",
            "y_axis": "count",
            "title": "置信度分布"
        }
    
    def _build_gps_chart_data(self, leakage_points: List[Any]) -> Dict[str, Any]:
        """构建GPS轨迹图表数据"""
        # 从泄漏点中提取GPS相关数据
        gps_points = []
        
        for point in leakage_points:
            if hasattr(point, '__dict__'):
                point_data = point.__dict__
            else:
                point_data = point
            
            spatial_info = point_data.get("spatial_info", {})
            if "gps_location" in spatial_info:
                gps_points.append({
                    "track_id": point_data.get("track_id", "unknown"),
                    "latitude": spatial_info["gps_location"].get("latitude", 0),
                    "longitude": spatial_info["gps_location"].get("longitude", 0),
                    "confidence": point_data.get("confidence_stats", {}).get("average", 0)
                })
        
        return {
            "chart_type": "map",
            "data": gps_points,
            "x_axis": "longitude",
            "y_axis": "latitude",
            "title": "GPS轨迹图",
            "has_gps_data": len(gps_points) > 0
        }
    
    def _build_risk_pie_chart_data(self, leakage_points: List[Any]) -> Dict[str, Any]:
        """构建风险等级饼图数据"""
        risk_counts = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        
        for point in leakage_points:
            if hasattr(point, '__dict__'):
                point_data = point.__dict__
            else:
                point_data = point
            
            risk_level = point_data.get("risk_assessment", {}).get("risk_level", "low")
            if risk_level in risk_counts:
                risk_counts[risk_level] += 1
        
        pie_data = []
        total = sum(risk_counts.values())
        
        for level, count in risk_counts.items():
            if count > 0:
                pie_data.append({
                    "label": level.upper(),
                    "value": count,
                    "percentage": (count / total * 100) if total > 0 else 0
                })
        
        return {
            "chart_type": "pie",
            "data": pie_data,
            "title": "风险等级分布"
        }
    
    def _build_dispersion_chart_data(self, dispersion_behavior_summary: Optional[Dict]) -> Dict[str, Any]:
        """构建扩散行为图表数据"""
        if not dispersion_behavior_summary:
            return {
                "chart_type": "bar",
                "data": [],
                "title": "扩散行为分析",
                "has_data": False
            }
        
        behavior_data = []
        for behavior_type, data in dispersion_behavior_summary.items():
            if isinstance(data, dict) and "count" in data:
                behavior_data.append({
                    "behavior": behavior_type,
                    "count": data["count"],
                    "percentage": data.get("percentage", 0.0)
                })
        
        return {
            "chart_type": "bar",
            "data": behavior_data,
            "x_axis": "behavior",
            "y_axis": "count",
            "title": "扩散行为分析",
            "has_data": len(behavior_data) > 0
        }
    
    def _build_time_segments_chart_data(self, time_segments_analysis: Optional[Dict]) -> Dict[str, Any]:
        """构建时间段分析图表数据"""
        if not time_segments_analysis:
            return {
                "chart_type": "line",
                "data": [],
                "title": "时间段分析",
                "has_data": False
            }
        
        segments_data = []
        segments = time_segments_analysis.get("segments", [])
        
        for segment in segments:
            segments_data.append({
                "start_time": segment.get("start_time", 0),
                "end_time": segment.get("end_time", 0),
                "activity_level": segment.get("activity_level", 0),
                "leakage_count": segment.get("leakage_count", 0)
            })
        
        return {
            "chart_type": "line",
            "data": segments_data,
            "x_axis": "time",
            "y_axis": "activity_level",
            "title": "时间段活跃度分析",
            "has_data": len(segments_data) > 0
        }
    
    def _calculate_risk_score(self, risk_distribution: Dict[str, int]) -> float:
        """计算风险评分"""
        weights = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        total_points = sum(risk_distribution.values())
        
        if total_points == 0:
            return 0.0
        
        weighted_sum = sum(count * weights[level] for level, count in risk_distribution.items())
        return (weighted_sum / (total_points * 4)) * 100  # 转换为百分比
    
    def _generate_risk_recommendations(self, overall_risk_level: str, 
                                     risk_distribution: Dict[str, int]) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        if overall_risk_level in ["high", "critical"]:
            recommendations.append("建议立即进行现场检查和维修")
            recommendations.append("加强安全监控措施")
        
        if risk_distribution["critical"] > 0:
            recommendations.append(f"发现{risk_distribution['critical']}个严重泄漏点，需要紧急处理")
        
        if risk_distribution["high"] > 0:
            recommendations.append(f"发现{risk_distribution['high']}个高风险泄漏点，建议优先处理")
        
        if sum(risk_distribution.values()) > 5:
            recommendations.append("泄漏点数量较多，建议制定系统性维修计划")
        
        if not recommendations:
            recommendations.append("当前风险等级较低，建议继续定期监控")
        
        return recommendations
    
    def _create_confidence_intervals(self, confidence_values: List[float]) -> List[Dict[str, Any]]:
        """创建置信度区间统计"""
        if not confidence_values:
            return []
        
        intervals = [
            ("0.0-0.2", 0.0, 0.2),
            ("0.2-0.4", 0.2, 0.4),
            ("0.4-0.6", 0.4, 0.6),
            ("0.6-0.8", 0.6, 0.8),
            ("0.8-1.0", 0.8, 1.0)
        ]
        
        interval_counts = {name: 0 for name, _, _ in intervals}
        total = len(confidence_values)
        
        for value in confidence_values:
            for name, min_val, max_val in intervals:
                if min_val <= value < max_val or (name == "0.8-1.0" and value == 1.0):
                    interval_counts[name] += 1
                    break
        
        result = []
        for name, count in interval_counts.items():
            result.append({
                "interval": name,
                "count": count,
                "percentage": (count / total * 100) if total > 0 else 0.0
            })
        
        return result