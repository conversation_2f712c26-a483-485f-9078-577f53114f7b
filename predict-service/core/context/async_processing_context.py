#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步视频处理上下文类
封装异步视频处理过程中的所有状态变量和配置
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import time


@dataclass
class AsyncProcessingContext:
    """异步视频处理上下文，封装处理流程状态"""

    # ========== 基本任务信息 ==========
    task_id: str
    video_id: str
    user_id: str
    confidence_threshold: float
    gas_leakage_mode: bool = False
    request_uuid: Optional[str] = None  # 请求UUID用于日志追踪
    
    # 视频帧信息
    total_frames: int = 0
    frame_counter: int = 0  # 已处理帧数

    # ========== 文件路径配置 ==========
    temp_frames_dir: Optional[str] = None
    output_dir: str = ""
    local_output_path: Optional[str] = None
    minio_output_path: Optional[str] = None

    # ========== 视频元数据 ==========
    frame_width: int = 0
    frame_height: int = 0
    fps: float = 0.0
    total_frames: int = 0  # 视频总帧数
    frame_counter: int = 0  # 已处理帧数

    # ========== 处理状态跟踪 ==========
    start_time: float = field(default_factory=time.time)
    frame_processing_time: float = 0.0
    synthesis_time: float = 0.0
    synthesis_success: bool = False

    # ========== 汇总数据 ==========
    gas_analysis_summary: Optional[Dict[str, Any]] = None
    temporal_gps_summary: Optional[Dict[str, Any]] = None

    # ========== 配置选项 ==========
    enable_video_synthesis: bool = True
    enable_minio_upload: bool = False # 当前已注释
    save_summary_files: bool = True

    def get_total_elapsed_time(self) -> float:
        """获取总体处理时间"""
        return time.time() - self.start_time

    def is_partial_success(self) -> bool:
        """判断是否为部分成功（逐帧处理成功但合成失败）"""
        return self.frame_processing_time > 0 and not self.synthesis_success

    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理过程摘要"""
        return {
            'total_time': self.get_total_elapsed_time(),
            'frame_processing_time': self.frame_processing_time,
            'synthesis_time': self.synthesis_time,
            'synthesis_success': self.synthesis_success,
            'status': 'success' if self.synthesis_success else ('partial_success' if self.is_partial_success() else 'processing')
        }

    def has_gas_analysis(self) -> bool:
        """是否有气体分析数据"""
        return self.gas_leakage_mode and self.gas_analysis_summary is not None

    def has_temporal_gps_data(self) -> bool:
        """是否有时间戳GPS数据"""
        return self.temporal_gps_summary is not None

    def should_synthesize_video(self) -> bool:
        """是否应该进行视频合成"""
        return (self.enable_video_synthesis and
                self.temp_frames_dir and
                self.local_output_path)

    def get_video_output_path(self) -> Optional[str]:
        """获取最终视频输出路径"""
        if self.synthesis_success and self.enable_minio_upload:
            return self.minio_output_path
        elif self.synthesis_success:
            return self.local_output_path
        return None