from typing import Dict, List, Optional, Any
import logging

from models.label_level import LabelLevel

logger = logging.getLogger(__name__)

class LabelConfigManager:
    """动态标签配置管理器，支持用户完全自定义层级"""

    def __init__(self, config_dict: Dict[str, Any]):
        self.levels: Dict[str, LabelLevel] = {}
        self.label_to_level: Dict[str, str] = {}
        self.default_level_name: Optional[str] = None
        self._parse_config(config_dict)

    def _parse_config(self, config_dict: Dict[str, Any]) -> None:
        """解析用户自定义配置"""
        try:
            label_levels = config_dict.get('label_levels', {})

            if not label_levels:
                logger.warning("No label levels provided, creating minimal default")
                self._create_minimal_default()
                return

            for level_name, level_data in label_levels.items():
                # 解析颜色
                color_data = level_data.get('color', [128, 128, 128]) # 默认灰色
                if isinstance(color_data, list) and len(color_data) >= 3:
                    color = tuple(color_data[:3])
                else:
                    color = (128, 128, 128)

                # 解析标签列表
                labels = level_data.get('labels', [])
                if isinstance(labels, str):
                    # 如果是字符串，按逗号分割
                    labels = [label.strip() for label in labels.split(',') if label.strip()]
                elif not isinstance(labels, list):
                    labels = []

                # 创建层级对象
                level = LabelLevel(
                    name=level_name,
                    labels=labels,
                    color=color,
                    alert_threshold=float(level_data.get('alert_threshold', 0.5)),
                    notification_priority=level_data.get('notification_priority', 'medium')
                )

                self.levels[level_name] = level

                # 建立标签到层级的映射
                for label in labels:
                    if label: # 确保标签不为空
                        self.label_to_level[label.lower()] = level_name

                # 设置第一个层级为默认层级
                if self.default_level_name is None:
                    self.default_level_name = level_name

            logger.info(f"Loaded {len(self.levels)} label levels with {len(self.label_to_level)} labels")

        except Exception as e:
            logger.error(f"Error parsing label config: {e}")
            self._create_minimal_default()

    def _create_minimal_default(self):
        """创建最小默认配置"""
        default_level = LabelLevel(
            name="default",
            labels=[],
            color=(0, 255, 0), # 绿色
            alert_threshold=0.5,
            notification_priority="medium"
        )
        self.levels["default"] = default_level
        self.default_level_name = "default"

    def get_level_for_label(self, label: str) -> LabelLevel:
        """获取标签对应的层级配置"""
        # 先尝试精确匹配
        level_name = self.label_to_level.get(label.lower())

        if level_name and level_name in self.levels:
            return self.levels[level_name]

        # 如果没有找到，返回默认层级
        if self.default_level_name and self.default_level_name in self.levels:
            return self.levels[self.default_level_name]

        # 如果连默认层级都没有，返回第一个层级
        if self.levels:
            return next(iter(self.levels.values()))

        # 最后的fallback
        return LabelLevel("unknown", [], (128, 128, 128), 0.5, "low")

    def get_level_by_name(self, level_name: str) -> Optional[LabelLevel]:
        """根据层级名称获取层级配置"""
        return self.levels.get(level_name)

    def should_alert(self, label: str, confidence: float) -> bool:
        """判断是否需要触发告警"""
        level = self.get_level_for_label(label)
        return confidence >= level.alert_threshold

    def get_all_levels(self) -> Dict[str, LabelLevel]:
        """获取所有层级配置"""
        return self.levels.copy()

    def get_labels_for_level(self, level_name: str) -> List[str]:
        """获取指定层级的所有标签"""
        level = self.levels.get(level_name)
        return level.labels if level else []

    @staticmethod
    def create_default() -> 'LabelConfigManager':
        """创建默认配置示例"""
        return LabelConfigManager({
            'label_levels': {
                'Critical': {
                    'labels': ['smoke', 'fire'],
                    'color': [255, 0, 0], # 红色
                    'alert_threshold': 0.7,
                    'notification_priority': 'high'
                },
                'Normal': {
                    'labels': ['steam'],
                    'color': [255, 255, 0], # 黄色
                    'alert_threshold': 0.5,
                    'notification_priority': 'medium'
                },
                'Others': {
                    'labels': [],
                    'color': [0, 255, 0], # 绿色
                    'alert_threshold': 0.3,
                    'notification_priority': 'low'
                }
            }
        })

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'label_levels': {
                name: {
                    'labels': level.labels,
                    'color': list(level.color),
                    'alert_threshold': level.alert_threshold,
                    'notification_priority': level.notification_priority
                }
                for name, level in self.levels.items()
            }
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"LabelConfigManager with {len(self.levels)} levels: {list(self.levels.keys())}"