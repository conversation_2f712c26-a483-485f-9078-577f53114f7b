import os
import logging
import configparser
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器单例类 - 统一管理系统配置"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._initialized = True
        self._load_config()

    def _get_config_file(self) -> str:
        """根据环境变量决定使用哪个配置文件"""
        environment = os.getenv('ENVIRONMENT', 'dev').lower()

        config_files = {
            'production': 'config/config-prod.ini',
            'prod': 'config/config-prod.ini',
            'development': 'config/config-dev.ini',
            'dev': 'config/config-dev.ini',
            'test': 'config/config-test.ini'
        }

        config_file = config_files.get(environment, 'config/config.ini')

        # 检查文件是否存在
        if not os.path.exists(config_file):
            logger.warning(f"Config file {config_file} not found, falling back to config/config.ini")
            config_file = 'config/config.ini'

        logger.info(f"Using config file: {config_file} (environment: {environment})")
        return config_file

    def _load_config(self):
        """加载配置文件并初始化配置项"""
        try:
            config = configparser.ConfigParser()
            config_file = self._get_config_file()
            config.read(config_file, encoding='utf-8')

            # 基础系统配置
            self._load_general_config(config)
            # 模型配置
            self._load_model_config(config)
            # 存储配置
            self._load_storage_config(config)
            # 网络服务配置
            self._load_network_config(config)
            # 日志配置
            self._load_logging_config(config)

            # 初始化线程池
            self._executor = None

            logger.info(f"Configuration loaded successfully from {config_file}")
            self._log_config_summary()

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}", exc_info=True)
            raise

    def _load_general_config(self, config: configparser.ConfigParser):
        """加载通用配置"""
        self.cpu_count = os.cpu_count()
        self.max_workers = int(os.getenv('MAX_WORKERS',
                                       config.get('general', 'max_workers', fallback=str(self.cpu_count))))
        self.output_dir = os.getenv('OUTPUT_DIR',
                                   config.get('general', 'output_dir', fallback='./output'))

        # 气体泄漏追踪模式配置
        self.gas_leakage_mode = os.getenv('GAS_LEAKAGE_MODE',
                                         config.get('general', 'gas_leakage_mode', fallback='true')).lower() == 'true'

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

    def _load_model_config(self, config: configparser.ConfigParser):
        """加载模型配置"""
        self.model_path = config.get('model', 'model_path', fallback='./models/best-mini.pt')
        self.enable_cuda = os.getenv('ENABLE_CUDA',
                                   config.get('model', 'enable_cuda', fallback='false')).lower() == 'true'

    def _load_storage_config(self, config: configparser.ConfigParser):
        """加载存储配置"""
        # 存储类型配置
        self.storage_type = config.get('storage', 'type', fallback='nas').lower()

        # MinIO 配置
        self.minio_url = config.get('minio', 'minio_api_url', fallback='localhost:9010')
        self.access_key = config.get('minio', 'access_key', fallback='minioadmin')
        self.secret_key = config.get('minio', 'secret_key', fallback='minioadmin')
        self.secure = config.get('minio', 'secure', fallback='false').lower() == 'true'
        self.bucket_name = config.get('minio', 'bucket_name', fallback='linked-all')

        # NAS 配置
        if config.has_section('nas'):
            # 优先使用环境变量 OUTPUT_DIR，然后是配置文件中的 root_path
            self.nas_root_path = os.getenv('OUTPUT_DIR', config.get('nas', 'root_path', fallback='./storage/nas-data'))
            self.nas_mount_point = config.get('nas', 'mount_point', fallback=None)
            self.nas_backup_enabled = config.get('nas', 'backup_enabled', fallback='false').lower() == 'true'
        else:
            # 如果没有 nas 配置段，使用环境变量或默认值
            self.nas_root_path = os.getenv('OUTPUT_DIR', './storage/nas-data')
            self.nas_mount_point = None
            self.nas_backup_enabled = False

        # 在 NAS 模式下，确保 output_dir 和 nas_root_path 保持一致
        if self.storage_type == 'nas':
            # 如果已经加载了 output_dir，确保它与 nas_root_path 一致
            # if hasattr(self, 'output_dir'):
            # # 使用已设置的 output_dir 作为 nas_root_path
            # self.nas_root_path = self.output_dir
            os.makedirs(self.nas_root_path, exist_ok=True)

    def _load_network_config(self, config: configparser.ConfigParser):
        """加载网络服务配置"""
        # RabbitMQ配置
        self.rabbitmq = {
            'host': config.get('rabbitmq', 'host', fallback='localhost'),
            'port': config.get('rabbitmq', 'port', fallback='5672'),
            'username': config.get('rabbitmq', 'username', fallback='deploy'),
            'password': config.get('rabbitmq', 'password', fallback='deploy@1234'),
            'virtual_host': config.get('rabbitmq', 'virtual_host', fallback='transfer'),
            # 增强连接配置
            'heartbeat': config.getint('rabbitmq', 'heartbeat', fallback=60),
            'blocked_connection_timeout': config.getint('rabbitmq', 'blocked_connection_timeout', fallback=30),
            'socket_timeout': config.getint('rabbitmq', 'socket_timeout', fallback=10),
            'stack_timeout': config.getint('rabbitmq', 'stack_timeout', fallback=15),
            'connection_attempts': config.getint('rabbitmq', 'connection_attempts', fallback=3),
            'retry_delay': config.getfloat('rabbitmq', 'retry_delay', fallback=2.0),
            # 连接池配置
            'max_connections': config.getint('rabbitmq', 'max_connections', fallback=8),
            'min_connections': config.getint('rabbitmq', 'min_connections', fallback=2),
            # 健康检查配置
            'health_check_interval': config.getint('rabbitmq', 'health_check_interval', fallback=30)
        }

        # 视频编码器配置 - 智能选择
        self.video_codec = config.get('video', 'video_codec', fallback='auto')
        self.video_quality = config.get('video', 'video_quality', fallback='balanced') # fast, balanced, high_quality

        # 初始化编码器选择逻辑
        self._init_video_codec_selection()

        # 数据库配置（可选）
        if config.has_section('database'):
            self.database = {
                'host': config.get('database', 'host', fallback='localhost'),
                'port': config.get('database', 'port', fallback='5432'),
                'database': config.get('database', 'database', fallback='uav_detector'),
                'username': config.get('database', 'username', fallback='deployop'),
                'password': config.get('database', 'password', fallback='deployop@1234')
            }
        else:
            self.database = None

        # Redis配置（可选）
        if config.has_section('redis'):
            self.redis = {
                'host': config.get('redis', 'host', fallback='localhost'),
                'port': config.get('redis', 'port', fallback='6379'),
                'db': config.get('redis', 'db', fallback='0')
            }
        else:
            self.redis = None

    def _load_logging_config(self, config: configparser.ConfigParser):
        """加载日志配置"""
        if config.has_section('logging'):
            self.logging = {
                'level': config.get('logging', 'level', fallback='INFO'),
                'console': config.get('logging', 'console', fallback='true').lower() == 'true',
                'file': config.get('logging', 'file', fallback='./logs/predict-service.log')
            }
        else:
            self.logging = {
                'level': 'INFO',
                'console': True,
                'file': './logs/predict-service.log'
            }

        # 确保日志目录存在
        log_dir = os.path.dirname(self.logging['file'])
        os.makedirs(log_dir, exist_ok=True)

    def _log_config_summary(self):
        """记录配置摘要"""
        logger.info(f"Storage type: {self.storage_type}")
        if self.storage_type == 'nas':
            logger.info(f"NAS root path: {self.nas_root_path}")
        elif self.storage_type == 'minio':
            logger.info(f"MinIO endpoint: {self.minio_url}")

        logger.info(f"CUDA enabled: {self.enable_cuda}")
        logger.info(f"Max workers: {self.max_workers}")
        logger.info(f"Output directory: {self.output_dir}")

    @property
    def executor(self):
        """懒加载线程池"""
        if self._executor is None:
            self._executor = ThreadPoolExecutor(max_workers=self.max_workers)
        return self._executor

    def cleanup(self):
        """清理资源"""
        if self._executor:
            self._executor.shutdown()
            self._executor = None

    def reload(self):
        """重新加载配置"""
        logger.info("Reloading configuration...")
        old_executor = self._executor
        self._load_config()

        # 关闭旧的线程池
        if old_executor:
            old_executor.shutdown()

    @property
    def rabbitmq_config(self) -> Dict[str, str]:
        """获取RabbitMQ配置"""
        return self.rabbitmq.copy()

    @property
    def database_config(self) -> Optional[Dict[str, str]]:
        """获取数据库配置"""
        return self.database.copy() if self.database else None

    @property
    def redis_config(self) -> Optional[Dict[str, str]]:
        """获取Redis配置"""
        return self.redis.copy() if self.redis else None

    def get_environment(self) -> str:
        """获取当前环境"""
        return os.getenv('ENVIRONMENT', 'dev').lower()

    def get_config_value(self, section: str, key: str, fallback=None) -> Any:
        """获取配置值的通用方法"""
        try:
            config = configparser.ConfigParser()
            config_file = self._get_config_file()
            config.read(config_file, encoding='utf-8')
            return config.get(section, key, fallback=fallback)
        except Exception as e:
            logger.warning(f"Failed to get config value {section}.{key}: {e}")
            return fallback

    def __str__(self):
        """返回配置信息的字符串表示"""
        config_info = [
            f"ConfigManager (Environment: {self.get_environment()}):",
            f" Output Directory: {self.output_dir}",
            f" Max Workers: {self.max_workers}",
            f" Model Path: {self.model_path}",
            f" CUDA Enabled: {self.enable_cuda}",
            f" Storage Type: {self.storage_type}",
        ]

        if self.storage_type == 'nas':
            config_info.append(f" NAS Root: {self.nas_root_path}")
        elif self.storage_type == 'minio':
            config_info.append(f" MinIO URL: {self.minio_url}")

        config_info.append(f" RabbitMQ Host: {self.rabbitmq['host']}")

        if self.database:
            config_info.append(f" Database Host: {self.database['host']}")

        if self.redis:
            config_info.append(f" Redis Host: {self.redis['host']}")

        return "\n".join(config_info)

    def _init_video_codec_selection(self):
        """初始化视频编码器选择逻辑"""
        # 根据使用场景的编码器优先级
        self.codec_priority = {
            'fast': ['MJPG', 'mp4v', 'XVID', 'h264'], # 速度优先
            'balanced': ['h264', 'mp4v', 'MJPG', 'XVID'], # 平衡（默认）
            'high_quality': ['h264', 'H265', 'mp4v', 'MJPG'] # 质量优先
        }

        # 根据文件用途的编码器选择
        self.codec_by_purpose = {
            'temp': 'MJPG', # 临时文件 - 速度最快
            'output': 'h264', # 输出文件 - 平衡
            'storage': 'h264', # 存储文件 - 压缩率好
            'streaming': 'h264' # 流媒体 - 兼容性好
        }

    def get_optimal_codec(self, purpose='output', quality=None):
        """获取最优编码器

        Args:
            purpose: 'temp', 'output', 'storage', 'streaming'
            quality: 'fast', 'balanced', 'high_quality'

        Returns:
            str: 推荐的编码器名称
        """
        if quality is None:
            quality = self.video_quality

        # 如果指定了用途，优先使用用途推荐
        if purpose in self.codec_by_purpose:
            return self.codec_by_purpose[purpose]

        # 否则根据质量设置选择
        if quality in self.codec_priority:
            return self.codec_priority[quality][0]

        return 'h264' # 默认回退

    def get_codec_fallback_list(self, purpose='output', quality=None):
        """获取编码器回退列表

        Args:
            purpose: 文件用途
            quality: 质量级别

        Returns:
            list: 按优先级排序的编码器列表
        """
        if quality is None:
            quality = self.video_quality

        # 基础列表
        base_list = self.codec_priority.get(quality, self.codec_priority['balanced'])

        # 根据用途调整
        if purpose == 'temp':
            # 临时文件优先速度
            return ['MJPG', 'mp4v', 'XVID', 'h264']
        elif purpose == 'streaming':
            # 流媒体优先兼容性
            return ['h264', 'mp4v', 'MJPG', 'XVID']
        else:
            return base_list.copy()