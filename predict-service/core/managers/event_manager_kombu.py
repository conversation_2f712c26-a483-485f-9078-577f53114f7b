import logging
import json
import time
import threading
from typing import Callable, Dict, Any
from kombu import Connection, Queue, Exchange, Producer, Consumer
from kombu.exceptions import ConnectionError, ChannelError, OperationalError
import socket

logger = logging.getLogger(__name__)

class EventManagerKombu:
    """使用Kombu的事件管理器 - 更稳定的RabbitMQ客户端"""
    _instance = None
    _instance_lock = threading.Lock()

    # 重试与等待配置参数
    MAX_RETRIES = 5
    RETRY_INTERVAL = 2 # 单位：秒
    CONSUME_RETRY_INTERVAL = 5 # 消费异常后的等待时间
    CONNECTION_TIMEOUT = 30 # 连接超时
    HEARTBEAT = 120 # 心跳间隔（增加到120秒，减少心跳丢失）

    def __new__(cls, config=None):
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = super(EventManagerKombu, cls).__new__(cls)
                    if config is None:
                        from .config_manager import ConfigManager
                        config = ConfigManager()
                    cls._instance.config = config
                    cls._instance.subscribers = {}
                    cls._instance._consumers = {} # 添加消费者管理

                    # 连接管理
                    cls._instance._connection_lock = threading.RLock()
                    cls._instance._connection = None
                    cls._instance._producer = None

                    # 队列和交换机定义
                    cls._instance._setup_exchanges_and_queues()
                    cls._instance._connect()

        return cls._instance

    def _get_connection_url(self) -> str:
        """构建 RabbitMQ 连接URL"""
        rabbitmq_config = self.config.rabbitmq
        # 修复vhost配置问题
        vhost = rabbitmq_config.get('virtual_host', '/')
        if vhost == '/':
            vhost_path = '/'
        else:
            vhost_path = f"/{vhost}"

        connection_url = (f"amqp://{rabbitmq_config['username']}:{rabbitmq_config['password']}"
                         f"@{rabbitmq_config['host']}:{rabbitmq_config['port']}{vhost_path}")

        logger.info(f" RabbitMQ连接URL: amqp://{rabbitmq_config['username']}:***@{rabbitmq_config['host']}:{rabbitmq_config['port']}{vhost_path}")
        return connection_url

    def _setup_exchanges_and_queues(self):
        """设置交换机和队列定义"""
        # 默认交换机
        self.default_exchange = Exchange('', type='direct')

        # 队列定义
        self.queues = {
            'notification_queue': Queue('notification_queue', exchange=self.default_exchange, routing_key='notification_queue', durable=True),
            'fail_queue': Queue('fail_queue', exchange=self.default_exchange, routing_key='fail_queue', durable=True),
            'for_java': Queue('for_java', exchange=self.default_exchange, routing_key='for_java', durable=True), # 添加for_java队列
        }

    def _connect(self):
        """建立连接"""
        try:
            connection_url = self._get_connection_url()

            # 连接参数
            connection_kwargs = {
                'heartbeat': self.HEARTBEAT,
                'connect_timeout': self.CONNECTION_TIMEOUT,
                'socket_timeout': self.CONNECTION_TIMEOUT,
                'transport_options': {
                    'socket_keepalive': True,
                    'socket_keepalive_idle': 60,
                    'socket_keepalive_intvl': 10,
                    'socket_keepalive_cnt': 3,
                }
            }

            with self._connection_lock:
                if self._connection:
                    try:
                        self._connection.close()
                    except:
                        pass

                self._connection = Connection(connection_url, **connection_kwargs)
                self._connection.ensure_connection(max_retries=self.MAX_RETRIES)

                # 创建生产者
                self._producer = Producer(self._connection)

                # 声明所有队列
                for queue in self.queues.values():
                    queue.declare(channel=self._connection.channel())

                logger.info(" Kombu RabbitMQ连接建立成功")

        except Exception as e:
            logger.error(f" Kombu RabbitMQ连接失败: {e}")
            raise

    def _ensure_connection(self):
        """确保连接可用"""
        try:
            with self._connection_lock:
                if not self._connection or not self._connection.connected:
                    logger.warning(" 连接已断开，正在重新连接...")
                    self._connect()

                # 测试连接
                self._connection.heartbeat_check()
                return True

        except Exception as e:
            logger.error(f" 连接检查失败: {e}")
            try:
                self._connect()
                return True
            except Exception as reconnect_error:
                logger.error(f" 重连失败: {reconnect_error}")
                return False

    def _is_amqp_protocol_error(self, error_str: str) -> bool:
        """检测是否为AMQP协议错误"""
        amqp_error_patterns = [
            'frame' in error_str and 'expecting' in error_str,
            'frame_end' in error_str,
            'received frame' in error_str,
            'amqp' in error_str and 'protocol' in error_str,
            'connection reset' in error_str,
            'unexpected frame' in error_str,
            'frame 0' in error_str and 'type' in error_str, # 添加之前遇到的错误模式
            'frame_end 0x' in error_str, # 添加新遇到的错误模式
        ]
        return any(amqp_error_patterns)

    def _validate_connection_health(self) -> bool:
        """验证连接健康状态，避免使用无效连接"""
        try:
            if self._connection is None:
                return False

            # 检查连接状态
            if not self._connection.connected:
                logger.debug("连接未激活")
                return False

            # 尝试执行一个轻量级操作来验证连接
            try:
                info = self._connection.info()
                logger.debug(f"连接健康检查通过: {info}")
                return True
            except Exception as health_check_error:
                logger.warning(f"连接健康检查失败: {health_check_error}")
                return False

        except Exception as e:
            logger.warning(f"连接健康验证异常: {e}")
            return False

    def _force_reconnect(self):
        """强制重新连接 - 用于处理协议错误"""
        try:
            with self._connection_lock:
                logger.info(" 执行强制重连...")

                # 关闭现有连接
                if self._connection is not None:
                    try:
                        self._connection.close()
                    except Exception:
                        pass # 忽略关闭错误

                # 重置连接和生产者
                self._connection = None
                self._producer = None

                # 重新建立连接
                self._connect()

                logger.info(" 强制重连完成")

        except Exception as e:
            logger.error(f" 强制重连失败: {e}")

    def _get_thread_safe_producer(self):
        """获取线程安全的生产者连接"""
        thread_id = threading.current_thread().ident
        with self._connection_lock:
            # 使用健康检查验证连接状态
            if (self._connection is None or
                self._producer is None or
                not self._validate_connection_health()):

                logger.debug(f"创建新的生产者连接 (线程: {thread_id})")
                # 清理旧连接
                if self._connection is not None:
                    try:
                        self._connection.close()
                    except Exception:
                        pass

                self._connection = None
                self._producer = None

                # 重新连接
                self._connect()

            # 返回当前有效的生产者
            return self._producer

    def send_message_to_queue(self, task_id: str, message: Dict[str, Any], retry_count: int = 0, queue_name: str = 'for_java') -> bool:
        """发送消息到队列 - 增强版"""
        if retry_count >= self.MAX_RETRIES:
            logger.error(f" 发送消息失败，已达到最大重试次数: {self.MAX_RETRIES}")
            return False

        try:
            # 获取线程安全的生产者
            producer = self._get_thread_safe_producer()
            if producer is None:
                raise ConnectionError("无法获取有效的生产者连接")

            # 准备消息
            message_body = json.dumps(message, ensure_ascii=False)

            # 线程安全的消息发送
            thread_id = threading.current_thread().ident
            logger.debug(f"发送消息到队列 {queue_name} (线程: {thread_id})")

            # 在锁保护下进行发送
            with self._connection_lock:
                producer.publish(
                    message_body,
                    routing_key=queue_name,
                    delivery_mode=2, # 持久化消息
                    retry=True,
                    retry_policy={
                        'interval_start': 0,
                        'interval_step': 0.2,
                        'interval_max': 0.2,
                        'max_retries': 3,
                    }
                )

            logger.debug(f" 消息发送成功 - 任务ID: {task_id}")
            return True

        except (ConnectionError, ChannelError, OperationalError, socket.error) as e:
            retry_count += 1
            error_str = str(e).lower()

            # 检查是否是AMQP协议错误
            if self._is_amqp_protocol_error(error_str):
                logger.warning(f" AMQP协议错误，强制重置连接: {e}")
                self._force_reconnect()
            else:
                logger.warning(f" 发送消息失败 (尝试 {retry_count}/{self.MAX_RETRIES}): {e}")

            if retry_count < self.MAX_RETRIES:
                time.sleep(self.RETRY_INTERVAL * retry_count) # 指数退避
                return self.send_message_to_queue(task_id, message, retry_count)
            else:
                logger.error(f" 发送消息最终失败 - 任务ID: {task_id}")
                return False

        except Exception as e:
            logger.error(f" 发送消息时发生未知错误: {e}", exc_info=True)
            return False

    def send_fail_message_to_queue(self, message: Dict[str, Any], retry_count: int = 0) -> bool:
        """发送失败消息到失败队列"""
        if retry_count >= self.MAX_RETRIES:
            logger.error(f" 发送失败消息失败，已达到最大重试次数: {self.MAX_RETRIES}")
            return False

        try:
            # 从消息中提取task_id (兼容性处理)
            task_id = message.get('task_id', message.get('taskId', 'unknown'))

            # 获取线程安全的生产者
            producer = self._get_thread_safe_producer()
            if producer is None:
                raise ConnectionError("无法获取有效的生产者连接")

            # 准备消息
            message_body = json.dumps(message, ensure_ascii=False)

            # 线程安全的失败消息发送
            thread_id = threading.current_thread().ident
            logger.debug(f"发送失败消息到fail_queue (线程: {thread_id})")

            # 发送消息到失败队列
            with self._connection_lock:
                producer.publish(
                    message_body,
                    routing_key='fail_queue',
                    delivery_mode=2, # 持久化消息
                    retry=True,
                    retry_policy={
                        'interval_start': 0,
                        'interval_step': 0.2,
                        'interval_max': 0.2,
                        'max_retries': 3,
                    }
                )

            logger.info(f" 失败消息发送成功 - 任务ID: {task_id}")
            return True

        except (ConnectionError, ChannelError, OperationalError, socket.error) as e:
            retry_count += 1
            error_str = str(e).lower()

            # 检查是否是AMQP协议错误
            if self._is_amqp_protocol_error(error_str):
                logger.warning(f" AMQP协议错误，强制重置连接: {e}")
                self._force_reconnect()
            else:
                logger.warning(f" 发送失败消息失败 (尝试 {retry_count}/{self.MAX_RETRIES}): {e}")

            if retry_count < self.MAX_RETRIES:
                time.sleep(self.RETRY_INTERVAL * retry_count)
                return self.send_fail_message_to_queue(message, retry_count)
            else:
                task_id = message.get('task_id', message.get('taskId', 'unknown'))
                logger.error(f" 发送失败消息最终失败 - 任务ID: {task_id}")
                return False

        except Exception as e:
            logger.error(f" 发送失败消息时发生未知错误: {e}", exc_info=True)
            return False

    def subscribe_to_queue(self, queue_name: str, callback: Callable[[str], None]):
        """订阅队列消息"""
        if queue_name in self._consumers:
            logger.warning(f" 队列 {queue_name} 已被订阅")
            return False

        # 将回调函数存储到_consumers中
        self._consumers[queue_name] = callback

        def start_consuming():
            """启动消息消费循环"""
            while True:
                try:
                    if not self._ensure_connection():
                        logger.error(" 无法建立连接，等待重试...")
                        time.sleep(self.CONSUME_RETRY_INTERVAL)
                        continue

                    # 创建消费者
                    def message_handler(body, message):
                        try:
                            message_str = body.decode('utf-8') if isinstance(body, bytes) else str(body)
                            callback(message_str)
                            message.ack() # 确认消息
                        except Exception as e:
                            logger.error(f" 处理消息失败: {e}", exc_info=True)
                            message.reject() # 拒绝消息

                    queue = self.queues.get(queue_name)
                    if not queue:
                        logger.error(f" 未找到队列定义: {queue_name}")
                        return

                    with Consumer(self._connection, [queue], callbacks=[message_handler]):
                        logger.info(f" 开始消费队列: {queue_name}")
                        # 开始消费
                        while True:
                            try:
                                self._connection.drain_events(timeout=10)
                            except socket.timeout:
                                # 超时是正常的，继续循环
                                continue
                            except Exception as e:
                                logger.error(f" 消费消息时出错: {e}")
                                raise

                except (ConnectionError, ChannelError, OperationalError, socket.error) as e:
                    error_str = str(e).lower()

                    # 检查是否是AMQP协议错误
                    if self._is_amqp_protocol_error(error_str):
                        logger.warning(f" 消费时AMQP协议错误: {e}")
                        # 在消费线程中不直接调用_force_reconnect，而是等待重试
                        time.sleep(self.CONSUME_RETRY_INTERVAL * 3) # 更长的等待时间处理协议错误
                    else:
                        logger.warning(f" 消费连接异常: {e}")
                        time.sleep(self.CONSUME_RETRY_INTERVAL)
                    continue

                except Exception as e:
                    logger.error(f" 消费时发生未知错误: {e}", exc_info=True)
                    time.sleep(self.CONSUME_RETRY_INTERVAL)
                    continue

        # 在后台线程中启动消费
        thread = threading.Thread(target=start_consuming, daemon=True)
        thread.start()
        logger.info(f" 队列订阅已启动: {queue_name}")
        return True

    def subscribe(self, event_name: str, callback: Callable[[str], None]):
        """订阅事件 - 兼容原EventManager的API"""
        # 将事件名映射到队列名
        queue_mapping = {
            'message_received': 'notification_queue',
            'fail_received': 'fail_queue'
        }

        queue_name = queue_mapping.get(event_name, event_name)
        logger.info(f" 订阅事件: {event_name} -> 队列: {queue_name}")

        # 调用队列订阅方法
        return self.subscribe_to_queue(queue_name, callback)

    def start_consuming(self, queue_name: str, callback: Callable[[str], None] = None):
        """开始消费队列 - 兼容原EventManager的API (阻塞式)"""
        logger.info(f" 开始消费队列: {queue_name}")

        # 检查队列是否已被订阅
        if queue_name in self._consumers:
            logger.info(f" 队列 {queue_name} 已在运行，进入阻塞等待...")
            # 模拟原EventManager的阻塞行为
            try:
                while True:
                    time.sleep(1) # 阻塞等待，模拟原始start_consuming的行为
            except KeyboardInterrupt:
                logger.info(f" 队列 {queue_name} 消费被中断")
                return

        if callback:
            # 如果提供了回调函数，直接订阅
            success = self.subscribe_to_queue(queue_name, callback)
        else:
            # 没有回调时，使用内置的消息处理逻辑
            # 这模仿原EventManager的行为：接收消息后发布'message_received'事件
            def message_processor(message_body: str):
                try:
                    import json
                    # 解析消息
                    message_data = json.loads(message_body) if isinstance(message_body, str) else message_body
                    logger.debug(f" 处理队列消息 from {queue_name}: {message_data}")

                    # 触发 'message_received' 事件 (兼容原EventManager行为)
                    self.publish('message_received', message_data)

                except json.JSONDecodeError as e:
                    logger.error(f" 消息JSON解析失败: {e}")
                except Exception as e:
                    logger.error(f" 处理消息时出错: {e}", exc_info=True)

            success = self.subscribe_to_queue(queue_name, message_processor)

        if success:
            logger.info(f" 队列 {queue_name} 开始消费，进入阻塞等待...")
            # 进入阻塞等待，模拟原EventManager的行为
            try:
                while True:
                    time.sleep(1) # 保持阻塞状态
            except KeyboardInterrupt:
                logger.info(f" 队列 {queue_name} 消费被中断")
        else:
            # 不抛出异常，而是记录错误并进入等待状态
            logger.warning(f" 队列 {queue_name} 订阅失败，但继续运行以保持兼容性")
            # 仍然进入阻塞等待，避免consumer_thread不断重试
            try:
                while True:
                    time.sleep(5) # 稍长的等待时间
            except KeyboardInterrupt:
                logger.info(f" 队列 {queue_name} 等待被中断")

    def publish(self, event_type: str, data: Dict[str, Any]):
        """发布事件给本地订阅者 - 兼容原EventManager的API"""
        if event_type in self.subscribers:
            logger.debug(f" 发布事件: {event_type} 给 {len(self.subscribers[event_type])} 个订阅者")
            for callback in self.subscribers[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f" 事件回调执行失败 {event_type}: {e}", exc_info=True)

    def close(self):
        """关闭连接"""
        try:
            with self._connection_lock:
                if self._connection:
                    self._connection.close()
                    logger.info(" Kombu连接已关闭")
        except Exception as e:
            logger.error(f" 关闭连接时出错: {e}")

    def __del__(self):
        """析构函数"""
        self.close()