import logging
import json
import pika
import time
import threading
import queue
from typing import Callable, Dict, Any, Optional
from pika.exceptions import StreamLostError, ChannelWrongStateError, AMQPConnectionError
import pika.exceptions

logger = logging.getLogger(__name__)

class EventManager:
    """事件管理器 - 处理RabbitMQ消息队列通信的单例类"""
    _instance = None
    _instance_lock = threading.Lock()

    # 重试与等待配置参数
    MAX_RETRIES = 3
    RETRY_INTERVAL = 1 # 单位：秒
    CONSUME_RETRY_INTERVAL = 5 # 消费异常后的等待时间

    def __new__(cls, config=None):
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = super(EventManager, cls).__new__(cls)
                    if config is None:
                        from .config_manager import ConfigManager
                        config = ConfigManager()
                    cls._instance.config = config
                    cls._instance.subscribers = {}

                    # 分离发布与消费的连接、通道及锁
                    cls._instance.publisher_lock = threading.RLock()
                    cls._instance.publisher_connection = None
                    cls._instance.publisher_channel = None

                    cls._instance.consumer_lock = threading.RLock()
                    cls._instance.consumer_connection = None
                    cls._instance.consumer_channel = None

                    # 专用发送线程模型
                    cls._instance._send_queue = queue.Queue(maxsize=1000) # 发送队列
                    cls._instance._sender_thread = None
                    cls._instance._sender_shutdown = threading.Event()
                    cls._instance._sender_lock = threading.RLock()
                    cls._instance._sender_connection = None
                    cls._instance._sender_channel = None

                    # 启动发送线程
                    cls._instance._start_sender_thread()

                    # 保留原有连接（用于其他功能）
                    cls._instance._connect_publisher()
                    cls._instance._connect_consumer()
        return cls._instance

    def _get_connection_params(self) -> pika.ConnectionParameters:
        """构建 RabbitMQ 连接参数"""
        credentials = pika.PlainCredentials(
            self.config.rabbitmq['username'],
            self.config.rabbitmq['password']
        )
        return pika.ConnectionParameters(
            host=self.config.rabbitmq['host'],
            port=int(self.config.rabbitmq['port']),
            virtual_host=self.config.rabbitmq['virtual_host'],
            credentials=credentials,
            heartbeat=30, # 降低心跳间隔，避免连接超时
            blocked_connection_timeout=300,
            connection_attempts=3, # 连接重试次数
            retry_delay=2, # 重试间隔
            socket_timeout=10, # Socket超时
            # 启用TCP keepalive防止连接丢失
            tcp_options={
                'TCP_KEEPIDLE': 60,
                'TCP_KEEPINTVL': 30,
                'TCP_KEEPCNT': 3
            }
        )

    def _connect_publisher(self):
        """建立发布专用连接和通道"""
        retry_count = 0
        while retry_count < self.MAX_RETRIES:
            try:
                if self.publisher_connection and self.publisher_connection.is_open:
                    try:
                        self.publisher_connection.close()
                    except Exception:
                        pass

                params = self._get_connection_params()
                connection = pika.BlockingConnection(params)
                channel = connection.channel()
                channel.confirm_delivery()
                channel.basic_qos(prefetch_count=1)
                with self.publisher_lock:
                    self.publisher_connection = connection
                    self.publisher_channel = channel
                logger.info("Publisher connected to RabbitMQ")
                return
            except (StreamLostError, ChannelWrongStateError) as e:
                retry_count += 1
                logger.error(f"Publisher connection error (attempt {retry_count}/{self.MAX_RETRIES}): {e}", exc_info=True)
                time.sleep(self.RETRY_INTERVAL)
            except Exception as e:
                retry_count += 1
                logger.error(f"Unexpected publisher connection error (attempt {retry_count}/{self.MAX_RETRIES}): {e}", exc_info=True)
                time.sleep(self.RETRY_INTERVAL)
        raise Exception("Publisher exceeded maximum retries for connecting to RabbitMQ.")

    def _connect_consumer(self):
        """建立消费专用连接和通道"""
        retry_count = 0
        while retry_count < self.MAX_RETRIES:
            try:
                if self.consumer_connection and self.consumer_connection.is_open:
                    try:
                        self.consumer_connection.close()
                    except Exception:
                        pass

                params = self._get_connection_params()
                connection = pika.BlockingConnection(params)
                channel = connection.channel()
                channel.basic_qos(prefetch_count=1)
                with self.consumer_lock:
                    self.consumer_connection = connection
                    self.consumer_channel = channel
                logger.info("Consumer connected to RabbitMQ")
                return
            except (StreamLostError, ChannelWrongStateError) as e:
                retry_count += 1
                logger.error(f"Consumer connection error (attempt {retry_count}/{self.MAX_RETRIES}): {e}", exc_info=True)
                time.sleep(self.RETRY_INTERVAL)
            except Exception as e:
                retry_count += 1
                logger.error(f"Unexpected consumer connection error (attempt {retry_count}/{self.MAX_RETRIES}): {e}", exc_info=True)
                time.sleep(self.RETRY_INTERVAL)
        raise Exception("Consumer exceeded maximum retries for connecting to RabbitMQ.")

    def _start_sender_thread(self):
        """启动专用发送线程"""
        if self._sender_thread is None or not self._sender_thread.is_alive():
            self._sender_shutdown.clear()
            self._sender_thread = threading.Thread(
                target=self._sender_worker,
                name="RabbitMQ-Sender",
                daemon=True
            )
            self._sender_thread.start()
            logger.info("发送线程已启动")

    def _sender_worker(self):
        """发送线程工作函数 - 单线程处理所有发送请求"""
        logger.info("发送线程开始工作")

        while not self._sender_shutdown.is_set():
            try:
                # 等待发送任务，带超时检查关闭信号
                try:
                    send_task = self._send_queue.get(timeout=1.0)
                except queue.Empty:
                    continue # 超时继续检查关闭信号

                # 处理发送任务
                self._process_send_task(send_task)
                self._send_queue.task_done()

            except Exception as e:
                logger.error(f"发送线程出现异常: {e}", exc_info=True)
                time.sleep(0.1) # 短暂等待避免忙循环

        logger.info("发送线程已关闭")

    def _process_send_task(self, task: dict):
        """处理单个发送任务"""
        queue_name = task.get('queue_name')
        message = task.get('message')
        result_callback = task.get('callback')
        retry_count = task.get('retry_count', 0)
        max_retries = task.get('max_retries', 3)

        success = False
        error_msg = None

        try:
            # 确保发送通道可用
            if not self._ensure_sender_channel():
                raise Exception("无法确保发送通道可用")

            # 序列化消息
            if isinstance(message, (dict, list)):
                message_body = json.dumps(message, ensure_ascii=False, default=str)
            else:
                message_body = str(message)

            # 声明队列（确保队列存在）- 智能参数匹配
            with self._sender_lock:
                # 尝试用不同参数声明队列，以兼容已存在的队列
                self._smart_queue_declare(queue_name)

                # 发送消息 - 移除mandatory参数避免返回False
                try:
                    success = self._sender_channel.basic_publish(
                        exchange='',
                        routing_key=queue_name,
                        body=message_body,
                        properties=pika.BasicProperties(
                            delivery_mode=2, # 持久化
                            content_type='application/json',
                            timestamp=int(time.time())
                        ),
                        mandatory=False # 改为False避免因队列问题返回False
                    )

                    # 由于使用了confirm_delivery，检查确认状态
                    if hasattr(self._sender_channel, '_delivery_confirmation') and self._sender_channel._delivery_confirmation:
                        # 如果启用了确认模式，success应该始终为True
                        # 实际的发送状态由delivery confirmation处理
                        success = True

                    if success:
                        logger.debug(f"消息成功发送到队列: {queue_name}")
                    else:
                        error_msg = "basic_publish返回False - 可能是队列不存在或交换机问题"
                        logger.warning(f"发送到 {queue_name} 失败: {error_msg}")
                except Exception as publish_error:
                    error_msg = f"basic_publish执行异常: {publish_error}"
                    logger.error(f"发送到 {queue_name} 时异常: {error_msg}")
                    raise publish_error

        except pika.exceptions.ChannelClosedByBroker as broker_error:
            if "PRECONDITION_FAILED" in str(broker_error):
                error_msg = f"队列参数不匹配错误: {broker_error}"
                logger.warning(f"发送消息时队列参数错误: {error_msg}")

                # 对于队列参数错误，重置连接后重试
                self._reset_sender_connection()

                if retry_count < max_retries:
                    retry_task = task.copy()
                    retry_task['retry_count'] = retry_count + 1

                    # 短延迟重试
                    delay = 1
                    threading.Timer(delay, lambda: self._send_queue.put(retry_task)).start()
                    logger.info(f"队列参数错误，将在{delay}秒后重试 (第{retry_count + 1}次重试)")
                    return
                else:
                    error_msg = f"队列参数错误，已达最大重试次数: {broker_error}"
            else:
                error_msg = f"broker错误: {broker_error}"
                logger.warning(f"发送消息时broker错误: {error_msg}")
                self._reset_sender_connection()

        except (StreamLostError, ChannelWrongStateError, AMQPConnectionError) as conn_error:
            error_msg = f"连接错误: {conn_error}"
            logger.warning(f"发送消息时连接错误: {error_msg}")

            # 强制重置发送连接
            self._reset_sender_connection()

            # 重试逻辑
            if retry_count < max_retries:
                retry_task = task.copy()
                retry_task['retry_count'] = retry_count + 1

                # 指数退避延迟重试
                delay = min(1 * (2 ** retry_count), 10)
                threading.Timer(delay, lambda: self._send_queue.put(retry_task)).start()
                logger.info(f"将在{delay}秒后重试发送 (第{retry_count + 1}次重试)")
                return
            else:
                error_msg = f"连接错误，已达最大重试次数: {conn_error}"

        except Exception as unexpected_error:
            error_msg = f"发送消息时出现意外错误: {unexpected_error}"
            logger.error(error_msg, exc_info=True)

        # 执行回调（如果有）
        if result_callback:
            try:
                result_callback(success, error_msg)
            except Exception as callback_error:
                logger.error(f"发送结果回调执行失败: {callback_error}", exc_info=True)

    def _ensure_sender_channel(self) -> bool:
        """确保发送通道可用"""
        with self._sender_lock:
            try:
                # 检查连接
                if (self._sender_connection is None or
                    not self._sender_connection.is_open):
                    self._connect_sender()

                # 检查通道
                if (self._sender_channel is None or
                    not self._sender_channel.is_open):
                    self._sender_channel = self._sender_connection.channel()
                    self._sender_channel.confirm_delivery()
                    self._sender_channel.basic_qos(prefetch_count=1)

                return True

            except Exception as e:
                logger.error(f"确保发送通道时出错: {e}", exc_info=True)
                self._reset_sender_connection()
                return False

    def _connect_sender(self):
        """建立发送专用连接"""
        retry_count = 0
        while retry_count < self.MAX_RETRIES:
            try:
                # 清理旧连接
                if self._sender_connection and self._sender_connection.is_open:
                    try:
                        self._sender_connection.close()
                    except Exception:
                        pass

                params = self._get_connection_params()
                connection = pika.BlockingConnection(params)
                channel = connection.channel()
                channel.confirm_delivery()
                channel.basic_qos(prefetch_count=1)

                self._sender_connection = connection
                self._sender_channel = channel
                logger.info("发送专用连接已建立")
                return

            except Exception as e:
                retry_count += 1
                logger.error(f"发送连接失败 (第{retry_count}次尝试): {e}", exc_info=True)
                if retry_count < self.MAX_RETRIES:
                    time.sleep(self.RETRY_INTERVAL * retry_count) # 递增延迟

        raise Exception("发送连接超过最大重试次数")

    def _reset_sender_connection(self):
        """重置发送连接"""
        with self._sender_lock:
            if self._sender_channel:
                try:
                    self._sender_channel.close()
                except Exception:
                    pass
                self._sender_channel = None

            if self._sender_connection:
                try:
                    self._sender_connection.close()
                except Exception:
                    pass
                self._sender_connection = None

        logger.info("发送连接已重置")

    def _smart_queue_declare(self, queue_name: str):
        """智能队列声明 - 兼容已存在队列的参数"""
        from pika.exceptions import ChannelClosedByBroker

        # 首先尝试被动声明（passive=True）来检查队列是否已存在
        try:
            result = self._sender_channel.queue_declare(queue=queue_name, passive=True)
            logger.debug(f"队列 {queue_name} 已存在，跳过声明")
            return
        except ChannelClosedByBroker as e:
            if "NOT_FOUND" in str(e):
                logger.debug(f"队列 {queue_name} 不存在，需要创建")
                # 重新建立通道继续创建队列
                self._reset_sender_connection()
                if not self._ensure_sender_channel():
                    raise Exception("无法重新建立发送通道")
            else:
                # 其他错误，重新抛出
                raise
        except Exception as e:
            logger.debug(f"检查队列 {queue_name} 存在性时出错: {e}，继续尝试创建")
            # 重新建立通道继续创建队列
            self._reset_sender_connection()
            if not self._ensure_sender_channel():
                raise Exception("无法重新建立发送通道")

        # 定义不同的队列参数组合
        queue_configs = []

        if queue_name.startswith('queue_'):
            # 任务队列优先尝试auto_delete=True（原有行为）
            queue_configs = [
                {'durable': True, 'auto_delete': True}, # 原有参数
                {'durable': True, 'auto_delete': False}, # 备选参数
                {'durable': False, 'auto_delete': True}, # 更多备选
                {'durable': False, 'auto_delete': False} # 最基本参数
            ]
        else:
            # 系统队列（如fail_queue）优先尝试auto_delete=False
            queue_configs = [
                {'durable': True, 'auto_delete': False}, # 系统队列标准参数
                {'durable': True, 'auto_delete': True}, # 备选参数
                {'durable': False, 'auto_delete': False}, # 更多备选
                {'durable': False, 'auto_delete': True} # 最后备选
            ]

        # 尝试不同参数组合
        last_error = None
        for i, config in enumerate(queue_configs):
            try:
                result = self._sender_channel.queue_declare(queue=queue_name, **config)
                logger.debug(f"队列 {queue_name} 声明成功，参数: {config}")

                # 验证队列确实创建/存在
                if hasattr(result, 'method') and hasattr(result.method, 'queue'):
                    confirmed_queue = result.method.queue
                    if confirmed_queue == queue_name:
                        logger.debug(f"队列 {queue_name} 确认存在")
                        return
                else:
                    logger.debug(f"队列 {queue_name} 声明返回: {result}")
                    return # 声明成功

            except ChannelClosedByBroker as e:
                last_error = e
                if "PRECONDITION_FAILED" in str(e) and "inequivalent arg" in str(e):
                    logger.debug(f"队列 {queue_name} 参数不匹配: {config}, 尝试下一组参数")
                    # 重新建立通道继续尝试
                    self._reset_sender_connection()
                    if not self._ensure_sender_channel():
                        raise Exception("无法重新建立发送通道")
                    continue
                else:
                    # 其他类型的错误，如果不是最后一个配置就继续尝试
                    if i < len(queue_configs) - 1:
                        logger.debug(f"队列声明失败，尝试下一组参数: {e}")
                        self._reset_sender_connection()
                        if not self._ensure_sender_channel():
                            raise Exception("无法重新建立发送通道")
                        continue
                    else:
                        raise
            except Exception as e:
                last_error = e
                logger.warning(f"队列声明失败，参数 {config}: {e}")
                # 如果不是最后一个配置，重置通道继续尝试
                if i < len(queue_configs) - 1:
                    self._reset_sender_connection()
                    if not self._ensure_sender_channel():
                        raise Exception("无法重新建立发送通道")
                    continue
                else:
                    # 最后一个配置也失败了，抛出异常
                    break

        # 所有参数都失败了，抛出最后一个异常
        if last_error:
            raise Exception(f"无法用任何参数组合声明队列 {queue_name}: {last_error}")
        else:
            raise Exception(f"无法用任何参数组合声明队列: {queue_name}")

    def _ensure_publisher_channel(self) -> bool:
        """确保发布通道有效，如无效则重连"""
        try:
            if self.publisher_channel is None or not self.publisher_channel.is_open:
                if self.publisher_connection is None or not self.publisher_connection.is_open:
                    self._connect_publisher()
                else:
                    with self.publisher_lock:
                        self.publisher_channel = self.publisher_connection.channel()
                        self.publisher_channel.confirm_delivery()
                        self.publisher_channel.basic_qos(prefetch_count=1)
            return True
        except Exception as e:
            logger.error(f"Error ensuring publisher channel: {e}", exc_info=True)
            return False

    def _ensure_consumer_channel(self) -> bool:
        """确保消费通道有效，如无效则重连 - 防栈溢出版"""
        try:
            if self.consumer_channel is None or not self.consumer_channel.is_open:
                if self.consumer_connection is None or not self.consumer_connection.is_open:
                    # 重置连接状态，避免残留状态导致问题
                    self._reset_consumer_connection()
                    self._connect_consumer()
                else:
                    with self.consumer_lock:
                        self.consumer_channel = self.consumer_connection.channel()
                        self.consumer_channel.basic_qos(prefetch_count=1)
            return True
        except Exception as e:
            logger.error(f"Error ensuring consumer channel: {e}", exc_info=True)
            # 确保异常时重置状态
            self._reset_consumer_connection()
            return False

    def _reset_consumer_connection(self):
        """重置消费者连接状态"""
        try:
            if self.consumer_channel:
                try:
                    if self.consumer_channel.is_open:
                        self.consumer_channel.stop_consuming()
                        self.consumer_channel.close()
                except Exception:
                    pass
                self.consumer_channel = None

            if self.consumer_connection:
                try:
                    if self.consumer_connection.is_open:
                        self.consumer_connection.close()
                except Exception:
                    pass
                self.consumer_connection = None

            logger.debug("Consumer connection state reset")
        except Exception as e:
            logger.warning(f"Error resetting consumer connection: {e}")

    def enqueue_send_message(self, task_id: str, message: Dict[str, Any],
                           callback: Optional[Callable[[bool, str], None]] = None) -> bool:
        """
        线程安全的消息发送接口 - 推荐使用
        将发送请求加入队列，由专用发送线程处理

        Args:
            task_id: 任务ID，将转换为队列名 queue_{task_id}
            message: 要发送的消息内容
            callback: 可选的结果回调函数，接收(success: bool, error_msg: str)

        Returns:
            bool: True表示成功加入发送队列，False表示队列已满
        """
        queue_name = f'queue_{task_id}'

        send_task = {
            'queue_name': queue_name,
            'message': message,
            'callback': callback,
            'retry_count': 0,
            'max_retries': self.MAX_RETRIES
        }

        try:
            self._send_queue.put(send_task, block=False) # 非阻塞放入
            logger.debug(f"发送任务已加入队列: {queue_name}")
            return True
        except queue.Full:
            logger.error(f"发送队列已满，无法发送到: {queue_name}")
            if callback:
                callback(False, "发送队列已满")
            return False

    def enqueue_send_message_to_queue(self, queue_name: str, message: Dict[str, Any],
                                    callback: Optional[Callable[[bool, str], None]] = None) -> bool:
        """
        通用的队列发送接口 - 直接指定队列名

        Args:
            queue_name: 目标队列名
            message: 要发送的消息内容
            callback: 可选的结果回调函数

        Returns:
            bool: True表示成功加入发送队列，False表示队列已满
        """
        send_task = {
            'queue_name': queue_name,
            'message': message,
            'callback': callback,
            'retry_count': 0,
            'max_retries': self.MAX_RETRIES
        }

        try:
            self._send_queue.put(send_task, block=False)
            logger.debug(f"发送任务已加入队列: {queue_name}")
            return True
        except queue.Full:
            logger.error(f"发送队列已满，无法发送到: {queue_name}")
            if callback:
                callback(False, "发送队列已满")
            return False

    def send_message_to_queue(self, task_id: str, message: Dict[str, Any], retry_count: int = 0) -> bool:
        """
        兼容性方法 - 保持向后兼容，但建议使用enqueue_send_message
        直接使用publisher通道发送，可能在多线程下出现问题
        """
        logger.warning("使用了旧的send_message_to_queue方法，建议改用enqueue_send_message")

        if not self._ensure_publisher_channel():
            if retry_count < self.MAX_RETRIES:
                # 指数退避
                delay = min(self.RETRY_INTERVAL * (2 ** retry_count), 30)
                time.sleep(delay)
                return self.send_message_to_queue(task_id, message, retry_count + 1)
            logger.error("Failed to ensure publisher channel after retries.")
            return False

        queue_name = f'queue_{task_id}'
        try:
            self.publisher_channel.queue_declare(queue=queue_name, durable=True, auto_delete=True)
            with self.publisher_lock:
                self.publisher_channel.basic_publish(
                    exchange='',
                    routing_key=queue_name,
                    body=json.dumps(message),
                    properties=pika.BasicProperties(
                        delivery_mode=2,
                        content_type='application/json'
                    )
                )
            logger.debug(f"Message sent to queue {queue_name}")
            return True
        except (StreamLostError, ChannelWrongStateError) as e:
            logger.warning(f"Connection error sending to {queue_name} (attempt {retry_count + 1}): {e}")
            # 强制重置连接
            with self.publisher_lock:
                self.publisher_channel = None
                self.publisher_connection = None
            if retry_count < self.MAX_RETRIES:
                delay = min(self.RETRY_INTERVAL * (2 ** retry_count), 30)
                time.sleep(delay)
                return self.send_message_to_queue(task_id, message, retry_count + 1)
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending message to {queue_name}: {e}", exc_info=True)
            if retry_count < self.MAX_RETRIES:
                delay = min(self.RETRY_INTERVAL * (2 ** retry_count), 30)
                time.sleep(delay)
                return self.send_message_to_queue(task_id, message, retry_count + 1)
            return False

    def send_fail_message_to_queue(self, task_id_or_message, error_message: str = None, error_details: str = "", retry_count: int = 0) -> bool:
        """发送失败消息到 fail_queue - 使用线程安全的发送机制"""
        # 兼容性处理：支持旧的调用方式（传入字典）和新的调用方式（传入参数）
        if isinstance(task_id_or_message, dict):
            # 旧的调用方式：直接传入完整消息字典
            fail_message = task_id_or_message
            task_id = fail_message.get('taskId', 'unknown')
        else:
            # 新的调用方式：传入task_id和错误信息
            task_id = task_id_or_message
            fail_message = {
                "messageType": "notifyFailure",
                "taskId": task_id,
                "errorMessage": error_message,
                "errorDetails": error_details,
                "timestamp": time.time()
            }

        # 使用新的线程安全发送机制
        success_flag = threading.Event()
        result_data = {'success': False, 'error': None}

        def result_callback(success: bool, error_msg: str):
            """发送结果回调"""
            result_data['success'] = success
            result_data['error'] = error_msg
            success_flag.set()

        # 加入发送队列
        if self.enqueue_send_message_to_queue('fail_queue', fail_message, result_callback):
            # 等待发送完成，最多等待10秒
            if success_flag.wait(timeout=10.0):
                if result_data['success']:
                    logger.info(f"Fail message sent for task {task_id}")
                    return True
                else:
                    logger.error(f"Failed to send fail message for task {task_id}: {result_data['error']}")
                    return False
            else:
                logger.error(f"Timeout waiting for fail message send completion for task {task_id}")
                return False
        else:
            logger.error(f"Failed to enqueue fail message for task {task_id}")
            return False

    def start_consuming(self, queue_name: str):
        """使用消费专用通道开始消费指定队列 - 增强错误处理版"""
        retry_count = 0
        max_retries = 5

        while retry_count < max_retries:
            try:
                # 确保消费者通道可用
                if not self._ensure_consumer_channel():
                    logger.warning("Consumer channel failed, forcing reset...")
                    self._reset_consumer_connection()
                    time.sleep(min(2 ** retry_count, 10)) # 指数退避
                    retry_count += 1
                    continue

                # 声明队列并开始消费
                self.consumer_channel.queue_declare(queue=queue_name, durable=True)
                self.consumer_channel.basic_qos(prefetch_count=1)
                self.consumer_channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=self._on_message_callback,
                    auto_ack=True
                )
                logger.info(f"Started consuming from queue: {queue_name}")

                # 开始消费循环
                try:
                    self.consumer_channel.start_consuming()
                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt")
                    self.consumer_channel.stop_consuming()
                    break
                except (StreamLostError, ChannelWrongStateError) as stream_error:
                    logger.warning(f"Stream/Channel error (attempt {retry_count + 1}): {stream_error}")
                    # 强制重置连接和通道
                    self._force_reset_consumer()
                    retry_count += 1
                    if retry_count < max_retries:
                        delay = min(2 ** retry_count, 30)
                        logger.info(f"Retrying in {delay} seconds...")
                        time.sleep(delay)
                        continue
                    else:
                        logger.error("Max retries exceeded for consuming")
                        raise
                except Exception as consume_error:
                    logger.error(f"Unexpected error in start_consuming: {consume_error}", exc_info=True)
                    self._reset_consumer_connection()
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(min(2 ** retry_count, 10))
                        continue
                    else:
                        raise

            except Exception as e:
                logger.error(f"Error consuming from {queue_name} (attempt {retry_count + 1}): {e}", exc_info=True)
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(min(2 ** retry_count, 30))
                else:
                    logger.error("Failed to start consuming after all retries")
                    raise

    def _force_reset_consumer(self):
        """强制重置消费者连接，处理更严重的连接问题"""
        with self.consumer_lock:
            # 强制关闭所有资源
            if self.consumer_channel:
                try:
                    self.consumer_channel.stop_consuming()
                except:
                    pass
                try:
                    self.consumer_channel.close()
                except:
                    pass
                self.consumer_channel = None

            if self.consumer_connection:
                try:
                    self.consumer_connection.close()
                except:
                    pass
                self.consumer_connection = None

        logger.info("Consumer connection forcefully reset")

    def _on_message_callback(self, ch, method, properties, body):
        """消息回调函数 - 异步处理避免阻塞IOLoop"""
        def process_message():
            """在单独线程中处理消息，避免阻塞pika的IOLoop"""
            try:
                message = json.loads(body.decode('utf-8'))

                # 发布内部事件
                event_type = message.get('event_type', 'message_received')
                if event_type in self.subscribers:
                    for callback in self.subscribers[event_type]:
                        try:
                            callback(message)
                        except Exception as callback_error:
                            logger.error(f"Error in callback for {event_type}: {callback_error}", exc_info=True)

                logger.debug(f"Processed message: {event_type}")
            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)

        # 在单独的守护线程中处理消息，避免阻塞pika的IOLoop
        import threading
        thread = threading.Thread(target=process_message, daemon=True)
        thread.start()

    def close(self):
        """优雅关闭连接和发送线程"""
        logger.info("开始关闭EventManager...")

        try:
            # 1. 关闭发送线程
            if hasattr(self, '_sender_shutdown'):
                self._sender_shutdown.set()
                logger.info("发送关闭信号...")

                # 等待发送队列清空（最多等待5秒）
                if hasattr(self, '_send_queue'):
                    try:
                        # 等待队列中的任务完成
                        queue_size = self._send_queue.qsize()
                        if queue_size > 0:
                            logger.info(f"等待{queue_size}个发送任务完成...")
                            self._send_queue.join() # 等待所有任务完成
                    except Exception as e:
                        logger.warning(f"等待发送队列清空时出错: {e}")

                # 等待发送线程结束
                if hasattr(self, '_sender_thread') and self._sender_thread:
                    self._sender_thread.join(timeout=3.0)
                    if self._sender_thread.is_alive():
                        logger.warning("发送线程未在超时时间内结束")
                    else:
                        logger.info("发送线程已结束")

            # 2. 关闭发送连接
            if hasattr(self, '_sender_connection') and self._sender_connection:
                try:
                    if self._sender_channel and self._sender_channel.is_open:
                        self._sender_channel.close()
                    if self._sender_connection.is_open:
                        self._sender_connection.close()
                    logger.info("发送连接已关闭")
                except Exception as e:
                    logger.warning(f"关闭发送连接时出错: {e}")

            # 3. 关闭消费连接
            if self.consumer_channel and self.consumer_channel.is_open:
                self.consumer_channel.stop_consuming()
                self.consumer_channel.close()
            if self.consumer_connection and self.consumer_connection.is_open:
                self.consumer_connection.close()

            # 4. 关闭发布连接
            if self.publisher_channel and self.publisher_channel.is_open:
                self.publisher_channel.close()
            if self.publisher_connection and self.publisher_connection.is_open:
                self.publisher_connection.close()

            logger.info("所有RabbitMQ连接已关闭")

        except Exception as e:
            logger.error(f"关闭EventManager时出错: {e}", exc_info=True)

    # 事件发布/订阅机制
    def subscribe(self, event_type: str, callback: Callable):
        """订阅事件"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)
        logger.debug(f"Subscribed to event: {event_type}")

    def publish(self, event_type: str, data: Dict[str, Any]):
        """发布事件"""
        if event_type in self.subscribers:
            for callback in self.subscribers[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in event callback for {event_type}: {e}", exc_info=True)