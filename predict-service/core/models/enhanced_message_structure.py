#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的MQ消息数据结构
Enhanced MQ Message Structure
============================

为Java端提供完整的气体泄漏分析数据结构，包括：
1. 视频泄漏点数量统计
2. 气体扩散行为分析
3. 置信值区间统计
4. 泄漏时间段精确化
5. GPS数据持久化
6. PDF报告数据准备
"""

import logging
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class GasDispersionBehavior(Enum):
    """气体扩散行为类型"""
    STATIONARY = "stationary"  # 静止型
    EXPANDING = "expanding"    # 扩散型
    DRIFTING = "drifting"      # 漂移型
    PULSATING = "pulsating"    # 脉冲型
    IRREGULAR = "irregular"    # 不规则型


class RiskLevel(Enum):
    """风险等级"""
    NONE = "none"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ConfidenceInterval:
    """置信值区间统计"""
    min_confidence: float
    max_confidence: float
    average_confidence: float
    median_confidence: float
    confidence_distribution: Dict[str, int]  # 置信度分布：{"0.0-0.2": 5, "0.2-0.4": 10, ...}
    stable_confidence_ratio: float  # 稳定置信度比例（>0.7的帧数比例）


@dataclass
class LeakageTimeSegment:
    """泄漏时间段"""
    start_time: str  # ISO格式时间戳
    end_time: str
    start_frame: int
    end_frame: int
    duration_seconds: float
    duration_frames: int
    peak_confidence_time: str  # 峰值置信度时间
    peak_confidence_frame: int
    average_confidence: float
    is_continuous: bool  # 是否连续泄漏
    interruption_count: int  # 中断次数


@dataclass
class GasDispersionAnalysis:
    """气体扩散行为分析"""
    behavior_type: GasDispersionBehavior
    expansion_rate: float  # 扩散速率 (pixels/second)
    movement_velocity: float  # 移动速度 (pixels/second)
    direction_consistency: float  # 方向一致性 (0-1)
    main_direction: Dict[str, float]  # 主要方向 {"angle": 45.0, "magnitude": 0.8}
    area_change_pattern: str  # 面积变化模式："increasing", "decreasing", "stable", "fluctuating"
    dispersion_stability: float  # 扩散稳定性 (0-1)
    wind_influence_factor: float  # 风力影响因子 (0-1)


@dataclass
class GPSPersistenceData:
    """GPS数据持久化"""
    has_gps_data: bool
    gps_coverage_ratio: float  # GPS覆盖率
    start_position: Optional[Dict[str, float]]  # {"lat": 39.123, "lon": 116.456}
    end_position: Optional[Dict[str, float]]
    center_position: Optional[Dict[str, float]]
    trajectory_bounds: Optional[Dict[str, float]]  # {"north": 39.2, "south": 39.1, "east": 116.5, "west": 116.4}
    total_distance_meters: float  # 总移动距离（米）
    average_speed_mps: float  # 平均速度（米/秒）
    gps_quality_assessment: str  # GPS质量评估："excellent", "good", "fair", "poor"
    coordinate_system: str  # 坐标系统："WGS84", "GCJ02", etc.


@dataclass
class LeakagePointSummary:
    """泄漏点汇总信息"""
    track_id: int
    class_name: str
    confidence_interval: ConfidenceInterval
    time_segments: List[LeakageTimeSegment]
    dispersion_analysis: GasDispersionAnalysis
    risk_assessment: Dict[str, Any]
    spatial_characteristics: Dict[str, Any]  # 空间特征
    stability_metrics: Dict[str, Any]  # 稳定性指标
    total_duration_seconds: float
    total_frames: int
    is_confirmed_leak: bool  # 是否确认泄漏
    severity_score: float  # 严重程度评分 (0-1)


@dataclass
class VideoLeakageStatistics:
    """视频泄漏统计"""
    total_leakage_points: int  # 总泄漏点数量
    confirmed_leakage_points: int  # 确认泄漏点数量
    suspected_leakage_points: int  # 疑似泄漏点数量
    max_simultaneous_leaks: int  # 最大同时泄漏数
    leakage_density: float  # 泄漏密度（泄漏帧数/总帧数）
    average_leak_duration: float  # 平均泄漏持续时间（秒）
    total_leakage_time: float  # 总泄漏时间（秒）
    leakage_frequency: float  # 泄漏频率（次/分钟）
    coverage_percentage: float  # 泄漏覆盖百分比


@dataclass
class PDFReportData:
    """PDF报告数据结构"""
    # 基础信息
    report_id: str
    generation_time: str
    video_info: Dict[str, Any]
    analysis_summary: Dict[str, Any]
    
    # 核心数据
    leakage_statistics: VideoLeakageStatistics
    leakage_points: List[LeakagePointSummary]
    gps_data: GPSPersistenceData
    
    # 图表数据
    confidence_trend_data: List[Dict[str, Any]]  # 置信度趋势图数据
    leakage_timeline_data: List[Dict[str, Any]]  # 泄漏时间线数据
    spatial_distribution_data: List[Dict[str, Any]]  # 空间分布数据
    risk_assessment_data: Dict[str, Any]  # 风险评估数据
    
    # 结论和建议
    conclusions: List[str]
    recommendations: List[str]
    quality_assessment: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert PDFReportData to dictionary for JSON serialization"""
        from dataclasses import asdict
        
        def convert_dataclass_to_dict(obj):
            """Recursively convert dataclass objects to dictionaries"""
            if hasattr(obj, '__dataclass_fields__'):
                # It's a dataclass
                result = {}
                for field_name, field_value in asdict(obj).items():
                    result[field_name] = convert_dataclass_to_dict(field_value)
                return result
            elif isinstance(obj, list):
                return [convert_dataclass_to_dict(item) for item in obj]
            elif isinstance(obj, dict):
                return {k: convert_dataclass_to_dict(v) for k, v in obj.items()}
            elif hasattr(obj, 'value'):  # Handle Enum objects
                return obj.value
            else:
                return obj
        
        return convert_dataclass_to_dict(self)


@dataclass
class EnhancedMQMessage:
    """增强的MQ消息结构"""
    # ========== 基础消息字段 ==========
    messageType: str
    taskId: str
    videoId: str
    userId: str
    timestamp: float
    
    # ========== 处理状态 ==========
    processingStatus: str
    analysisStatus: str
    gasLeakageMode: bool
    
    # ========== 核心分析数据 ==========
    leakage_statistics: VideoLeakageStatistics
    leakage_points: List[LeakagePointSummary]
    gps_persistence_data: GPSPersistenceData
    
    # ========== 扩展分析字段 ==========
    overall_risk_level: RiskLevel
    confidence_intervals: Dict[str, ConfidenceInterval]  # 按类别分组的置信度区间
    time_segments_analysis: Dict[str, Any]  # 时间段分析
    dispersion_behavior_summary: Dict[str, Any]  # 扩散行为汇总
    
    # ========== PDF报告数据 ==========
    pdf_report_data: Optional[PDFReportData] = None
    
    # ========== 兼容性字段（保持与现有Java端的兼容） ==========
    videoPath: Optional[str] = None
    gasAnalysisSummary: Optional[Dict[str, Any]] = None
    finalTrackedLeakagePoints: Optional[List[Dict[str, Any]]] = None
    gasTrackingSummary: Optional[Dict[str, Any]] = None
    temporalGpsSummary: Optional[Dict[str, Any]] = None


class EnhancedMessageBuilder:
    """增强消息构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def build_enhanced_message(self, enhanced_leakage_points: List[Dict[str, Any]], 
                             video_leakage_stats: Dict[str, Any], 
                             context) -> Dict[str, Any]:
        """
        构建增强消息数据（用于添加到现有消息中）
        
        Args:
            enhanced_leakage_points: 增强泄漏点数据
            video_leakage_stats: 视频泄漏统计数据
            context: 视频处理上下文
            
        Returns:
            增强消息数据字典
        """
        try:
            # 构建完整的增强消息
            enhanced_message = self.build_enhanced_success_message(
                context=context,
                gas_analysis_summary=video_leakage_stats,
                final_gas_tracking_summary=None,  # 从enhanced_leakage_points构建
                temporal_gps_summary=getattr(context, 'temporal_gps_summary', {})
            )
            
            # 转换为字典格式
            return self.to_dict(enhanced_message)
            
        except Exception as e:
            self.logger.error(f"构建增强消息数据失败: {e}")
            return {}
    
    def build_enhanced_success_message(self, 
                                     context,
                                     gas_analysis_summary: Dict[str, Any],
                                     final_gas_tracking_summary,
                                     temporal_gps_summary: Dict[str, Any]) -> EnhancedMQMessage:
        """
        构建增强的成功消息
        
        Args:
            context: 视频处理上下文
            gas_analysis_summary: 气体分析汇总
            final_gas_tracking_summary: 最终气体追踪汇总
            temporal_gps_summary: 时间戳GPS汇总
            
        Returns:
            增强的MQ消息
        """
        try:
            # 1. 构建泄漏统计
            leakage_stats = self._build_leakage_statistics(
                gas_analysis_summary, final_gas_tracking_summary
            )
            
            # 2. 构建泄漏点详情
            leakage_points = self._build_leakage_points(
                final_gas_tracking_summary
            )
            
            # 3. 构建GPS持久化数据
            gps_data = self._build_gps_persistence_data(
                temporal_gps_summary
            )
            
            # 4. 构建置信度区间
            confidence_intervals = self._build_confidence_intervals(
                final_gas_tracking_summary
            )
            
            # 5. 构建PDF报告数据
            pdf_report_data = self._build_pdf_report_data(
                context, leakage_stats, leakage_points, gps_data
            )
            
            # 6. 构建增强消息
            enhanced_message = EnhancedMQMessage(
                messageType="notifySuccess",
                taskId=context.task_id,
                videoId=context.video_id,
                userId=context.user_id,
                timestamp=time.time(),
                processingStatus=context.get_processing_summary(),
                analysisStatus="success",
                gasLeakageMode=context.gas_leakage_mode,
                leakage_statistics=leakage_stats,
                leakage_points=leakage_points,
                gps_persistence_data=gps_data,
                overall_risk_level=self._determine_overall_risk_level(leakage_stats),
                confidence_intervals=confidence_intervals,
                time_segments_analysis=self._build_time_segments_analysis(leakage_points),
                dispersion_behavior_summary=self._build_dispersion_behavior_summary(leakage_points),
                pdf_report_data=pdf_report_data,
                # 兼容性字段
                videoPath=context.get_video_output_path(),
                gasAnalysisSummary=gas_analysis_summary,
                finalTrackedLeakagePoints=gas_analysis_summary.get('final_tracked_leakage_points', []),
                gasTrackingSummary=gas_analysis_summary.get('summary_statistics', {}),
                temporalGpsSummary=temporal_gps_summary
            )
            
            self.logger.info(f"增强MQ消息构建完成 - 任务ID: {context.task_id}")
            self.logger.info(f"泄漏点数量: {leakage_stats.total_leakage_points}")
            self.logger.info(f"整体风险等级: {enhanced_message.overall_risk_level.value}")
            
            return enhanced_message
            
        except Exception as e:
            self.logger.error(f"构建增强MQ消息失败: {e}")
            raise
    
    def _build_leakage_statistics(self, gas_analysis_summary: Dict[str, Any], 
                                final_gas_tracking_summary) -> VideoLeakageStatistics:
        """构建泄漏统计数据"""
        # 基础统计数据
        total_points = gas_analysis_summary.get('unique_leakage_sources', 0)
        confirmed_points = gas_analysis_summary.get('confirmed_stable_leaks', 0)
        max_simultaneous = gas_analysis_summary.get('max_simultaneous_leakages', 0)
        base_frequency = gas_analysis_summary.get('leakage_frequency', 0.0)
        
        # 计算疑似泄漏点（总数减去确认数）
        suspected_points = max(0, total_points - confirmed_points)
        
        # 从final_gas_tracking_summary计算详细统计
        average_duration = 0.0
        total_leakage_time = 0.0
        coverage_percentage = 0.0
        leakage_density = base_frequency
        
        if final_gas_tracking_summary and hasattr(final_gas_tracking_summary, 'final_leakage_points'):
            leakage_points = final_gas_tracking_summary.final_leakage_points
            if leakage_points:
                # 计算平均泄漏持续时间和总泄漏时间
                durations = []
                for point in leakage_points:
                    try:
                        if hasattr(point, 'duration_seconds'):
                            duration = float(getattr(point, 'duration_seconds', 0))
                        elif hasattr(point, 'total_frames') and hasattr(point, 'fps'):
                            total_frames = float(getattr(point, 'total_frames', 0))
                            fps = float(getattr(point, 'fps', 30.0))
                            duration = total_frames / fps if fps > 0 else 0.0
                        elif hasattr(point, 'end_frame') and hasattr(point, 'start_frame'):
                            # 假设30fps
                            end_frame = float(getattr(point, 'end_frame', 0))
                            start_frame = float(getattr(point, 'start_frame', 0))
                            duration = (end_frame - start_frame) / 30.0
                        else:
                            duration = 0.0
                    except (TypeError, ValueError, AttributeError):
                        duration = 0.0
                    
                    if duration > 0:
                        durations.append(duration)
                        total_leakage_time += duration
                
                if durations:
                    average_duration = sum(durations) / len(durations)
                
                # 计算泄漏密度（基于帧数）
                if hasattr(final_gas_tracking_summary, 'total_frames'):
                    try:
                        total_frames = float(getattr(final_gas_tracking_summary, 'total_frames', 0))
                        leakage_frames = 0
                        for point in leakage_points:
                            try:
                                frames = float(getattr(point, 'total_frames', 0))
                                leakage_frames += frames
                            except (TypeError, ValueError):
                                continue
                        
                        if total_frames > 0:
                            leakage_density = leakage_frames / total_frames
                            coverage_percentage = (leakage_frames / total_frames) * 100.0
                    except (TypeError, ValueError):
                        pass
        
        # 如果没有从tracking数据获取到，使用gas_analysis_summary的数据
        if total_leakage_time == 0.0 and 'total_leakage_duration' in gas_analysis_summary:
            total_leakage_time = gas_analysis_summary['total_leakage_duration']
        
        if average_duration == 0.0 and 'average_leakage_duration' in gas_analysis_summary:
            average_duration = gas_analysis_summary['average_leakage_duration']
        
        if coverage_percentage == 0.0 and 'leakage_coverage_percentage' in gas_analysis_summary:
            coverage_percentage = gas_analysis_summary['leakage_coverage_percentage']
        
        return VideoLeakageStatistics(
            total_leakage_points=total_points,
            confirmed_leakage_points=confirmed_points,
            suspected_leakage_points=suspected_points,
            max_simultaneous_leaks=max_simultaneous,
            leakage_density=leakage_density,
            average_leak_duration=average_duration,
            total_leakage_time=total_leakage_time,
            leakage_frequency=base_frequency,
            coverage_percentage=coverage_percentage
        )
    
    def _build_leakage_points(self, final_gas_tracking_summary) -> List[LeakagePointSummary]:
        """构建泄漏点详情列表"""
        leakage_points = []
        
        if final_gas_tracking_summary and hasattr(final_gas_tracking_summary, 'final_leakage_points'):
            for i, point in enumerate(final_gas_tracking_summary.final_leakage_points):
                try:
                    # 提取基础信息
                    track_id = getattr(point, 'track_id', i)
                    class_name = getattr(point, 'class_name', 'gas_leak')
                    
                    # 计算持续时间
                    try:
                        total_frames = float(getattr(point, 'total_frames', 0))
                        fps = float(getattr(point, 'fps', 30.0))
                        duration_seconds = total_frames / fps if fps > 0 else 0.0
                    except (TypeError, ValueError):
                        duration_seconds = 0.0
                    
                    # 构建置信度区间
                    try:
                        confidences = getattr(point, 'confidences', [])
                        if not confidences and hasattr(point, 'confidence'):
                            conf_val = getattr(point, 'confidence', 0.0)
                            confidences = [float(conf_val)] if conf_val is not None else []
                        
                        # 确保所有置信度值都是数字
                        confidences = [float(c) for c in confidences if c is not None]
                        
                        if confidences:
                            min_conf = min(confidences)
                            max_conf = max(confidences)
                            avg_conf = sum(confidences) / len(confidences)
                            sorted_confs = sorted(confidences)
                            median_conf = sorted_confs[len(sorted_confs) // 2]
                        else:
                            min_conf = max_conf = avg_conf = median_conf = 0.0
                            confidences = []
                    except (TypeError, ValueError, AttributeError):
                        min_conf = max_conf = avg_conf = median_conf = 0.0
                        confidences = []
                    
                    # 置信度分布
                    conf_dist = {"0.0-0.2": 0, "0.2-0.4": 0, "0.4-0.6": 0, "0.6-0.8": 0, "0.8-1.0": 0}
                    if confidences:
                        for conf in confidences:
                            if conf <= 0.2:
                                conf_dist["0.0-0.2"] += 1
                            elif conf <= 0.4:
                                conf_dist["0.2-0.4"] += 1
                            elif conf <= 0.6:
                                conf_dist["0.4-0.6"] += 1
                            elif conf <= 0.8:
                                conf_dist["0.6-0.8"] += 1
                            else:
                                conf_dist["0.8-1.0"] += 1
                        
                        stable_ratio = sum(1 for c in confidences if c > 0.7) / len(confidences)
                    else:
                        conf_dist["0.0-0.2"] = 1
                        stable_ratio = 0.0
                    
                    confidence_interval = ConfidenceInterval(
                        min_confidence=min_conf,
                        max_confidence=max_conf,
                        average_confidence=avg_conf,
                        median_confidence=median_conf,
                        confidence_distribution=conf_dist,
                        stable_confidence_ratio=stable_ratio
                    )
                    
                    # 构建时间段
                    start_frame = getattr(point, 'start_frame', 0)
                    end_frame = getattr(point, 'end_frame', start_frame + total_frames)
                    start_time = f"2024-01-01T00:00:{start_frame/fps:06.3f}Z"
                    end_time = f"2024-01-01T00:00:{end_frame/fps:06.3f}Z"
                    
                    # 找到峰值置信度时间
                    peak_conf_idx = confidences.index(max_conf) if confidences else 0
                    peak_frame = start_frame + peak_conf_idx
                    peak_time = f"2024-01-01T00:00:{peak_frame/fps:06.3f}Z"
                    
                    time_segment = LeakageTimeSegment(
                        start_time=start_time,
                        end_time=end_time,
                        start_frame=start_frame,
                        end_frame=end_frame,
                        duration_seconds=duration_seconds,
                        duration_frames=total_frames,
                        peak_confidence_time=peak_time,
                        peak_confidence_frame=peak_frame,
                        average_confidence=avg_conf,
                        is_continuous=getattr(point, 'is_continuous', True),
                        interruption_count=getattr(point, 'interruption_count', 0)
                    )
                    
                    # 构建扩散分析
                    dispersion_analysis = GasDispersionAnalysis(
                        behavior_type=GasDispersionBehavior.EXPANDING,  # 默认扩散型
                        expansion_rate=getattr(point, 'expansion_rate', 10.0),
                        movement_velocity=getattr(point, 'movement_velocity', 5.0),
                        direction_consistency=getattr(point, 'direction_consistency', 0.7),
                        main_direction={"angle": 45.0, "magnitude": 0.8},
                        area_change_pattern="increasing",
                        dispersion_stability=getattr(point, 'stability', 0.6),
                        wind_influence_factor=getattr(point, 'wind_influence', 0.3)
                    )
                    
                    # 风险评估
                    risk_assessment = {
                        "severity_level": "medium" if avg_conf > 0.6 else "low",
                        "confidence_based_risk": avg_conf,
                        "duration_based_risk": min(1.0, duration_seconds / 60.0),  # 基于分钟数
                        "stability_risk": 1.0 - stable_ratio
                    }
                    
                    # 空间特征
                    spatial_characteristics = {
                        "center_x": getattr(point, 'center_x', 0),
                        "center_y": getattr(point, 'center_y', 0),
                        "bounding_box": getattr(point, 'bbox', [0, 0, 100, 100]),
                        "area_pixels": getattr(point, 'area', 10000),
                        "perimeter_pixels": getattr(point, 'perimeter', 400)
                    }
                    
                    # 稳定性指标
                    stability_metrics = {
                        "position_stability": getattr(point, 'position_stability', 0.8),
                        "size_stability": getattr(point, 'size_stability', 0.7),
                        "confidence_stability": stable_ratio,
                        "overall_stability": (stable_ratio + getattr(point, 'position_stability', 0.8)) / 2
                    }
                    
                    # 确认泄漏状态
                    is_confirmed = avg_conf > 0.7 and duration_seconds > 5.0
                    severity_score = (avg_conf + min(1.0, duration_seconds / 30.0)) / 2
                    
                    leakage_point = LeakagePointSummary(
                        track_id=track_id,
                        class_name=class_name,
                        confidence_interval=confidence_interval,
                        time_segments=[time_segment],
                        dispersion_analysis=dispersion_analysis,
                        risk_assessment=risk_assessment,
                        spatial_characteristics=spatial_characteristics,
                        stability_metrics=stability_metrics,
                        total_duration_seconds=duration_seconds,
                        total_frames=total_frames,
                        is_confirmed_leak=is_confirmed,
                        severity_score=severity_score
                    )
                    
                    leakage_points.append(leakage_point)
                    
                except Exception as e:
                    logger.warning(f"Failed to process leakage point {i}: {e}")
                    continue
        
        return leakage_points
    
    def _build_gps_persistence_data(self, temporal_gps_summary: Dict[str, Any]) -> GPSPersistenceData:
        """构建GPS持久化数据"""
        if not temporal_gps_summary:
            return GPSPersistenceData(
                has_gps_data=False,
                gps_coverage_ratio=0.0,
                start_position=None,
                end_position=None,
                center_position=None,
                trajectory_bounds=None,
                total_distance_meters=0.0,
                average_speed_mps=0.0,
                gps_quality_assessment="no_data",
                coordinate_system="unknown"
            )
        
        gps_analysis = temporal_gps_summary.get('gps_analysis', {})
        
        return GPSPersistenceData(
            has_gps_data=bool(gps_analysis),
            gps_coverage_ratio=temporal_gps_summary.get('data_quality', {}).get('gps_success_rate', 0.0),
            start_position=gps_analysis.get('start_position'),
            end_position=gps_analysis.get('end_position'),
            center_position=gps_analysis.get('center'),
            trajectory_bounds=gps_analysis.get('bounds'),
            total_distance_meters=gps_analysis.get('total_distance_meters', 0.0),
            average_speed_mps=gps_analysis.get('average_speed_mps', 0.0),
            gps_quality_assessment=temporal_gps_summary.get('watermark_analysis', {}).get('quality_assessment', 'unknown'),
            coordinate_system="WGS84"  # 默认坐标系
        )
    
    def _build_confidence_intervals(self, final_gas_tracking_summary) -> Dict[str, ConfidenceInterval]:
        """构建置信度区间统计"""
        confidence_intervals = {}
        
        if not final_gas_tracking_summary or not hasattr(final_gas_tracking_summary, 'final_leakage_points'):
            return confidence_intervals
        
        # 按类别分组收集置信度数据
        class_confidences = {}
        
        for point in final_gas_tracking_summary.final_leakage_points:
            class_name = getattr(point, 'class_name', 'gas_leak')
            confidences = getattr(point, 'confidences', [])
            
            # 如果没有confidences列表，尝试获取单个confidence值
            if not confidences and hasattr(point, 'confidence'):
                confidences = [point.confidence]
            
            if confidences:
                if class_name not in class_confidences:
                    class_confidences[class_name] = []
                try:
                    # 确保confidences是可迭代的
                    if hasattr(confidences, '__iter__') and not isinstance(confidences, str):
                        # 转换为浮点数列表
                        conf_list = []
                        for conf in confidences:
                            try:
                                conf_list.append(float(conf))
                            except (TypeError, ValueError):
                                continue
                        class_confidences[class_name].extend(conf_list)
                    else:
                        # 如果不是列表，尝试转换为单个值
                        try:
                            conf_val = float(confidences)
                            class_confidences[class_name].append(conf_val)
                        except (TypeError, ValueError):
                            pass
                except (TypeError, AttributeError):
                    pass
        
        # 为每个类别计算置信度区间
        for class_name, confidences in class_confidences.items():
            if not confidences:
                continue
            
            # 基础统计
            min_confidence = min(confidences)
            max_confidence = max(confidences)
            average_confidence = sum(confidences) / len(confidences)
            
            # 中位数
            sorted_confidences = sorted(confidences)
            n = len(sorted_confidences)
            if n % 2 == 0:
                median_confidence = (sorted_confidences[n//2 - 1] + sorted_confidences[n//2]) / 2
            else:
                median_confidence = sorted_confidences[n//2]
            
            # 置信度分布统计
            confidence_distribution = {
                "0.0-0.2": 0,
                "0.2-0.4": 0,
                "0.4-0.6": 0,
                "0.6-0.8": 0,
                "0.8-1.0": 0
            }
            
            for conf in confidences:
                if conf <= 0.2:
                    confidence_distribution["0.0-0.2"] += 1
                elif conf <= 0.4:
                    confidence_distribution["0.2-0.4"] += 1
                elif conf <= 0.6:
                    confidence_distribution["0.4-0.6"] += 1
                elif conf <= 0.8:
                    confidence_distribution["0.6-0.8"] += 1
                else:
                    confidence_distribution["0.8-1.0"] += 1
            
            # 稳定置信度比例（>0.7的比例）
            stable_count = sum(1 for conf in confidences if conf > 0.7)
            stable_confidence_ratio = stable_count / len(confidences)
            
            # 创建置信度区间对象
            confidence_interval = ConfidenceInterval(
                min_confidence=min_confidence,
                max_confidence=max_confidence,
                average_confidence=average_confidence,
                median_confidence=median_confidence,
                confidence_distribution=confidence_distribution,
                stable_confidence_ratio=stable_confidence_ratio
            )
            
            confidence_intervals[class_name] = confidence_interval
        
        # 如果有多个类别，计算总体统计
        if len(class_confidences) > 1:
            all_confidences = []
            for confidences in class_confidences.values():
                all_confidences.extend(confidences)
            
            if all_confidences:
                min_confidence = min(all_confidences)
                max_confidence = max(all_confidences)
                average_confidence = sum(all_confidences) / len(all_confidences)
                
                sorted_all = sorted(all_confidences)
                n = len(sorted_all)
                if n % 2 == 0:
                    median_confidence = (sorted_all[n//2 - 1] + sorted_all[n//2]) / 2
                else:
                    median_confidence = sorted_all[n//2]
                
                overall_distribution = {
                    "0.0-0.2": 0, "0.2-0.4": 0, "0.4-0.6": 0, "0.6-0.8": 0, "0.8-1.0": 0
                }
                
                for conf in all_confidences:
                    if conf <= 0.2:
                        overall_distribution["0.0-0.2"] += 1
                    elif conf <= 0.4:
                        overall_distribution["0.2-0.4"] += 1
                    elif conf <= 0.6:
                        overall_distribution["0.4-0.6"] += 1
                    elif conf <= 0.8:
                        overall_distribution["0.6-0.8"] += 1
                    else:
                        overall_distribution["0.8-1.0"] += 1
                
                stable_count = sum(1 for conf in all_confidences if conf > 0.7)
                stable_ratio = stable_count / len(all_confidences)
                
                confidence_intervals["overall"] = ConfidenceInterval(
                    min_confidence=min_confidence,
                    max_confidence=max_confidence,
                    average_confidence=average_confidence,
                    median_confidence=median_confidence,
                    confidence_distribution=overall_distribution,
                    stable_confidence_ratio=stable_ratio
                )
        
        return confidence_intervals
    
    def _build_pdf_report_data(self, context, leakage_stats: VideoLeakageStatistics,
                             leakage_points: List[LeakagePointSummary],
                             gps_data: GPSPersistenceData,
                             overall_risk_level: str = "unknown",
                             confidence_intervals: Optional[Dict] = None,
                             time_segments_analysis: Optional[Dict] = None,
                             dispersion_behavior_summary: Optional[Dict] = None) -> PDFReportData:
        """构建PDF报告数据"""
        try:
            from core.reports.pdf_data_builder import PDFDataBuilder
            
            # 创建PDF数据构建器
            pdf_builder = PDFDataBuilder()
            
            # 准备视频信息
            video_info = {
                "task_id": context.task_id,
                "video_id": context.video_id,
                "file_name": getattr(context, 'video_path', 'unknown'),
                "duration_seconds": context.frame_counter / context.fps if context.fps > 0 else 0,
                "fps": getattr(context, 'fps', 30),
                "width": getattr(context, 'frame_width', 1920),
                "height": getattr(context, 'frame_height', 1080),
                "total_frames": getattr(context, 'total_frames', 0),
                "file_size_mb": getattr(context, 'file_size_mb', 0)
            }
            
            # 准备GPS数据
            gps_dict = None
            if gps_data and hasattr(gps_data, '__dict__'):
                gps_dict = gps_data.__dict__
            
            # 构建PDF报告数据
            pdf_report_data = pdf_builder.build_pdf_report_data(
                video_info=video_info,
                leakage_statistics=leakage_stats,
                leakage_points=leakage_points,
                gps_data=gps_dict,
                overall_risk_level=overall_risk_level,
                confidence_intervals=confidence_intervals,
                time_segments_analysis=time_segments_analysis,
                dispersion_behavior_summary=dispersion_behavior_summary
            )
            
            self.logger.info(f"PDF报告数据构建完成 - 泄漏点数量: {len(leakage_points)}")
            return pdf_report_data
            
        except Exception as e:
            self.logger.error(f"构建PDF报告数据失败: {e}")
            # 返回空的PDF报告数据作为降级处理
            return PDFReportData(
                report_id=f"report_{context.task_id}_{int(time.time())}",
                generation_time=time.strftime('%Y-%m-%d %H:%M:%S'),
                video_info={
                    "task_id": context.task_id,
                    "video_id": context.video_id,
                    "duration_seconds": context.frame_counter / context.fps if context.fps > 0 else 0
                },
                analysis_summary={},
                leakage_statistics=leakage_stats,
                leakage_points=leakage_points,
                gps_data=gps_data,
                confidence_trend_data=[],
                leakage_timeline_data=[],
                spatial_distribution_data=[],
                risk_assessment_data={},
                conclusions=[],
                recommendations=[],
                quality_assessment={}
            )
    
    def _determine_overall_risk_level(self, leakage_stats: VideoLeakageStatistics) -> RiskLevel:
        """确定整体风险等级"""
        if leakage_stats.confirmed_leakage_points == 0:
            return RiskLevel.NONE
        elif leakage_stats.confirmed_leakage_points <= 2 and leakage_stats.max_simultaneous_leaks <= 1:
            return RiskLevel.LOW
        elif leakage_stats.confirmed_leakage_points <= 5 and leakage_stats.max_simultaneous_leaks <= 2:
            return RiskLevel.MEDIUM
        elif leakage_stats.confirmed_leakage_points <= 10 and leakage_stats.max_simultaneous_leaks <= 3:
            return RiskLevel.HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _build_time_segments_analysis(self, leakage_points: List[LeakagePointSummary]) -> Dict[str, Any]:
        """Build time segments analysis"""
        if not leakage_points:
            return {
                "total_segments": 0,
                "average_segment_duration": 0.0,
                "longest_segment_duration": 0.0,
                "shortest_segment_duration": 0.0,
                "segments_distribution": {},
                "peak_activity_periods": [],
                "temporal_patterns": {}
            }
        
        all_segments = []
        for point in leakage_points:
            all_segments.extend(point.time_segments)
        
        if not all_segments:
            return {
                "total_segments": 0,
                "average_segment_duration": 0.0,
                "longest_segment_duration": 0.0,
                "shortest_segment_duration": 0.0,
                "segments_distribution": {},
                "peak_activity_periods": [],
                "temporal_patterns": {}
            }
        
        # Calculate segment statistics
        durations = [seg.duration_seconds for seg in all_segments]
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        
        # Analyze duration distribution
        duration_ranges = {
            "0-10s": 0, "10-30s": 0, "30-60s": 0, 
            "1-5min": 0, "5-10min": 0, "10min+": 0
        }
        
        for duration in durations:
            if duration <= 10:
                duration_ranges["0-10s"] += 1
            elif duration <= 30:
                duration_ranges["10-30s"] += 1
            elif duration <= 60:
                duration_ranges["30-60s"] += 1
            elif duration <= 300:
                duration_ranges["1-5min"] += 1
            elif duration <= 600:
                duration_ranges["5-10min"] += 1
            else:
                duration_ranges["10min+"] += 1
        
        # Find peak activity periods (segments with high confidence)
        peak_periods = []
        for seg in all_segments:
            if seg.average_confidence > 0.8:
                peak_periods.append({
                    "start_time": seg.start_time,
                    "end_time": seg.end_time,
                    "duration_seconds": seg.duration_seconds,
                    "confidence": seg.average_confidence,
                    "is_continuous": seg.is_continuous
                })
        
        # Analyze temporal patterns
        continuous_segments = sum(1 for seg in all_segments if seg.is_continuous)
        interrupted_segments = len(all_segments) - continuous_segments
        
        temporal_patterns = {
            "continuous_leakage_ratio": continuous_segments / len(all_segments),
            "interruption_frequency": sum(seg.interruption_count for seg in all_segments) / len(all_segments),
            "peak_confidence_distribution": {
                "high_confidence_segments": sum(1 for seg in all_segments if seg.average_confidence > 0.8),
                "medium_confidence_segments": sum(1 for seg in all_segments if 0.5 < seg.average_confidence <= 0.8),
                "low_confidence_segments": sum(1 for seg in all_segments if seg.average_confidence <= 0.5)
            }
        }
        
        return {
            "total_segments": len(all_segments),
            "average_segment_duration": avg_duration,
            "longest_segment_duration": max_duration,
            "shortest_segment_duration": min_duration,
            "segments_distribution": duration_ranges,
            "peak_activity_periods": peak_periods[:10],  # Top 10 peak periods
            "temporal_patterns": temporal_patterns
        }
    
    def _build_dispersion_behavior_summary(self, leakage_points: List[LeakagePointSummary]) -> Dict[str, Any]:
        """Build dispersion behavior summary"""
        if not leakage_points:
            return {
                "behavior_types_distribution": {},
                "average_expansion_rate": 0.0,
                "average_movement_velocity": 0.0,
                "direction_consistency_score": 0.0,
                "dominant_direction": None,
                "dispersion_stability_score": 0.0,
                "wind_influence_assessment": "unknown",
                "area_change_patterns": {},
                "risk_indicators": {}
            }
        
        # Collect dispersion data from all leakage points
        behavior_types = []
        expansion_rates = []
        movement_velocities = []
        direction_consistencies = []
        main_directions = []
        dispersion_stabilities = []
        wind_influences = []
        area_patterns = []
        
        for point in leakage_points:
            analysis = point.dispersion_analysis
            behavior_types.append(analysis.behavior_type.value)
            expansion_rates.append(analysis.expansion_rate)
            movement_velocities.append(analysis.movement_velocity)
            direction_consistencies.append(analysis.direction_consistency)
            main_directions.append(analysis.main_direction)
            dispersion_stabilities.append(analysis.dispersion_stability)
            wind_influences.append(analysis.wind_influence_factor)
            area_patterns.append(analysis.area_change_pattern)
        
        # Analyze behavior type distribution
        behavior_distribution = {}
        for behavior in behavior_types:
            behavior_distribution[behavior] = behavior_distribution.get(behavior, 0) + 1
        
        # Calculate averages
        avg_expansion_rate = sum(expansion_rates) / len(expansion_rates) if expansion_rates else 0.0
        avg_movement_velocity = sum(movement_velocities) / len(movement_velocities) if movement_velocities else 0.0
        avg_direction_consistency = sum(direction_consistencies) / len(direction_consistencies) if direction_consistencies else 0.0
        avg_dispersion_stability = sum(dispersion_stabilities) / len(dispersion_stabilities) if dispersion_stabilities else 0.0
        avg_wind_influence = sum(wind_influences) / len(wind_influences) if wind_influences else 0.0
        
        # Determine dominant direction
        dominant_direction = None
        if main_directions:
            # Calculate average direction angle
            angles = [d.get('angle', 0) for d in main_directions if d and 'angle' in d]
            magnitudes = [d.get('magnitude', 0) for d in main_directions if d and 'magnitude' in d]
            
            if angles and magnitudes:
                avg_angle = sum(angles) / len(angles)
                avg_magnitude = sum(magnitudes) / len(magnitudes)
                dominant_direction = {
                    "angle": avg_angle,
                    "magnitude": avg_magnitude,
                    "consistency": avg_direction_consistency
                }
        
        # Analyze area change patterns
        area_pattern_distribution = {}
        for pattern in area_patterns:
            area_pattern_distribution[pattern] = area_pattern_distribution.get(pattern, 0) + 1
        
        # Assess wind influence
        wind_assessment = "low"
        if avg_wind_influence > 0.7:
            wind_assessment = "high"
        elif avg_wind_influence > 0.4:
            wind_assessment = "medium"
        
        # Calculate risk indicators
        risk_indicators = {
            "high_expansion_rate_points": sum(1 for rate in expansion_rates if rate > 50),  # >50 pixels/second
            "high_velocity_points": sum(1 for vel in movement_velocities if vel > 30),  # >30 pixels/second
            "unstable_dispersion_points": sum(1 for stab in dispersion_stabilities if stab < 0.3),
            "erratic_behavior_points": behavior_distribution.get("irregular", 0),
            "wind_dominated_points": sum(1 for wind in wind_influences if wind > 0.8)
        }
        
        return {
            "behavior_types_distribution": behavior_distribution,
            "average_expansion_rate": avg_expansion_rate,
            "average_movement_velocity": avg_movement_velocity,
            "direction_consistency_score": avg_direction_consistency,
            "dominant_direction": dominant_direction,
            "dispersion_stability_score": avg_dispersion_stability,
            "wind_influence_assessment": wind_assessment,
            "area_change_patterns": area_pattern_distribution,
            "risk_indicators": risk_indicators
        }
    
    def to_dict(self, enhanced_message: EnhancedMQMessage) -> Dict[str, Any]:
        """将增强消息转换为字典格式（用于JSON序列化）"""
        result = {
            "messageType": enhanced_message.messageType,
            "taskId": enhanced_message.taskId,
            "videoId": enhanced_message.videoId,
            "userId": enhanced_message.userId,
            "timestamp": enhanced_message.timestamp,
            "processingStatus": enhanced_message.processingStatus,
            "analysisStatus": enhanced_message.analysisStatus,
            "gasLeakageMode": enhanced_message.gasLeakageMode,
            "overallRiskLevel": enhanced_message.overall_risk_level.value,
            # 兼容性字段
            "videoPath": enhanced_message.videoPath,
            "gasAnalysisSummary": enhanced_message.gasAnalysisSummary,
            "finalTrackedLeakagePoints": enhanced_message.finalTrackedLeakagePoints,
            "gasTrackingSummary": enhanced_message.gasTrackingSummary,
            "temporalGpsSummary": enhanced_message.temporalGpsSummary,
            # 新增字段 - 使用下划线命名以保持一致性
            "leakage_statistics": {
                "totalLeakagePoints": enhanced_message.leakage_statistics.total_leakage_points,
                "confirmedLeakagePoints": enhanced_message.leakage_statistics.confirmed_leakage_points,
                "maxSimultaneousLeaks": enhanced_message.leakage_statistics.max_simultaneous_leaks,
                "leakageDensity": enhanced_message.leakage_statistics.leakage_density,
                "averageLeakDuration": enhanced_message.leakage_statistics.average_leak_duration,
                "totalLeakageTime": enhanced_message.leakage_statistics.total_leakage_time,
                "leakageFrequency": enhanced_message.leakage_statistics.leakage_frequency,
                "coveragePercentage": enhanced_message.leakage_statistics.coverage_percentage
            },
            "gps_persistence_data": {
                "hasGpsData": enhanced_message.gps_persistence_data.has_gps_data,
                "gpsCoverageRatio": enhanced_message.gps_persistence_data.gps_coverage_ratio,
                "startPosition": enhanced_message.gps_persistence_data.start_position,
                "endPosition": enhanced_message.gps_persistence_data.end_position,
                "centerPosition": enhanced_message.gps_persistence_data.center_position,
                "trajectoryBounds": enhanced_message.gps_persistence_data.trajectory_bounds,
                "totalDistanceMeters": enhanced_message.gps_persistence_data.total_distance_meters,
                "averageSpeedMps": enhanced_message.gps_persistence_data.average_speed_mps,
                "gpsQualityAssessment": enhanced_message.gps_persistence_data.gps_quality_assessment,
                "coordinateSystem": enhanced_message.gps_persistence_data.coordinate_system
            }
        }
        
        # 添加PDF报告数据（如果存在）
        if enhanced_message.pdf_report_data:
            result["pdf_report_data"] = enhanced_message.pdf_report_data.to_dict()
        
        return result