#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强数据分析器
Enhanced Data Analyzer
=====================

实现以下核心分析功能：
1. 置信度区间统计分析
2. 泄漏时间段精确化
3. 气体扩散行为分析
4. 泄漏点详细特征提取
5. 风险评估算法
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import math

from ..models.enhanced_message_structure import (
    ConfidenceInterval, LeakageTimeSegment, GasDispersionAnalysis,
    GasDispersionBehavior, RiskLevel, LeakagePointSummary
)

logger = logging.getLogger(__name__)


class ConfidenceAnalyzer:
    """置信度分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_confidence_intervals(self, tracking_data: List[Dict[str, Any]]) -> Dict[str, ConfidenceInterval]:
        """
        分析置信度区间统计
        
        Args:
            tracking_data: 追踪数据列表
            
        Returns:
            按类别分组的置信度区间统计
        """
        try:
            confidence_intervals = {}
            
            # 按类别分组数据
            grouped_data = self._group_by_class(tracking_data)
            
            for class_name, data_points in grouped_data.items():
                confidence_values = [point.get('confidence', 0.0) for point in data_points]
                
                if not confidence_values:
                    continue
                
                confidence_interval = self._calculate_confidence_interval(confidence_values)
                confidence_intervals[class_name] = confidence_interval
                
                self.logger.debug(f"类别 {class_name} 置信度分析完成: "
                                f"平均值={confidence_interval.average_confidence:.3f}, "
                                f"稳定比例={confidence_interval.stable_confidence_ratio:.3f}")
            
            return confidence_intervals
            
        except Exception as e:
            self.logger.error(f"置信度区间分析失败: {e}")
            return {}
    
    def _group_by_class(self, tracking_data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按类别分组追踪数据"""
        grouped = defaultdict(list)
        
        for data_point in tracking_data:
            class_name = data_point.get('class_name', 'unknown')
            grouped[class_name].append(data_point)
        
        return dict(grouped)
    
    def _calculate_confidence_interval(self, confidence_values: List[float]) -> ConfidenceInterval:
        """计算置信度区间统计"""
        confidence_array = np.array(confidence_values)
        
        # 基础统计
        min_conf = float(np.min(confidence_array))
        max_conf = float(np.max(confidence_array))
        avg_conf = float(np.mean(confidence_array))
        median_conf = float(np.median(confidence_array))
        
        # 置信度分布
        distribution = self._calculate_confidence_distribution(confidence_values)
        
        # 稳定置信度比例（>0.7的比例）
        stable_ratio = float(np.sum(confidence_array > 0.7) / len(confidence_array))
        
        return ConfidenceInterval(
            min_confidence=min_conf,
            max_confidence=max_conf,
            average_confidence=avg_conf,
            median_confidence=median_conf,
            confidence_distribution=distribution,
            stable_confidence_ratio=stable_ratio
        )
    
    def _calculate_confidence_distribution(self, confidence_values: List[float]) -> Dict[str, int]:
        """计算置信度分布"""
        bins = [(0.0, 0.2), (0.2, 0.4), (0.4, 0.6), (0.6, 0.8), (0.8, 1.0)]
        distribution = {}
        
        for min_val, max_val in bins:
            key = f"{min_val}-{max_val}"
            count = sum(1 for conf in confidence_values if min_val <= conf < max_val)
            distribution[key] = count
        
        # 处理边界情况（1.0）
        if any(conf == 1.0 for conf in confidence_values):
            distribution["0.8-1.0"] += sum(1 for conf in confidence_values if conf == 1.0)
        
        return distribution


class TimeSegmentAnalyzer:
    """时间段分析器"""
    
    def __init__(self, fps: float = 30.0):
        self.fps = fps
        self.logger = logging.getLogger(__name__)
    
    def analyze_leakage_time_segments(self, tracking_data: List[Dict[str, Any]], 
                                    start_timestamp: Optional[str] = None) -> List[LeakageTimeSegment]:
        """
        分析泄漏时间段
        
        Args:
            tracking_data: 追踪数据列表
            start_timestamp: 视频开始时间戳
            
        Returns:
            泄漏时间段列表
        """
        try:
            if not tracking_data:
                return []
            
            # 按帧号排序
            sorted_data = sorted(tracking_data, key=lambda x: x.get('frame_number', 0))
            
            # 检测连续段
            segments = self._detect_continuous_segments(sorted_data)
            
            # 转换为时间段对象
            time_segments = []
            for segment in segments:
                time_segment = self._create_time_segment(segment, start_timestamp)
                time_segments.append(time_segment)
            
            self.logger.info(f"检测到 {len(time_segments)} 个泄漏时间段")
            return time_segments
            
        except Exception as e:
            self.logger.error(f"时间段分析失败: {e}")
            return []
    
    def _detect_continuous_segments(self, sorted_data: List[Dict[str, Any]], 
                                  max_gap: int = 5) -> List[List[Dict[str, Any]]]:
        """检测连续时间段"""
        if not sorted_data:
            return []
        
        segments = []
        current_segment = [sorted_data[0]]
        
        for i in range(1, len(sorted_data)):
            current_frame = sorted_data[i].get('frame_number', 0)
            prev_frame = sorted_data[i-1].get('frame_number', 0)
            
            # 如果帧间隔小于等于最大间隔，认为是连续的
            if current_frame - prev_frame <= max_gap:
                current_segment.append(sorted_data[i])
            else:
                # 开始新的段
                if len(current_segment) > 0:
                    segments.append(current_segment)
                current_segment = [sorted_data[i]]
        
        # 添加最后一个段
        if len(current_segment) > 0:
            segments.append(current_segment)
        
        return segments
    
    def _create_time_segment(self, segment_data: List[Dict[str, Any]], 
                           start_timestamp: Optional[str] = None) -> LeakageTimeSegment:
        """创建时间段对象"""
        start_frame = segment_data[0].get('frame_number', 0)
        end_frame = segment_data[-1].get('frame_number', 0)
        duration_frames = end_frame - start_frame + 1
        duration_seconds = duration_frames / self.fps
        
        # 计算时间戳
        if start_timestamp:
            base_time = datetime.fromisoformat(start_timestamp.replace('Z', '+00:00'))
            start_time = base_time + timedelta(seconds=start_frame / self.fps)
            end_time = base_time + timedelta(seconds=end_frame / self.fps)
        else:
            # 使用相对时间
            start_time = datetime.fromtimestamp(start_frame / self.fps)
            end_time = datetime.fromtimestamp(end_frame / self.fps)
        
        # 找到峰值置信度
        peak_data = max(segment_data, key=lambda x: x.get('confidence', 0.0))
        peak_frame = peak_data.get('frame_number', start_frame)
        peak_time = start_time + timedelta(seconds=(peak_frame - start_frame) / self.fps)
        
        # 计算平均置信度
        confidences = [point.get('confidence', 0.0) for point in segment_data]
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        # 检测中断
        interruption_count = self._count_interruptions(segment_data)
        
        return LeakageTimeSegment(
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            start_frame=start_frame,
            end_frame=end_frame,
            duration_seconds=duration_seconds,
            duration_frames=duration_frames,
            peak_confidence_time=peak_time.isoformat(),
            peak_confidence_frame=peak_frame,
            average_confidence=float(avg_confidence),
            is_continuous=interruption_count == 0,
            interruption_count=interruption_count
        )
    
    def _count_interruptions(self, segment_data: List[Dict[str, Any]], 
                           confidence_threshold: float = 0.5) -> int:
        """计算中断次数"""
        interruptions = 0
        in_interruption = False
        
        for point in segment_data:
            confidence = point.get('confidence', 0.0)
            
            if confidence < confidence_threshold:
                if not in_interruption:
                    interruptions += 1
                    in_interruption = True
            else:
                in_interruption = False
        
        return interruptions


class DispersionAnalyzer:
    """扩散行为分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_gas_dispersion(self, tracking_data: List[Dict[str, Any]], 
                             fps: float = 30.0) -> GasDispersionAnalysis:
        """
        分析气体扩散行为
        
        Args:
            tracking_data: 追踪数据列表
            fps: 帧率
            
        Returns:
            气体扩散分析结果
        """
        try:
            if not tracking_data:
                return self._create_default_dispersion_analysis()
            
            # 提取位置和面积数据
            positions, areas, frames = self._extract_spatial_data(tracking_data)
            
            if len(positions) < 2:
                return self._create_default_dispersion_analysis()
            
            # 分析扩散行为类型
            behavior_type = self._classify_dispersion_behavior(positions, areas)
            
            # 计算扩散速率
            expansion_rate = self._calculate_expansion_rate(areas, fps)
            
            # 计算移动速度
            movement_velocity = self._calculate_movement_velocity(positions, fps)
            
            # 分析方向一致性
            direction_consistency, main_direction = self._analyze_direction_consistency(positions)
            
            # 分析面积变化模式
            area_change_pattern = self._analyze_area_change_pattern(areas)
            
            # 计算扩散稳定性
            dispersion_stability = self._calculate_dispersion_stability(positions, areas)
            
            # 估算风力影响因子
            wind_influence = self._estimate_wind_influence(positions, areas)
            
            return GasDispersionAnalysis(
                behavior_type=behavior_type,
                expansion_rate=expansion_rate,
                movement_velocity=movement_velocity,
                direction_consistency=direction_consistency,
                main_direction=main_direction,
                area_change_pattern=area_change_pattern,
                dispersion_stability=dispersion_stability,
                wind_influence_factor=wind_influence
            )
            
        except Exception as e:
            self.logger.error(f"气体扩散分析失败: {e}")
            return self._create_default_dispersion_analysis()
    
    def _extract_spatial_data(self, tracking_data: List[Dict[str, Any]]) -> Tuple[List[Tuple[float, float]], List[float], List[int]]:
        """提取空间数据"""
        positions = []
        areas = []
        frames = []
        
        for point in tracking_data:
            # 提取中心位置
            bbox = point.get('bbox', [])
            if len(bbox) >= 4:
                center_x = (bbox[0] + bbox[2]) / 2
                center_y = (bbox[1] + bbox[3]) / 2
                positions.append((center_x, center_y))
                
                # 计算面积
                area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                areas.append(area)
                
                frames.append(point.get('frame_number', 0))
        
        return positions, areas, frames
    
    def _classify_dispersion_behavior(self, positions: List[Tuple[float, float]], 
                                    areas: List[float]) -> GasDispersionBehavior:
        """分类扩散行为"""
        if len(positions) < 3:
            return GasDispersionBehavior.STATIONARY
        
        # 计算位置变化
        position_changes = []
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            distance = math.sqrt(dx*dx + dy*dy)
            position_changes.append(distance)
        
        # 计算面积变化
        area_changes = []
        for i in range(1, len(areas)):
            change = areas[i] - areas[i-1]
            area_changes.append(change)
        
        avg_position_change = np.mean(position_changes)
        avg_area_change = np.mean(area_changes)
        area_variance = np.var(area_changes)
        
        # 分类逻辑
        if avg_position_change < 5 and abs(avg_area_change) < 100:
            return GasDispersionBehavior.STATIONARY
        elif avg_area_change > 200 and avg_position_change < 10:
            return GasDispersionBehavior.EXPANDING
        elif avg_position_change > 15 and abs(avg_area_change) < 150:
            return GasDispersionBehavior.DRIFTING
        elif area_variance > 10000:  # 高方差表示脉冲
            return GasDispersionBehavior.PULSATING
        else:
            return GasDispersionBehavior.IRREGULAR
    
    def _calculate_expansion_rate(self, areas: List[float], fps: float) -> float:
        """计算扩散速率"""
        if len(areas) < 2:
            return 0.0
        
        area_changes = []
        for i in range(1, len(areas)):
            change_per_frame = areas[i] - areas[i-1]
            change_per_second = change_per_frame * fps
            area_changes.append(change_per_second)
        
        return float(np.mean(area_changes)) if area_changes else 0.0
    
    def _calculate_movement_velocity(self, positions: List[Tuple[float, float]], fps: float) -> float:
        """计算移动速度"""
        if len(positions) < 2:
            return 0.0
        
        velocities = []
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            distance_per_frame = math.sqrt(dx*dx + dy*dy)
            velocity = distance_per_frame * fps
            velocities.append(velocity)
        
        return float(np.mean(velocities)) if velocities else 0.0
    
    def _analyze_direction_consistency(self, positions: List[Tuple[float, float]]) -> Tuple[float, Dict[str, float]]:
        """分析方向一致性"""
        if len(positions) < 3:
            return 0.0, {"angle": 0.0, "magnitude": 0.0}
        
        angles = []
        for i in range(1, len(positions) - 1):
            dx1 = positions[i][0] - positions[i-1][0]
            dy1 = positions[i][1] - positions[i-1][1]
            dx2 = positions[i+1][0] - positions[i][0]
            dy2 = positions[i+1][1] - positions[i][1]
            
            angle1 = math.atan2(dy1, dx1)
            angle2 = math.atan2(dy2, dx2)
            
            angle_diff = abs(angle2 - angle1)
            if angle_diff > math.pi:
                angle_diff = 2 * math.pi - angle_diff
            
            angles.append(angle_diff)
        
        # 计算一致性（角度差异越小，一致性越高）
        avg_angle_diff = np.mean(angles) if angles else math.pi
        consistency = 1.0 - (avg_angle_diff / math.pi)
        
        # 计算主要方向
        if len(positions) >= 2:
            total_dx = positions[-1][0] - positions[0][0]
            total_dy = positions[-1][1] - positions[0][1]
            main_angle = math.atan2(total_dy, total_dx)
            main_magnitude = math.sqrt(total_dx*total_dx + total_dy*total_dy)
        else:
            main_angle = 0.0
            main_magnitude = 0.0
        
        return float(consistency), {
            "angle": float(math.degrees(main_angle)),
            "magnitude": float(main_magnitude)
        }
    
    def _analyze_area_change_pattern(self, areas: List[float]) -> str:
        """分析面积变化模式"""
        if len(areas) < 3:
            return "stable"
        
        # 计算趋势
        x = np.arange(len(areas))
        slope, _ = np.polyfit(x, areas, 1)
        
        # 计算变化幅度
        area_std = np.std(areas)
        area_mean = np.mean(areas)
        variation_coefficient = area_std / area_mean if area_mean > 0 else 0
        
        if abs(slope) < 10 and variation_coefficient < 0.2:
            return "stable"
        elif slope > 50:
            return "increasing"
        elif slope < -50:
            return "decreasing"
        else:
            return "fluctuating"
    
    def _calculate_dispersion_stability(self, positions: List[Tuple[float, float]], areas: List[float]) -> float:
        """计算扩散稳定性"""
        if len(positions) < 2 or len(areas) < 2:
            return 0.0
        
        # 位置稳定性
        position_vars = []
        window_size = min(5, len(positions))
        for i in range(len(positions) - window_size + 1):
            window_positions = positions[i:i+window_size]
            x_coords = [pos[0] for pos in window_positions]
            y_coords = [pos[1] for pos in window_positions]
            position_var = np.var(x_coords) + np.var(y_coords)
            position_vars.append(position_var)
        
        # 面积稳定性
        area_vars = []
        for i in range(len(areas) - window_size + 1):
            window_areas = areas[i:i+window_size]
            area_var = np.var(window_areas)
            area_vars.append(area_var)
        
        # 综合稳定性（方差越小，稳定性越高）
        avg_position_var = np.mean(position_vars) if position_vars else 0
        avg_area_var = np.mean(area_vars) if area_vars else 0
        
        # 归一化到0-1范围
        position_stability = 1.0 / (1.0 + avg_position_var / 1000.0)
        area_stability = 1.0 / (1.0 + avg_area_var / 10000.0)
        
        return float((position_stability + area_stability) / 2.0)
    
    def _estimate_wind_influence(self, positions: List[Tuple[float, float]], areas: List[float]) -> float:
        """估算风力影响因子"""
        if len(positions) < 3:
            return 0.0
        
        # 基于运动模式估算风力影响
        # 如果有持续的方向性运动，可能受风力影响
        consistency, main_direction = self._analyze_direction_consistency(positions)
        
        # 基于面积变化估算
        area_change_pattern = self._analyze_area_change_pattern(areas)
        
        wind_factor = 0.0
        
        # 方向一致性高 -> 可能有风力影响
        if consistency > 0.7:
            wind_factor += 0.4
        
        # 持续扩散 -> 可能有风力影响
        if area_change_pattern == "increasing":
            wind_factor += 0.3
        
        # 运动幅度大 -> 可能有风力影响
        if main_direction["magnitude"] > 50:
            wind_factor += 0.3
        
        return min(wind_factor, 1.0)
    
    def _create_default_dispersion_analysis(self) -> GasDispersionAnalysis:
        """创建默认扩散分析结果"""
        return GasDispersionAnalysis(
            behavior_type=GasDispersionBehavior.STATIONARY,
            expansion_rate=0.0,
            movement_velocity=0.0,
            direction_consistency=0.0,
            main_direction={"angle": 0.0, "magnitude": 0.0},
            area_change_pattern="stable",
            dispersion_stability=0.0,
            wind_influence_factor=0.0
        )


class EnhancedDataAnalyzer:
    """增强数据分析器主类"""
    
    def __init__(self, fps: float = 30.0):
        self.fps = fps
        self.confidence_analyzer = ConfidenceAnalyzer()
        self.time_segment_analyzer = TimeSegmentAnalyzer(fps)
        self.dispersion_analyzer = DispersionAnalyzer()
        self.logger = logging.getLogger(__name__)
    
    def analyze_leakage_point(self, track_id: int, tracking_data: List[Dict[str, Any]], 
                            start_timestamp: Optional[str] = None) -> LeakagePointSummary:
        """
        分析单个泄漏点的详细信息
        
        Args:
            track_id: 追踪ID
            tracking_data: 该泄漏点的追踪数据
            start_timestamp: 视频开始时间戳
            
        Returns:
            泄漏点汇总信息
        """
        try:
            if not tracking_data:
                self.logger.warning(f"追踪ID {track_id} 没有数据")
                return self._create_empty_leakage_summary(track_id)
            
            # 置信度分析
            confidence_intervals = self.confidence_analyzer.analyze_confidence_intervals(tracking_data)
            class_name = tracking_data[0].get('class_name', 'unknown')
            confidence_interval = confidence_intervals.get(class_name, self._create_default_confidence_interval())
            
            # 时间段分析
            time_segments = self.time_segment_analyzer.analyze_leakage_time_segments(tracking_data, start_timestamp)
            
            # 扩散行为分析
            dispersion_analysis = self.dispersion_analyzer.analyze_gas_dispersion(tracking_data, self.fps)
            
            # 风险评估
            risk_assessment = self._assess_risk(confidence_interval, time_segments, dispersion_analysis)
            
            # 空间特征
            spatial_characteristics = self._extract_spatial_characteristics(tracking_data)
            
            # 稳定性指标
            stability_metrics = self._calculate_stability_metrics(tracking_data)
            
            # 计算总持续时间
            total_duration = sum(segment.duration_seconds for segment in time_segments)
            total_frames = sum(segment.duration_frames for segment in time_segments)
            
            # 确认泄漏判断
            is_confirmed = self._is_confirmed_leak(confidence_interval, time_segments, dispersion_analysis)
            
            # 严重程度评分
            severity_score = self._calculate_severity_score(confidence_interval, time_segments, dispersion_analysis)
            
            leakage_summary = LeakagePointSummary(
                track_id=track_id,
                class_name=class_name,
                confidence_interval=confidence_interval,
                time_segments=time_segments,
                dispersion_analysis=dispersion_analysis,
                risk_assessment=risk_assessment,
                spatial_characteristics=spatial_characteristics,
                stability_metrics=stability_metrics,
                total_duration_seconds=total_duration,
                total_frames=total_frames,
                is_confirmed_leak=is_confirmed,
                severity_score=severity_score
            )
            
            self.logger.info(f"泄漏点 {track_id} 分析完成: "
                           f"持续时间={total_duration:.1f}s, "
                           f"确认状态={is_confirmed}, "
                           f"严重程度={severity_score:.3f}")
            
            return leakage_summary
            
        except Exception as e:
            self.logger.error(f"泄漏点 {track_id} 分析失败: {e}")
            return self._create_empty_leakage_summary(track_id)
    
    def _create_default_confidence_interval(self) -> ConfidenceInterval:
        """创建默认置信度区间"""
        return ConfidenceInterval(
            min_confidence=0.0,
            max_confidence=0.0,
            average_confidence=0.0,
            median_confidence=0.0,
            confidence_distribution={},
            stable_confidence_ratio=0.0
        )
    
    def _assess_risk(self, confidence_interval: ConfidenceInterval, 
                   time_segments: List[LeakageTimeSegment],
                   dispersion_analysis: GasDispersionAnalysis) -> Dict[str, Any]:
        """评估风险"""
        risk_factors = {
            "confidence_risk": 1.0 - confidence_interval.average_confidence,
            "duration_risk": min(sum(seg.duration_seconds for seg in time_segments) / 60.0, 1.0),
            "dispersion_risk": 1.0 - dispersion_analysis.dispersion_stability,
            "expansion_risk": min(dispersion_analysis.expansion_rate / 1000.0, 1.0)
        }
        
        overall_risk = np.mean(list(risk_factors.values()))
        
        if overall_risk < 0.2:
            risk_level = RiskLevel.LOW
        elif overall_risk < 0.4:
            risk_level = RiskLevel.MEDIUM
        elif overall_risk < 0.7:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.CRITICAL
        
        return {
            "risk_level": risk_level.value,
            "overall_risk_score": float(overall_risk),
            "risk_factors": risk_factors,
            "risk_description": self._get_risk_description(risk_level)
        }
    
    def _extract_spatial_characteristics(self, tracking_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取空间特征"""
        if not tracking_data:
            return {}
        
        bboxes = [point.get('bbox', []) for point in tracking_data if point.get('bbox')]
        if not bboxes:
            return {}
        
        # 计算平均尺寸
        widths = [(bbox[2] - bbox[0]) for bbox in bboxes]
        heights = [(bbox[3] - bbox[1]) for bbox in bboxes]
        areas = [w * h for w, h in zip(widths, heights)]
        
        # 计算位置范围
        center_xs = [(bbox[0] + bbox[2]) / 2 for bbox in bboxes]
        center_ys = [(bbox[1] + bbox[3]) / 2 for bbox in bboxes]
        
        return {
            "average_width": float(np.mean(widths)),
            "average_height": float(np.mean(heights)),
            "average_area": float(np.mean(areas)),
            "max_area": float(np.max(areas)),
            "min_area": float(np.min(areas)),
            "area_variance": float(np.var(areas)),
            "position_range_x": float(np.max(center_xs) - np.min(center_xs)),
            "position_range_y": float(np.max(center_ys) - np.min(center_ys)),
            "center_position": {
                "x": float(np.mean(center_xs)),
                "y": float(np.mean(center_ys))
            }
        }
    
    def _calculate_stability_metrics(self, tracking_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算稳定性指标"""
        if not tracking_data:
            return {}
        
        confidences = [point.get('confidence', 0.0) for point in tracking_data]
        
        return {
            "confidence_stability": float(1.0 - np.std(confidences)) if confidences else 0.0,
            "detection_continuity": len(tracking_data),  # 检测连续性
            "average_confidence": float(np.mean(confidences)) if confidences else 0.0,
            "confidence_trend": self._calculate_confidence_trend(confidences)
        }
    
    def _calculate_confidence_trend(self, confidences: List[float]) -> str:
        """计算置信度趋势"""
        if len(confidences) < 3:
            return "stable"
        
        # 线性回归计算趋势
        x = np.arange(len(confidences))
        slope, _ = np.polyfit(x, confidences, 1)
        
        if slope > 0.01:
            return "increasing"
        elif slope < -0.01:
            return "decreasing"
        else:
            return "stable"
    
    def _is_confirmed_leak(self, confidence_interval: ConfidenceInterval,
                         time_segments: List[LeakageTimeSegment],
                         dispersion_analysis: GasDispersionAnalysis) -> bool:
        """判断是否为确认泄漏"""
        # 确认条件：
        # 1. 平均置信度 > 0.6
        # 2. 稳定置信度比例 > 0.5
        # 3. 总持续时间 > 2秒
        # 4. 扩散稳定性 > 0.3
        
        conditions = [
            confidence_interval.average_confidence > 0.6,
            confidence_interval.stable_confidence_ratio > 0.5,
            sum(seg.duration_seconds for seg in time_segments) > 2.0,
            dispersion_analysis.dispersion_stability > 0.3
        ]
        
        # 至少满足3个条件
        return sum(conditions) >= 3
    
    def _calculate_severity_score(self, confidence_interval: ConfidenceInterval,
                                time_segments: List[LeakageTimeSegment],
                                dispersion_analysis: GasDispersionAnalysis) -> float:
        """计算严重程度评分"""
        # 权重分配
        weights = {
            "confidence": 0.3,
            "duration": 0.25,
            "expansion": 0.25,
            "stability": 0.2
        }
        
        # 各项评分
        confidence_score = confidence_interval.average_confidence
        duration_score = min(sum(seg.duration_seconds for seg in time_segments) / 30.0, 1.0)
        expansion_score = min(dispersion_analysis.expansion_rate / 500.0, 1.0)
        stability_score = 1.0 - dispersion_analysis.dispersion_stability
        
        # 加权平均
        severity = (
            weights["confidence"] * confidence_score +
            weights["duration"] * duration_score +
            weights["expansion"] * expansion_score +
            weights["stability"] * stability_score
        )
        
        return float(min(severity, 1.0))
    
    def _get_risk_description(self, risk_level: RiskLevel) -> str:
        """获取风险描述"""
        descriptions = {
            RiskLevel.NONE: "无风险",
            RiskLevel.LOW: "低风险 - 建议持续监控",
            RiskLevel.MEDIUM: "中等风险 - 建议加强检查",
            RiskLevel.HIGH: "高风险 - 需要立即关注",
            RiskLevel.CRITICAL: "严重风险 - 需要紧急处理"
        }
        return descriptions.get(risk_level, "未知风险")
    
    def _create_empty_leakage_summary(self, track_id: int) -> LeakagePointSummary:
        """创建空的泄漏点汇总"""
        return LeakagePointSummary(
            track_id=track_id,
            class_name="unknown",
            confidence_interval=self._create_default_confidence_interval(),
            time_segments=[],
            dispersion_analysis=self.dispersion_analyzer._create_default_dispersion_analysis(),
            risk_assessment={"risk_level": RiskLevel.NONE.value, "overall_risk_score": 0.0},
            spatial_characteristics={},
            stability_metrics={},
            total_duration_seconds=0.0,
            total_frames=0,
            is_confirmed_leak=False,
            severity_score=0.0
        )