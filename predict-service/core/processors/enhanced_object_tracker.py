#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强型目标追踪器 - 融合ByteTracker算法与镜头运动补偿
集成了卡尔曼滤波、两阶段关联算法和镜头运动补偿功能
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass, field
from collections import defaultdict, deque
import time
import math
from enum import Enum

# 导入ByteTracker相关组件
try:
    from ultralytics.trackers.utils.kalman_filter import KalmanFilterXYAH
    from ultralytics.trackers.utils import matching
    from ultralytics.trackers.basetrack import BaseTrack, TrackState
except ImportError:
    # 如果无法导入，使用简化版本
    class TrackState(Enum):
        New = 0
        Tracked = 1
        Lost = 2
        Removed = 3

    class BaseTrack:
        _count = 0
        track_id = 0
        is_activated = False
        state = TrackState.New
        history = {}
        features = []
        curr_feature = None
        score = 0
        start_frame = 0
        frame_id = 0
        time_since_update = 0
        location = (np.inf, np.inf)

        @staticmethod
        def next_id():
            BaseTrack._count += 1
            return BaseTrack._count

        @staticmethod
        def reset_id():
            BaseTrack._count = 0

        def mark_lost(self):
            self.state = TrackState.Lost

        def mark_removed(self):
            self.state = TrackState.Removed

logger = logging.getLogger(__name__)

@dataclass
class CameraMotion:
    """镜头运动信息"""
    translation: Tuple[float, float] = (0.0, 0.0) # 平移 (dx, dy)
    rotation: float = 0.0 # 旋转角度
    scale: float = 1.0 # 缩放比例
    confidence: float = 0.0 # 运动估计置信度

class CameraMotionCompensator:
    """镜头运动补偿器"""

    def __init__(self, max_features: int = 1000, quality_level: float = 0.01, min_distance: int = 10):
        self.max_features = max_features
        self.quality_level = quality_level
        self.min_distance = min_distance
        self.prev_gray = None
        self.prev_points = None
        self.motion_history = deque(maxlen=10)

        # Lucas-Kanade光流参数
        self.lk_params = dict(
            winSize=(15, 15),
            maxLevel=2,
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
        )

    def detect_camera_motion(self, frame: np.ndarray) -> CameraMotion:
        """检测镜头运动"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame

        if self.prev_gray is None:
            self.prev_gray = gray
            self.prev_points = cv2.goodFeaturesToTrack(
                gray, self.max_features, self.quality_level, self.min_distance
            )
            return CameraMotion()

        try:
            if self.prev_points is not None and len(self.prev_points) > 10:
                curr_points, status, error = cv2.calcOpticalFlowPyrLK(
                    self.prev_gray, gray, self.prev_points, None, **self.lk_params
                )

                valid_old = self.prev_points[status == 1]
                valid_new = curr_points[status == 1]

                if len(valid_old) >= 8:
                    transform_matrix, inliers = cv2.estimateAffinePartial2D(
                        valid_old, valid_new, method=cv2.RANSAC, ransacReprojThreshold=3.0
                    )

                    if transform_matrix is not None:
                        dx = transform_matrix[0, 2]
                        dy = transform_matrix[1, 2]
                        rotation = math.atan2(transform_matrix[1, 0], transform_matrix[0, 0])
                        scale_x = math.sqrt(transform_matrix[0, 0]**2 + transform_matrix[1, 0]**2)
                        scale_y = math.sqrt(transform_matrix[0, 1]**2 + transform_matrix[1, 1]**2)
                        scale = (scale_x + scale_y) / 2
                        confidence = np.sum(inliers) / len(valid_old) if inliers is not None else 0.0

                        motion = CameraMotion(
                            translation=(dx, dy),
                            rotation=rotation,
                            scale=scale,
                            confidence=confidence
                        )

                        self.motion_history.append(motion)
                        self.prev_points = cv2.goodFeaturesToTrack(
                            gray, self.max_features, self.quality_level, self.min_distance
                        )
                        self.prev_gray = gray
                        return motion

            self.prev_points = cv2.goodFeaturesToTrack(
                gray, self.max_features, self.quality_level, self.min_distance
            )
            self.prev_gray = gray
            return CameraMotion()

        except Exception as e:
            logger.warning(f"相机运动估计失败: {e}")
            self.prev_gray = gray
            return CameraMotion()

class EnhancedSTrack(BaseTrack):
    """
    增强的STrack类，集成镜头运动补偿功能
    """

    def __init__(self, xywh, score, cls, use_kalman=True):
        """初始化增强追踪对象"""
        super().__init__()

        # 基础属性
        self.xywh = np.asarray(xywh[:4], dtype=np.float32)
        self.score = score
        self.cls = cls
        self.idx = xywh[-1] if len(xywh) > 4 else 0
        self.angle = xywh[4] if len(xywh) == 6 else None

        # 卡尔曼滤波相关
        self.use_kalman = use_kalman
        if use_kalman:
            try:
                self.kalman_filter = KalmanFilterXYAH()
                self.mean, self.covariance = None, None
            except:
                self.use_kalman = False
                logger.warning("卡尔曼滤波器初始化失败，使用简化追踪")

        # 追踪历史
        self.track_history = deque(maxlen=30)
        self.confidence_history = deque(maxlen=10)
        self.velocity = (0.0, 0.0)

        # 状态管理
        self.consecutive_miss = 0
        self.age = 0
        self.stable_frames = 0
        self.last_seen_frame = 0

        # 镜头运动补偿相关
        self.motion_compensated = False
        self.original_position = None

    @property
    def tlwh(self):
        """获取tlwh格式边界框"""
        if self.use_kalman and self.mean is not None:
            ret = self.mean[:4].copy()
            ret[2] *= ret[3] # w = aspect_ratio * h
            ret[:2] -= ret[2:] / 2 # 转换为左上角坐标
            return ret
        else:
            # 从xywh转换为tlwh
            ret = self.xywh.copy()
            ret[:2] -= ret[2:] / 2
            return ret

    @property
    def xyxy(self):
        """获取xyxy格式边界框"""
        ret = self.tlwh.copy()
        ret[2:] += ret[:2]
        return ret

    @property
    def tlbr(self):
        """获取tlbr格式边界框（与xyxy相同）"""
        return self.xyxy

    @property
    def center(self):
        """获取中心点坐标"""
        if self.use_kalman and self.mean is not None:
            return (self.mean[0], self.mean[1])
        else:
            return (self.xywh[0], self.xywh[1])

    def predict(self):
        """使用卡尔曼滤波预测下一状态"""
        if self.use_kalman and self.kalman_filter is not None and self.mean is not None:
            mean_state = self.mean.copy()
            if self.state != TrackState.Tracked:
                mean_state[7] = 0 # 重置速度
            self.mean, self.covariance = self.kalman_filter.predict(mean_state, self.covariance)
        else:
            # 简化预测：基于速度
            self.xywh[0] += self.velocity[0]
            self.xywh[1] += self.velocity[1]

    def activate(self, frame_id):
        """激活追踪对象"""
        self.track_id = self.next_id()

        if self.use_kalman and self.kalman_filter is not None:
            # 初始化卡尔曼滤波器
            tlwh = self.tlwh
            xyah = self._tlwh_to_xyah(tlwh)
            self.mean, self.covariance = self.kalman_filter.initiate(xyah)

        self.state = TrackState.Tracked
        self.is_activated = True
        self.frame_id = frame_id
        self.start_frame = frame_id
        self.last_seen_frame = frame_id

        logger.debug(f"激活新追踪对象 ID={self.track_id}, class={self.cls}")

    def update(self, new_detection, frame_id):
        """更新追踪对象状态"""
        old_center = self.center

        # 更新基础信息
        self.frame_id = frame_id
        self.last_seen_frame = frame_id
        self.consecutive_miss = 0
        self.age += 1
        self.stable_frames += 1
        self.score = new_detection.score
        self.cls = new_detection.cls

        if self.use_kalman and self.kalman_filter is not None and self.mean is not None:
            # 使用卡尔曼滤波更新
            new_tlwh = new_detection.tlwh
            xyah = self._tlwh_to_xyah(new_tlwh)
            self.mean, self.covariance = self.kalman_filter.update(
                self.mean, self.covariance, xyah
            )
        else:
            # 简化更新
            self.xywh = new_detection.xywh.copy()

        # 更新历史记录
        current_center = self.center
        self.track_history.append(current_center)
        self.confidence_history.append(self.score)

        # 计算速度
        if old_center != (0, 0):
            self.velocity = (
                current_center[0] - old_center[0],
                current_center[1] - old_center[1]
            )

        self.state = TrackState.Tracked
        self.is_activated = True

    def apply_camera_motion_compensation(self, camera_motion: CameraMotion):
        """应用镜头运动补偿"""
        if camera_motion.confidence < 0.3:
            return

        dx, dy = camera_motion.translation

        if self.use_kalman and self.mean is not None:
            # 补偿卡尔曼滤波器状态
            self.mean[0] -= dx # x坐标补偿
            self.mean[1] -= dy # y坐标补偿
        else:
            # 补偿简化状态
            self.xywh[0] -= dx
            self.xywh[1] -= dy

        self.motion_compensated = True

    def _tlwh_to_xyah(self, tlwh):
        """转换tlwh到xyah格式"""
        ret = np.asarray(tlwh).copy()
        ret[:2] += ret[2:] / 2 # 转换为中心点
        ret[2] /= ret[3] # aspect ratio = w/h
        return ret

    @property
    def is_confirmed(self):
        """判断目标是否已确认"""
        return (self.stable_frames >= 3 and
                len(self.confidence_history) > 0 and
                np.mean(list(self.confidence_history)) > 0.6)

class EnhancedObjectTracker:
    """
    增强型目标追踪器 - 专为不规则气体泄漏检测优化
    
    本类融合了ByteTracker算法与镜头运动补偿功能，专门针对不规则形状的气体云团进行优化：
    
    核心功能：
    1. 多阶段关联算法：处理气体形状变化和分裂/合并情况
    2. 镜头运动补偿：消除相机抖动对气体追踪的影响
    3. 卡尔曼滤波预测：预测气体扩散轨迹
    4. 置信度自适应：根据气体检测质量动态调整追踪策略
    
    不规则气体处理特性：
    - 支持形状变化：通过IoU和中心距离双重匹配机制
    - 处理分裂合并：低置信度检测用于维持轨迹连续性
    - 扩散预测：基于气体物理特性的运动模型
    - 边界模糊：容忍检测边界的不确定性
    
    适用场景：
    - 工业气体泄漏监测
    - 环境污染物扩散追踪
    - 烟雾火焰检测
    - 其他不规则形状目标追踪
    """

    def __init__(self,
                 track_high_thresh: float = 0.6,
                 track_low_thresh: float = 0.1,
                 new_track_thresh: float = 0.7,
                 match_thresh: float = 0.8,
                 track_buffer: int = 30,
                 frame_rate: int = 30,
                 use_kalman: bool = True):
        """
        初始化增强型目标追踪器
        
        参数设计针对不规则气体特性进行优化：
        - track_high_thresh=0.6: 较低的高置信度阈值，适应气体边界模糊性
        - track_low_thresh=0.1: 极低的低置信度阈值，捕获气体扩散边缘
        - new_track_thresh=0.7: 适中的新轨迹阈值，避免噪声干扰
        - match_thresh=0.8: 较高的匹配阈值，确保追踪稳定性
        - track_buffer=30: 较长的缓冲时间，适应气体间歇性检测

        Args:
            track_high_thresh: 高置信度检测阈值，用于主要追踪
            track_low_thresh: 低置信度检测阈值，用于轨迹恢复
            new_track_thresh: 新轨迹创建阈值，防止误检
            match_thresh: IoU匹配阈值，控制关联严格程度
            track_buffer: 轨迹缓冲帧数，丢失后保持时间
            frame_rate: 视频帧率，影响时间相关参数
            use_kalman: 是否使用卡尔曼滤波进行状态预测
        """
        # ByteTracker参数
        self.track_high_thresh = track_high_thresh
        self.track_low_thresh = track_low_thresh
        self.new_track_thresh = new_track_thresh
        self.match_thresh = match_thresh
        self.max_time_lost = int(frame_rate / 30.0 * track_buffer)
        self.use_kalman = use_kalman

        # 追踪状态管理
        self.tracked_stracks = []  # 活跃轨迹：当前正在追踪的气体目标
        self.lost_stracks = []     # 丢失轨迹：暂时丢失但可能恢复的气体目标
        self.removed_stracks = []  # 移除轨迹：确认消失的气体目标
        self.frame_id = 0

        # 镜头运动补偿器
        self.motion_compensator = CameraMotionCompensator()

        # 重置ID计数器
        BaseTrack.reset_id()

        logger.info(f"增强型目标追踪器初始化完成 (Kalman: {use_kalman})")

    def update(self, detections: List[Dict], frame: np.ndarray) -> Tuple[List[EnhancedSTrack], CameraMotion]:
        """
        更新追踪器状态 - 不规则气体追踪的核心算法
        
        本方法实现了专为不规则气体优化的多阶段追踪流程：
        1. 镜头运动检测：消除相机抖动对气体追踪的影响
        2. 运动补偿：对现有轨迹进行位置校正
        3. 置信度分离：区分清晰和模糊的气体检测
        4. 多阶段关联：处理气体形状变化、分裂、合并
        5. 状态更新：维护轨迹生命周期
        
        不规则气体处理策略：
        - 低置信度检测用于维持轨迹连续性（气体边缘模糊）
        - 双重匹配机制：IoU + 中心距离（适应形状变化）
        - 预测补偿：基于气体扩散物理特性
        - 容错机制：允许短期检测失败

        Args:
            detections: 检测结果列表，每个检测包含 bbox, confidence, class_id, class_name
            frame: 当前帧图像，用于镜头运动分析

        Returns:
            (tracked_objects, camera_motion): 活跃追踪对象列表和镜头运动信息
        """
        self.frame_id += 1

        # 1. 检测镜头运动 - 分析相机位移、旋转、缩放
        camera_motion = self.motion_compensator.detect_camera_motion(frame)

        # 2. 应用镜头运动补偿 - 校正所有轨迹位置，消除相机抖动影响
        self._apply_camera_motion_compensation(camera_motion)

        # 3. 分离高低置信度检测 - 区分清晰气体和边缘模糊区域
        high_conf_dets, low_conf_dets = self._separate_detections(detections)

        # 4. 多阶段关联算法 - 处理气体形状变化、分裂、合并情况
        activated_stracks, refind_stracks, lost_stracks, removed_stracks = self._multi_stage_association(
            high_conf_dets, low_conf_dets, frame
        )

        # 5. 更新轨迹状态 - 维护气体目标的生命周期
        self._update_track_states(activated_stracks, refind_stracks, lost_stracks, removed_stracks)
        
        # TODO: 调试信息 - 分析轨迹状态更新结果
        logger.debug(f"[DEBUG] 轨迹状态更新结果:")
        logger.debug(f"  - activated_stracks: {len(activated_stracks)}")
        logger.debug(f"  - refind_stracks: {len(refind_stracks)}")
        logger.debug(f"  - lost_stracks: {len(lost_stracks)}")
        logger.debug(f"  - removed_stracks: {len(removed_stracks)}")
        logger.debug(f"  - 总tracked_stracks: {len(self.tracked_stracks)}")

        # 6. 返回活跃轨迹 - 过滤出当前正在追踪的气体目标
        active_tracks = [t for t in self.tracked_stracks if t.is_activated]
        
        # TODO: 调试信息 - 分析最终返回结果
        logger.debug(f"[DEBUG] 最终返回结果:")
        logger.debug(f"  - active_tracks数量: {len(active_tracks)}")
        for i, track in enumerate(active_tracks):
            logger.debug(f"  - track {i}: id={track.track_id}, score={track.score}, is_activated={track.is_activated}")
        
        if len(active_tracks) == 0:
            logger.warning(f"[DEBUG] 返回的active_tracks为空！可能原因:")
            logger.warning(f"  1. 输入detections为空")
            logger.warning(f"  2. 所有检测都被置信度阈值过滤")
            logger.warning(f"  3. 多阶段关联失败")
            logger.warning(f"  4. 轨迹未被正确激活")

        return active_tracks, camera_motion

    def _apply_camera_motion_compensation(self, camera_motion: CameraMotion):
        """
        对所有轨迹应用镜头运动补偿
        
        镜头运动补偿对不规则气体追踪至关重要：
        - 气体边界模糊，容易受相机抖动影响
        - 补偿平移、旋转、缩放变化
        - 提高轨迹连续性和稳定性
        """
        all_tracks = self.tracked_stracks + self.lost_stracks

        for track in all_tracks:
            track.apply_camera_motion_compensation(camera_motion)

    def _separate_detections(self, detections: List[Dict]) -> Tuple[List[EnhancedSTrack], List[EnhancedSTrack]]:
        """
        分离高低置信度检测 - 不规则气体的关键预处理步骤
        
        气体检测特点：
        - 中心区域置信度高（气体浓度大）
        - 边缘区域置信度低（气体扩散边界）
        - 低置信度检测用于维持轨迹连续性
        - 避免因边界模糊导致的轨迹断裂
        
        Returns:
            (high_conf_tracks, low_conf_tracks): 高置信度和低置信度轨迹列表
        """
        high_conf_tracks = []
        low_conf_tracks = []
        
        # TODO: 调试信息 - 分析为什么track为空
        logger.debug(f"[DEBUG] _separate_detections 输入参数:")
        logger.debug(f"  - detections数量: {len(detections)}")
        logger.debug(f"  - track_low_thresh: {self.track_low_thresh}")
        logger.debug(f"  - track_high_thresh: {self.track_high_thresh}")
        
        if len(detections) == 0:
            logger.warning(f"[DEBUG] detections列表为空，无法创建追踪对象")
            return high_conf_tracks, low_conf_tracks

        for i, det in enumerate(detections):
            confidence = det.get('confidence', 0)
            logger.debug(f"[DEBUG] 检测 {i}: confidence={confidence}, threshold={self.track_low_thresh}")
            
            if confidence < self.track_low_thresh:
                logger.debug(f"[DEBUG] 检测 {i} 被过滤: confidence({confidence}) < track_low_thresh({self.track_low_thresh})")
                continue

            # 创建追踪对象
            try:
                bbox = det['bbox'] # [x1, y1, x2, y2]
                logger.debug(f"[DEBUG] 检测 {i} bbox: {bbox}")
                
                # 验证bbox格式
                if len(bbox) != 4:
                    logger.error(f"[DEBUG] 检测 {i} bbox格式错误: 期望4个值，实际{len(bbox)}个")
                    continue
                    
                xywh = [
                    (bbox[0] + bbox[2]) / 2, # center_x
                    (bbox[1] + bbox[3]) / 2, # center_y
                    bbox[2] - bbox[0], # width
                    bbox[3] - bbox[1], # height
                    i # index
                ]
                logger.debug(f"[DEBUG] 检测 {i} 转换后xywh: {xywh}")

                track = EnhancedSTrack(xywh, confidence, det.get('class_id', 0), self.use_kalman)
                logger.debug(f"[DEBUG] 检测 {i} 成功创建EnhancedSTrack对象")

                if confidence >= self.track_high_thresh:
                    high_conf_tracks.append(track)
                    logger.debug(f"[DEBUG] 检测 {i} 添加到高置信度列表")
                else:
                    low_conf_tracks.append(track)
                    logger.debug(f"[DEBUG] 检测 {i} 添加到低置信度列表")
                    
            except Exception as e:
                logger.error(f"[DEBUG] 检测 {i} 创建EnhancedSTrack失败: {e}")
                logger.error(f"[DEBUG] 检测数据: {det}")
                continue
        
        logger.debug(f"[DEBUG] _separate_detections 结果:")
        logger.debug(f"  - 高置信度轨迹数量: {len(high_conf_tracks)}")
        logger.debug(f"  - 低置信度轨迹数量: {len(low_conf_tracks)}")
        
        return high_conf_tracks, low_conf_tracks

    def _multi_stage_association(self, high_conf_dets: List[EnhancedSTrack],
                                low_conf_dets: List[EnhancedSTrack],
                                frame: np.ndarray) -> Tuple[List, List, List, List]:
        """
        多阶段关联算法 - 专为不规则气体优化的ByteTracker算法
        
        不规则气体追踪的核心挑战：
        1. 形状变化：气体扩散导致边界框变化
        2. 分裂合并：气体云团可能分裂或合并
        3. 边界模糊：检测置信度不稳定
        4. 间歇性：气体可能暂时消失
        
        多阶段处理策略：
        - 第一阶段：高置信度检测与活跃轨迹关联（主要追踪）
        - 第二阶段：低置信度检测与丢失轨迹关联（轨迹恢复）
        - 第三阶段：未确认轨迹处理（新目标确认）
        - 第四阶段：新轨迹创建（新气体泄漏点）
        
        Returns:
            (activated, refind, lost, removed): 激活、重新发现、丢失、移除的轨迹列表
        """
        activated_stracks = []
        refind_stracks = []
        lost_stracks = []
        removed_stracks = []

        # 分离确认和未确认轨迹 - 区分稳定气体和新出现气体
        unconfirmed = []  # 未确认轨迹：新检测到的潜在气体
        tracked_stracks = []  # 已确认轨迹：稳定追踪的气体目标
        for track in self.tracked_stracks:
            if not track.is_activated:
                unconfirmed.append(track)
            else:
                tracked_stracks.append(track)

        # 第一阶段：高置信度检测与活跃轨迹关联
        # 处理清晰可见的气体区域与现有轨迹的匹配
        strack_pool = tracked_stracks + self.lost_stracks

        # 预测轨迹位置 - 基于气体扩散模型预测下一帧位置
        for track in strack_pool:
            track.predict()

        # 计算距离矩阵并进行匹配 - 使用IoU和中心距离双重度量
        if high_conf_dets and strack_pool:
            dists = self._calculate_distance_matrix(strack_pool, high_conf_dets)
            matches, u_track, u_detection = self._linear_assignment(dists, self.match_thresh)

            # 处理匹配结果 - 更新成功关联的轨迹
            for itracked, idet in matches:
                track = strack_pool[itracked]
                det = high_conf_dets[idet]
                if track.state == TrackState.Tracked:
                    track.update(det, self.frame_id)
                    activated_stracks.append(track)  # 继续追踪的气体
                else:
                    track.update(det, self.frame_id)
                    refind_stracks.append(track)  # 重新发现的气体
        else:
            u_track = list(range(len(strack_pool)))
            u_detection = list(range(len(high_conf_dets)))

        # 第二阶段：低置信度检测与未匹配轨迹关联
        # 关键：利用气体边缘模糊区域维持轨迹连续性
        if low_conf_dets:
            r_tracked_stracks = [strack_pool[i] for i in u_track if strack_pool[i].state == TrackState.Tracked]

            if r_tracked_stracks:
                dists = self._calculate_distance_matrix(r_tracked_stracks, low_conf_dets)
                matches, u_track2, u_detection2 = self._linear_assignment(dists, 0.5)  # 更宽松的阈值

                # 处理低置信度匹配 - 防止气体轨迹因边界模糊而断裂
                for itracked, idet in matches:
                    track = r_tracked_stracks[itracked]
                    det = low_conf_dets[idet]
                    if track.state == TrackState.Tracked:
                        track.update(det, self.frame_id)
                        activated_stracks.append(track)  # 通过低置信度维持的轨迹
                    else:
                        track.update(det, self.frame_id)
                        refind_stracks.append(track)  # 通过低置信度恢复的轨迹

                # 标记未匹配轨迹为丢失 - 气体可能暂时消散
                for it in u_track2:
                    track = r_tracked_stracks[it]
                    if track.state != TrackState.Lost:
                        track.mark_lost()
                        lost_stracks.append(track)

        # 第三阶段：处理未确认轨迹 - 新气体目标的确认过程
        remaining_dets = [high_conf_dets[i] for i in u_detection]
        if unconfirmed and remaining_dets:
            dists = self._calculate_distance_matrix(unconfirmed, remaining_dets)
            matches, u_unconfirmed, u_detection3 = self._linear_assignment(dists, 0.7)  # 严格阈值确保准确性

            # 确认新的气体目标 - 将候选轨迹转为正式轨迹
            for itracked, idet in matches:
                unconfirmed[itracked].update(remaining_dets[idet], self.frame_id)
                activated_stracks.append(unconfirmed[itracked])  # 新确认的气体轨迹

            # 移除未确认的候选轨迹 - 可能是误检或噪声
            for it in u_unconfirmed:
                track = unconfirmed[it]
                track.mark_removed()
                removed_stracks.append(track)

            remaining_dets = [remaining_dets[i] for i in u_detection3]

        # 第四阶段：创建新轨迹 - 检测新的气体泄漏点
        for det in remaining_dets:
            if det.score >= self.new_track_thresh:  # 高置信度才创建新轨迹，避免误检
                det.activate(self.frame_id)
                activated_stracks.append(det)  # 新发现的气体泄漏

        return activated_stracks, refind_stracks, lost_stracks, removed_stracks

    def _calculate_distance_matrix(self, tracks: List[EnhancedSTrack],
                                  detections: List[EnhancedSTrack]) -> np.ndarray:
        """
        计算轨迹与检测之间的距离矩阵 - 专为不规则气体优化
        
        不规则气体的距离计算挑战：
        1. 形状变化：传统IoU可能不够准确
        2. 边界模糊：需要容忍边界不确定性
        3. 中心偏移：气体扩散可能导致中心位置变化
        
        优化策略：
        - 使用IoU作为主要度量（适应形状变化）
        - 结合中心距离作为辅助度量
        - 对气体特性进行特殊处理
        
        Returns:
            距离矩阵，值越小表示匹配度越高
        """
        if not tracks or not detections:
            return np.empty((0, 0))

        # 使用IoU距离
        dists = np.zeros((len(tracks), len(detections)))

        for i, track in enumerate(tracks):
            for j, det in enumerate(detections):
                # 计算IoU
                iou = self._calculate_iou(track.xyxy, det.xyxy)
                dists[i, j] = 1 - iou # 转换为距离

        return dists

    def _calculate_iou(self, bbox1: np.ndarray, bbox2: np.ndarray) -> float:
        """计算两个边界框的IoU"""
        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2

        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)

        # 计算并集
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def _linear_assignment(self, cost_matrix: np.ndarray, thresh: float) -> Tuple[List, List, List]:
        """线性分配算法（简化版匈牙利算法）"""
        if cost_matrix.size == 0:
            return [], list(range(cost_matrix.shape[0])), list(range(cost_matrix.shape[1]))

        try:
            # 尝试使用scipy的线性分配
            from scipy.optimize import linear_sum_assignment
            row_ind, col_ind = linear_sum_assignment(cost_matrix)

            matches = []
            unmatched_a = []
            unmatched_b = []

            for i in range(cost_matrix.shape[0]):
                if i not in row_ind:
                    unmatched_a.append(i)

            for j in range(cost_matrix.shape[1]):
                if j not in col_ind:
                    unmatched_b.append(j)

            for i, j in zip(row_ind, col_ind):
                if cost_matrix[i, j] <= thresh:
                    matches.append((i, j))
                else:
                    unmatched_a.append(i)
                    unmatched_b.append(j)

            return matches, unmatched_a, unmatched_b

        except ImportError:
            # 回退到贪婪匹配
            return self._greedy_assignment(cost_matrix, thresh)

    def _greedy_assignment(self, cost_matrix: np.ndarray, thresh: float) -> Tuple[List, List, List]:
        """贪婪匹配算法"""
        matches = []
        used_rows = set()
        used_cols = set()

        # 按成本排序
        costs = []
        for i in range(cost_matrix.shape[0]):
            for j in range(cost_matrix.shape[1]):
                if cost_matrix[i, j] <= thresh:
                    costs.append((cost_matrix[i, j], i, j))

        costs.sort()

        for cost, i, j in costs:
            if i not in used_rows and j not in used_cols:
                matches.append((i, j))
                used_rows.add(i)
                used_cols.add(j)

        unmatched_a = [i for i in range(cost_matrix.shape[0]) if i not in used_rows]
        unmatched_b = [j for j in range(cost_matrix.shape[1]) if j not in used_cols]

        return matches, unmatched_a, unmatched_b

    def _update_track_states(self, activated_stracks, refind_stracks, lost_stracks, removed_stracks):
        """更新轨迹状态"""
        # 更新活跃轨迹列表
        self.tracked_stracks = [t for t in self.tracked_stracks if t.state == TrackState.Tracked]
        self.tracked_stracks.extend(activated_stracks)
        self.tracked_stracks.extend(refind_stracks)

        # 更新丢失轨迹列表
        self.lost_stracks = [t for t in self.lost_stracks if t not in self.tracked_stracks]
        self.lost_stracks.extend(lost_stracks)
        self.lost_stracks = [t for t in self.lost_stracks if t not in removed_stracks]

        # 移除超时轨迹
        for track in self.lost_stracks[:]:
            if self.frame_id - track.frame_id > self.max_time_lost:
                track.mark_removed()
                removed_stracks.append(track)
                self.lost_stracks.remove(track)

        # 更新移除轨迹列表
        self.removed_stracks.extend(removed_stracks)
        if len(self.removed_stracks) > 1000:
            self.removed_stracks = self.removed_stracks[-999:]

    def get_leakage_summary(self) -> Dict[str, Any]:
        """获取检测点汇总信息"""
        active_detection_points = {}

        for track in self.tracked_stracks:
            if track.is_confirmed:
                active_detection_points[track.track_id] = {
                    'position': track.center,
                    'confidence': track.score,
                    'age': track.age,
                    'class_name': getattr(track, 'class_name', f'class_{track.cls}')
                }

        return {
            'active_detection_points': active_detection_points,
            'total_points_detected': len(active_detection_points)
        }

    def reset(self):
        """重置追踪器状态"""
        self.tracked_stracks.clear()
        self.lost_stracks.clear()
        self.removed_stracks.clear()
        self.motion_compensator = CameraMotionCompensator()
        self.frame_id = 0
        BaseTrack.reset_id()
        logger.info("增强型目标追踪器已重置")

    def draw_tracking_info(self, frame: np.ndarray) -> np.ndarray:
        """在帧上绘制追踪信息"""
        result_frame = frame.copy()

        for track in self.tracked_stracks:
            if track.is_confirmed:
                # 绘制边界框
                bbox = track.xyxy.astype(int)
                color = (0, 255, 0) if track.cls == 0 else (255, 0, 0) # 假设类别0为泄漏
                cv2.rectangle(result_frame,
                            (bbox[0], bbox[1]),
                            (bbox[2], bbox[3]),
                            color, 2)

                # 绘制ID和信息
                label = f"ID:{track.track_id} {track.score:.2f}"
                cv2.putText(result_frame, label,
                          (bbox[0], bbox[1] - 10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                # 绘制轨迹
                if len(track.track_history) > 1:
                    points = [(int(p[0]), int(p[1])) for p in track.track_history]
                    for i in range(1, len(points)):
                        cv2.line(result_frame, points[i-1], points[i], color, 1)

        return result_frame

    def reset(self):
        """重置追踪器状态"""
        self.tracked_stracks.clear()
        self.lost_stracks.clear()
        self.removed_stracks.clear()
        self.motion_compensator = CameraMotionCompensator()
        self.frame_id = 0
        BaseTrack.reset_id()
        logger.info("增强型目标追踪器已重置")

# TODO: 添加配置文件支持，允许动态调整追踪参数
# TODO: 集成ReID特征提取，提高长期追踪稳定性
# TODO: 添加多类别追踪优化，针对不同类别使用不同参数
# TODO: 实现轨迹平滑算法，减少抖动
# TODO: 添加性能监控和统计功能