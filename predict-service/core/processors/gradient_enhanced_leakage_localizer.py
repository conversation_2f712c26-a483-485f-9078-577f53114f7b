#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
梯度增强泄漏源定位模块

功能：在现有光流分析基础上，结合图像梯度变化定位疑似气体泄漏源点
作者：Trae AI 编程助手
创建时间：2024

主要功能：
1. 梯度分析：使用Sobel算子计算图像梯度
2. 光流发散度计算：分析光流向量的发散特性
3. 泄漏源定位：结合梯度和光流信息推理泄漏源点
4. 可视化：生成调试图像和最终叠加图
"""

import cv2
import numpy as np
import logging
from typing import Tuple, Optional, Dict, List, Any
from sklearn.cluster import DBSCAN, MeanShift
import math

logger = logging.getLogger(__name__)

class GradientEnhancedLeakageLocalizer:
    """
    梯度增强泄漏源定位器

    在现有光流分析基础上，通过梯度分析和发散度计算来精确定位气体泄漏源点。
    该类设计为可插拔模块，不修改现有光流计算逻辑。
    """

    def __init__(self, debug_mode: bool = False):
        """
        初始化梯度增强泄漏源定位器

        Args:
            debug_mode: 是否启用调试模式，输出中间处理图像
        """
        self.debug_mode = True
        self.debug_images = {} # 存储调试图像

        # 梯度计算参数
        self.sobel_ksize = 3 # Sobel算子核大小
        self.gradient_threshold = 30 # 梯度幅值阈值

        # 发散度计算参数
        self.divergence_kernel_size = 5 # 发散度计算窗口大小
        self.divergence_threshold = 0.5 # 发散度阈值

        # 聚类参数
        self.clustering_eps = 30 # DBSCAN聚类半径
        self.clustering_min_samples = 3 # DBSCAN最小样本数

        # 可视化参数
        self.leak_point_color = (0, 0, 255) # 泄漏源点颜色（红色）
        self.leak_point_size = 8 # 泄漏源点大小

    def analyze_gradient_and_flow(self,
                                 prev_frame: np.ndarray,
                                 curr_frame: np.ndarray,
                                 yolo_mask: np.ndarray,
                                 flow: np.ndarray,
                                 flow_magnitude: np.ndarray,
                                 flow_angle: np.ndarray) -> Dict[str, Any]:
        """
        主要分析函数：结合梯度和光流信息定位泄漏源

        Args:
            prev_frame: 前一帧图像 (BGR格式)
            curr_frame: 当前帧图像 (BGR格式)
            yolo_mask: YOLOv8输出的气体mask区域 (uint8, 255为mask)
            flow: 光流向量场 (H, W, 2)
            flow_magnitude: 光流幅度 (H, W)
            flow_angle: 光流角度 (H, W)

        Returns:
            包含泄漏源点坐标和可视化图像的字典
        """
        try:
            # 1. 梯度分析
            gradient_result = self._compute_gradient_analysis(curr_frame, yolo_mask)

            # 2. 光流发散度计算
            divergence_result = self._compute_flow_divergence(flow, yolo_mask)

            # 3. 联合推理泄漏源点
            leak_sources = self._locate_leak_sources(
                gradient_result, divergence_result, flow_magnitude, yolo_mask
            )

            # 4. 生成可视化图像
            overlay_frame = self._create_visualization(
                curr_frame, flow, flow_magnitude, flow_angle,
                leak_sources, yolo_mask
            )

            # 5. 准备返回结果
            result = {
                'leak_source_points': leak_sources,
                'overlay_frame': overlay_frame,
                'gradient_magnitude': gradient_result['magnitude'],
                'gradient_direction': gradient_result['direction'],
                'flow_divergence': divergence_result['divergence_map'],
                'candidate_points': divergence_result['candidate_points']
            }

            # 添加调试信息
            if self.debug_mode:
                result['debug_images'] = self.debug_images.copy()

            return result

        except Exception as e:
            logger.error(f"梯度增强泄漏源分析失败: {e}")
            return {
                'leak_source_points': [],
                'overlay_frame': curr_frame.copy(),
                'error': str(e)
            }

    def _compute_gradient_analysis(self, frame: np.ndarray, mask: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算图像梯度分析

        使用Sobel算子计算水平和垂直梯度，然后计算梯度幅值和方向。
        梯度变化显著的区域可能是气体泄漏的边界或源点。

        Args:
            frame: 输入图像 (BGR格式)
            mask: 气体检测mask

        Returns:
            包含梯度信息的字典
        """
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 应用高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 计算Sobel梯度
        grad_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=self.sobel_ksize)
        grad_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=self.sobel_ksize)

        # 计算梯度幅值和方向
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        direction = np.arctan2(grad_y, grad_x) # 弧度制

        # 应用mask，只保留气体区域的梯度
        mask_binary = (mask > 127).astype(np.uint8)
        magnitude_masked = magnitude * mask_binary
        direction_masked = direction * mask_binary

        # 调试可视化
        if self.debug_mode:
            # 梯度幅值可视化
            # 修复：处理NaN值避免normalize警告
            magnitude_clean_debug = np.nan_to_num(magnitude_masked, nan=0.0, posinf=0.0, neginf=0.0)
            magnitude_vis = cv2.normalize(magnitude_clean_debug, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            self.debug_images['gradient_magnitude'] = cv2.applyColorMap(magnitude_vis, cv2.COLORMAP_JET)

            # 梯度方向可视化
            direction_vis = ((direction_masked + np.pi) / (2 * np.pi) * 255).astype(np.uint8)
            self.debug_images['gradient_direction'] = cv2.applyColorMap(direction_vis, cv2.COLORMAP_HSV)

        return {
            'magnitude': magnitude_masked,
            'direction': direction_masked,
            'grad_x': grad_x,
            'grad_y': grad_y
        }

    def _compute_flow_divergence(self, flow: np.ndarray, mask: np.ndarray) -> Dict[str, Any]:
        """
        计算光流发散度

        发散度衡量向量场在某点的"发散"程度。正发散度表示向量从该点向外发散，
        这是气体泄漏源的重要特征。计算公式：div(F) = ∂u/∂x + ∂v/∂y

        Args:
            flow: 光流向量场 (H, W, 2)
            mask: 气体检测mask

        Returns:
            包含发散度信息的字典
        """
        h, w = flow.shape[:2]
        divergence = np.zeros((h, w), dtype=np.float32)

        # 提取u和v分量
        u = flow[:, :, 0] # x方向分量
        v = flow[:, :, 1] # y方向分量

        # 计算偏导数（使用中心差分）
        du_dx = np.zeros_like(u)
        dv_dy = np.zeros_like(v)

        # 中心差分计算偏导数
        du_dx[:, 1:-1] = (u[:, 2:] - u[:, :-2]) / 2.0
        dv_dy[1:-1, :] = (v[2:, :] - v[:-2, :]) / 2.0

        # 边界处理（前向/后向差分）
        du_dx[:, 0] = u[:, 1] - u[:, 0]
        du_dx[:, -1] = u[:, -1] - u[:, -2]
        dv_dy[0, :] = v[1, :] - v[0, :]
        dv_dy[-1, :] = v[-1, :] - v[-2, :]

        # 计算发散度
        divergence = du_dx + dv_dy

        # 应用mask
        mask_binary = (mask > 127).astype(np.uint8)
        divergence_masked = divergence * mask_binary

        # 寻找发散度候选点（正发散度且超过阈值）
        candidate_points = []
        high_divergence_mask = (divergence_masked > self.divergence_threshold) & (mask_binary > 0)

        if np.any(high_divergence_mask):
            y_coords, x_coords = np.where(high_divergence_mask)
            for i in range(len(x_coords)):
                candidate_points.append({
                    'x': int(x_coords[i]),
                    'y': int(y_coords[i]),
                    'divergence': float(divergence_masked[y_coords[i], x_coords[i]])
                })

        # 调试可视化
        if self.debug_mode:
            # 发散度热图
            # 修复：处理NaN值避免normalize警告
            divergence_clean = np.nan_to_num(np.abs(divergence_masked), nan=0.0, posinf=0.0, neginf=0.0)
            div_vis = cv2.normalize(divergence_clean, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            self.debug_images['flow_divergence'] = cv2.applyColorMap(div_vis, cv2.COLORMAP_JET)

            # 候选点可视化
            candidate_vis = mask.copy()
            for point in candidate_points:
                cv2.circle(candidate_vis, (point['x'], point['y']), 3, (255, 255, 255), -1)
            self.debug_images['candidate_points'] = candidate_vis

        return {
            'divergence_map': divergence_masked,
            'candidate_points': candidate_points,
            'du_dx': du_dx,
            'dv_dy': dv_dy
        }

    def _locate_leak_sources(self,
                           gradient_result: Dict[str, np.ndarray],
                           divergence_result: Dict[str, Any],
                           flow_magnitude: np.ndarray,
                           mask: np.ndarray) -> List[Dict[str, Any]]:
        """
        联合梯度和光流信息定位泄漏源点

        结合以下特征寻找泄漏源：
        1. 梯度幅值显著增加的区域
        2. 光流发散度高的点
        3. 光流幅度适中的区域（避免噪声）

        Args:
            gradient_result: 梯度分析结果
            divergence_result: 发散度分析结果
            flow_magnitude: 光流幅度
            mask: 气体检测mask

        Returns:
            泄漏源点列表
        """
        leak_sources = []

        # 获取候选点
        candidate_points = divergence_result['candidate_points']
        if not candidate_points:
            logger.debug("未找到发散度候选点")
            return leak_sources

        # 提取候选点坐标用于聚类
        points_array = np.array([[p['x'], p['y']] for p in candidate_points])

        if len(points_array) < self.clustering_min_samples:
            # 候选点太少，直接使用最高发散度的点
            best_point = max(candidate_points, key=lambda p: p['divergence'])

            # 验证该点的梯度特征
            x, y = best_point['x'], best_point['y']
            gradient_mag = gradient_result['magnitude'][y, x]
            flow_mag = flow_magnitude[y, x]

            if gradient_mag > self.gradient_threshold:
                leak_sources.append({
                    'x': x,
                    'y': y,
                    'confidence': self._calculate_confidence(best_point['divergence'], gradient_mag, flow_mag),
                    'divergence': best_point['divergence'],
                    'gradient_magnitude': float(gradient_mag),
                    'flow_magnitude': float(flow_mag),
                    'source_type': 'single_point',
                    'divergence_score': float(best_point['divergence'])
                })
        else:
            # 使用DBSCAN聚类候选点
            try:
                clustering = DBSCAN(eps=self.clustering_eps, min_samples=self.clustering_min_samples)
                cluster_labels = clustering.fit_predict(points_array)

                # 处理每个聚类
                unique_labels = set(cluster_labels)
                for label in unique_labels:
                    if label == -1: # 噪声点
                        continue

                    # 获取聚类中的点
                    cluster_mask = cluster_labels == label
                    cluster_points = points_array[cluster_mask]
                    cluster_candidates = [candidate_points[i] for i in range(len(candidate_points)) if cluster_mask[i]]

                    # 计算聚类中心
                    center_x = int(np.mean(cluster_points[:, 0]))
                    center_y = int(np.mean(cluster_points[:, 1]))

                    # 确保中心点在图像范围内
                    center_x = max(0, min(mask.shape[1]-1, center_x))
                    center_y = max(0, min(mask.shape[0]-1, center_y))

                    # 计算聚类的平均特征
                    avg_divergence = np.mean([p['divergence'] for p in cluster_candidates])
                    gradient_mag = gradient_result['magnitude'][center_y, center_x]
                    flow_mag = flow_magnitude[center_y, center_x]

                    # 验证聚类中心的特征
                    if gradient_mag > self.gradient_threshold:
                        leak_sources.append({
                            'x': center_x,
                            'y': center_y,
                            'confidence': self._calculate_confidence(avg_divergence, gradient_mag, flow_mag),
                            'divergence': float(avg_divergence),
                            'gradient_magnitude': float(gradient_mag),
                            'flow_magnitude': float(flow_mag),
                            'source_type': 'clustered_point',
                            'cluster_size': len(cluster_candidates),
                            'divergence_score': float(avg_divergence)
                        })

            except Exception as e:
                logger.warning(f"聚类分析失败，使用备选方案: {e}")
                # 备选方案：选择发散度最高的点
                best_point = max(candidate_points, key=lambda p: p['divergence'])
                x, y = best_point['x'], best_point['y']
                gradient_mag = gradient_result['magnitude'][y, x]
                flow_mag = flow_magnitude[y, x]

                leak_sources.append({
                    'x': x,
                    'y': y,
                    'confidence': self._calculate_confidence(best_point['divergence'], gradient_mag, flow_mag),
                    'divergence': best_point['divergence'],
                    'gradient_magnitude': float(gradient_mag),
                    'flow_magnitude': float(flow_mag),
                    'source_type': 'fallback_point',
                    'divergence_score': float(best_point['divergence'])
                })

        # 按置信度排序
        leak_sources.sort(key=lambda x: x['confidence'], reverse=True)

        logger.info(f"定位到 {len(leak_sources)} 个潜在泄漏源点")
        return leak_sources

    def _calculate_confidence(self, divergence: float, gradient_mag: float, flow_mag: float) -> float:
        """
        计算泄漏源点的置信度

        综合考虑发散度、梯度幅值和光流幅度来评估泄漏源的可信度。

        Args:
            divergence: 发散度值
            gradient_mag: 梯度幅值
            flow_mag: 光流幅值

        Returns:
            置信度分数 (0-1)
        """
        # 归一化各个特征
        div_score = min(1.0, divergence / (self.divergence_threshold * 3)) # 发散度分数
        grad_score = min(1.0, gradient_mag / (self.gradient_threshold * 2)) # 梯度分数
        flow_score = min(1.0, flow_mag / 5.0) # 光流分数（适中的光流更可信）

        # 加权平均
        confidence = 0.4 * div_score + 0.4 * grad_score + 0.2 * flow_score

        return float(confidence)

    def _create_visualization(self,
                            frame: np.ndarray,
                            flow: np.ndarray,
                            flow_magnitude: np.ndarray,
                            flow_angle: np.ndarray,
                            leak_sources: List[Dict[str, Any]],
                            mask: np.ndarray) -> np.ndarray:
        """
        创建包含泄漏源点的可视化图像

        在原图基础上叠加HSV光流可视化和泄漏源点标记。

        Args:
            frame: 原始图像
            flow: 光流向量场
            flow_magnitude: 光流幅度
            flow_angle: 光流角度
            leak_sources: 泄漏源点列表
            mask: 气体检测mask

        Returns:
            可视化图像
        """
        # 1. 创建增强的HSV光流背景
        overlay = self._create_enhanced_hsv_flow_background(frame, flow, flow_magnitude, flow_angle, mask)

        # 2. 绘制光流箭头（简化版本，避免过于密集）
        overlay = self._draw_simplified_flow_arrows(overlay, flow, mask, flow_magnitude)

        # 3. 绘制泄漏源点
        for i, source in enumerate(leak_sources):
            x, y = source['x'], source['y']
            confidence = source['confidence']

            # 根据置信度调整标记大小和颜色
            if confidence > 0.7:
                color = (0, 0, 255) # 红色 - 高置信度
                size = self.leak_point_size + 2
            elif confidence > 0.5:
                color = (0, 165, 255) # 橙色 - 中等置信度
                size = self.leak_point_size
            else:
                color = (0, 255, 255) # 黄色 - 低置信度
                size = self.leak_point_size - 2

            # 绘制十字标记
            cv2.drawMarker(overlay, (x, y), color, cv2.MARKER_CROSS, size, 3)

            # 绘制圆圈
            cv2.circle(overlay, (x, y), size//2, color, 2)

            # 添加置信度文本
            text = f"S{i+1}: {confidence:.2f}"
            cv2.putText(overlay, text, (x + 10, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # 3. 添加图例
        overlay = self._add_legend(overlay, len(leak_sources))

        return overlay

    def _draw_simplified_flow_arrows(self, image: np.ndarray, flow: np.ndarray,
                                   mask: np.ndarray, magnitude: np.ndarray) -> np.ndarray:
        """
        绘制简化的光流箭头（避免过于密集）

        Args:
            image: 输入图像
            flow: 光流向量场
            mask: 气体检测mask
            magnitude: 光流幅度

        Returns:
            绘制了光流箭头的图像
        """
        h, w = image.shape[:2]
        mask_binary = (mask > 127).astype(np.uint8)

        # 设置采样间隔（增加箭头密度）
        step = 15 # 每15像素绘制一个箭头（原来25，现在更密集）
        min_magnitude = 0.5 # 最小幅度阈值（降低阈值显示更多箭头）

        for y in range(step//2, h, step):
            for x in range(step//2, w, step):
                if mask_binary[y, x] == 0:
                    continue

                mag = magnitude[y, x]
                if mag < min_magnitude:
                    continue

                # 计算箭头终点
                dx = flow[y, x, 0]
                dy = flow[y, x, 1]

                arrow_length = min(mag * 18, step * 0.8) # 增大箭头长度（原来10，现在18）
                end_x = int(x + dx * arrow_length / mag) if mag > 0 else x
                end_y = int(y + dy * arrow_length / mag) if mag > 0 else y

                # 确保终点在图像范围内
                end_x = max(0, min(w-1, end_x))
                end_y = max(0, min(h-1, end_y))

                # 设置箭头颜色（蓝色系，与泄漏源点区分）
                color = (255, 100, 0) # 蓝色

                # 绘制箭头（增加线条粗细和箭头尖端大小）
                cv2.arrowedLine(image, (x, y), (end_x, end_y), color, 2, tipLength=0.4)

                # 在箭头起点绘制小圆点（增强可见性）
                cv2.circle(image, (x, y), 2, color, -1)

        return image

    def _create_enhanced_hsv_flow_background(self, frame: np.ndarray, flow: np.ndarray,
                                           magnitude: np.ndarray, angle: np.ndarray,
                                           mask: np.ndarray) -> np.ndarray:
        """
        创建增强的HSV光流背景可视化

        为梯度增强模块提供专门的HSV光流可视化，与主模块保持一致性。

        Args:
            frame: 原始图像
            flow: 光流向量场
            magnitude: 光流幅度
            angle: 光流角度
            mask: 气体检测mask

        Returns:
            HSV光流背景图像
        """
        h, w = frame.shape[:2]
        mask_binary = (mask > 127).astype(np.uint8)

        # 检查是否有显著运动
        mean_magnitude = np.mean(magnitude[mask_binary > 0]) if np.any(mask_binary > 0) else 0

        if mean_magnitude < 0.1:
            # 如果没有显著运动，返回原图
            return frame.copy()

        # 1. 创建HSV图像
        hsv = np.zeros((h, w, 3), dtype=np.uint8)

        # 2. 色相通道：编码流动方向
        # 将角度从[-π, π]映射到[0, 179]
        # 修复：处理angle中的NaN值
        angle_safe = np.nan_to_num(angle, nan=0.0, posinf=0.0, neginf=0.0)
        hue_raw = (angle_safe + np.pi) * 179 / (2 * np.pi)
        hue_safe = np.nan_to_num(hue_raw, nan=0.0, posinf=179.0, neginf=0.0)
        hue = np.clip(hue_safe, 0, 179).astype(np.uint8)
        hsv[..., 0] = hue

        # 3. 饱和度通道：根据幅度调整，突出强流动区域
        # 修复：在normalize之前处理NaN值
        magnitude_clean = np.nan_to_num(magnitude, nan=0.0, posinf=0.0, neginf=0.0)
        magnitude_normalized = cv2.normalize(magnitude_clean, None, 0, 1, cv2.NORM_MINMAX)
        # 为梯度增强模块使用稍微不同的饱和度映射
        saturation_raw = magnitude_normalized * 255 * 1.1
        saturation_safe = np.nan_to_num(saturation_raw, nan=0.0, posinf=255.0, neginf=0.0)
        saturation = np.clip(saturation_safe, 0, 255).astype(np.uint8)
        hsv[..., 1] = saturation

        # 4. 亮度通道：使用适合梯度分析的亮度映射
        # 修复：处理NaN和无穷大值，避免RuntimeWarning
        magnitude_safe = np.nan_to_num(magnitude_normalized, nan=0.0, posinf=1.0, neginf=0.0)
        # 确保所有值都在有效范围内
        magnitude_safe = np.clip(magnitude_safe, 0.0, 1.0)
        magnitude_enhanced = np.power(magnitude_safe, 0.6) # 稍微不同的伽马值
        brightness_base = 170 # 稍低的基础亮度，为泄漏源点留出对比度
        brightness_range = 80 # 稍大的亮度变化范围
        brightness_raw = brightness_base + magnitude_enhanced * brightness_range
        brightness_safe = np.nan_to_num(brightness_raw, nan=brightness_base, posinf=245, neginf=40)
        brightness = np.clip(brightness_safe, 40, 245).astype(np.uint8) # 为泄漏源点标记留出亮度空间
        hsv[..., 2] = brightness

        # 5. 转换为BGR并应用掩码
        flow_rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
        masked_flow = flow_rgb * mask_binary[..., np.newaxis]

        # 6. 与原图混合
        alpha = 0.75 # 稍微不同的混合比例
        result = cv2.addWeighted(frame, 1.0, masked_flow, alpha, 0)

        # 7. 添加梯度特定的高亮效果
        # 突出显示高梯度区域（可能的泄漏源）
        high_gradient_threshold = np.percentile(magnitude[mask_binary > 0], 90)
        high_gradient_mask = (magnitude > high_gradient_threshold).astype(np.uint8) * mask_binary

        if np.any(high_gradient_mask):
            # 为高梯度区域添加特殊标记
            hsv_highlight = hsv.copy()
            hsv_highlight[..., 1] = 255 # 最大饱和度
            hsv_highlight[..., 2] = 255 # 最大亮度
            flow_highlight = cv2.cvtColor(hsv_highlight, cv2.COLOR_HSV2BGR)

            masked_highlight = flow_highlight * high_gradient_mask[..., np.newaxis]
            result = cv2.addWeighted(result, 1.0, masked_highlight, 0.25, 0)

        return result

    def _add_legend(self, image: np.ndarray, num_sources: int) -> np.ndarray:
        """
        添加图例说明

        Args:
            image: 输入图像
            num_sources: 泄漏源数量

        Returns:
            添加了图例的图像
        """
        # 移除图例显示，直接返回原图像
        return image


def enhance_optical_flow_with_gradient_analysis(prev_frame: np.ndarray,
                                               curr_frame: np.ndarray,
                                               yolo_mask: np.ndarray,
                                               existing_flow_result: Dict[str, Any],
                                               debug_mode: bool = False) -> Dict[str, Any]:
    """
    光流分析的梯度增强扩展函数

    这是主要的集成函数，用于在现有光流分析基础上添加梯度分析和泄漏源定位功能。
    可以直接在frame_processor.py的_compute_flow函数中调用。

    Args:
        prev_frame: 前一帧图像 (BGR格式)
        curr_frame: 当前帧图像 (BGR格式)
        yolo_mask: YOLOv8输出的气体mask区域 (uint8, 255为mask)
        existing_flow_result: 现有光流分析结果，包含'image'和'velocity'键
        debug_mode: 是否启用调试模式

    Returns:
        增强后的分析结果，包含泄漏源点信息
    """
    try:
        # 重新计算光流（因为需要原始flow数据）
        prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
        curr_gray = cv2.cvtColor(curr_frame, cv2.COLOR_BGR2GRAY)

        # 计算光流
        flow = cv2.calcOpticalFlowFarneback(prev_gray, curr_gray, None, 0.5, 3, 15, 3, 5, 1.2, 0)
        magnitude, angle = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # 创建梯度增强定位器
        localizer = GradientEnhancedLeakageLocalizer(debug_mode=debug_mode)

        # 执行增强分析
        enhanced_result = localizer.analyze_gradient_and_flow(
            prev_frame, curr_frame, yolo_mask, flow, magnitude, angle
        )

        # 合并结果
        result = existing_flow_result.copy()
        result.update({
            'enhanced_analysis': enhanced_result,
            'leak_source_points': enhanced_result.get('leak_source_points', []),
            'gradient_enhanced_overlay': enhanced_result.get('overlay_frame'),
        })

        # TODO: 可以考虑将泄漏源点信息集成到tracking系统中
        # TODO: 添加时序分析，跟踪泄漏源点的稳定性
        # TODO: 优化聚类算法参数，适应不同场景的气体泄漏模式

        logger.info(f"梯度增强分析完成，发现 {len(enhanced_result.get('leak_source_points', []))} 个潜在泄漏源")

        return result

    except Exception as e:
        logger.error(f"梯度增强光流分析失败: {e}")
        # 返回原始结果
        return existing_flow_result


# 使用示例和集成指南
if __name__ == "__main__":
    """
    使用示例：

    # 在frame_processor.py的_compute_flow函数中集成：

    def _compute_flow(self, prev_gray, curr_gray, mask, fps, scale):
        # ... 现有光流计算代码 ...

        # 新增：梯度增强泄漏源定位
        from core.processors.gradient_enhanced_leakage_localizer import enhance_optical_flow_with_gradient_analysis

        # 准备输入数据
        prev_frame_bgr = cv2.cvtColor(prev_gray, cv2.COLOR_GRAY2BGR)
        curr_frame_bgr = cv2.cvtColor(curr_gray, cv2.COLOR_GRAY2BGR)
        yolo_mask = np.zeros_like(prev_gray)
        cv2.fillPoly(yolo_mask, [np.array(mask, dtype=np.int32)], 255)

        # 执行增强分析
        enhanced_result = enhance_optical_flow_with_gradient_analysis(
            prev_frame_bgr, curr_frame_bgr, yolo_mask,
            {'image': result, 'velocity': velocity},
            debug_mode=True
        )

        # 使用增强后的可视化图像
        if 'gradient_enhanced_overlay' in enhanced_result:
            result = enhanced_result['gradient_enhanced_overlay']

        return enhanced_result
    """
    print("梯度增强泄漏源定位模块已就绪")
    print("请参考文档注释中的集成指南进行使用")