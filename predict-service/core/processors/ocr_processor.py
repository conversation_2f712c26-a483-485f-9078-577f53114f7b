import logging
import traceback



import cv2
import numpy as np
from typing import Dict, Optional, List, Tuple, Any
import re
from paddleocr import PaddleOCR
logger = logging.getLogger(__name__)
# ppocr 日志级别由统一日志配置管理，不在此处硬编码


class OCRProcessor:
    def __init__(self):
        """初始化OCR处理器"""
        self.ocr = None
        self._ocr_initialized = False
        self._initialize_ocr()

    def _initialize_ocr(self):
        """初始化PaddleOCR"""
        if self._ocr_initialized:
            return

        try:
            # 环境变量已在模块导入时设置，强制使用CPU模式
            self.ocr = PaddleOCR(use_angle_cls=False, enable_hpi=False, lang='en',
            det_model_dir='core/models/ocr_v3/en_PP-OCRv3_det_infer',
            rec_model_dir='core/models/ocr_v3/en_PP-OCRv3_rec_infer')
            self._ocr_initialized = True
            logger.info("OCR initialized successfully in CPU mode")
        except Exception as e:
            logger.error(f"Exception traceback: {traceback.format_exc()}")
            logger.error(f"OCR initialization failed: {e}")
            self.ocr = None

    def _extract_text_info(self, result: Any, label: str) -> Dict[str, Any]:
        """从OCR结果中提取文本信息"""
        extracted_info: Dict[str, Any] = {}

        try:
            if not result or len(result) == 0:
                return extracted_info

            # 处理结果的第一层
            detections = result[0] if result and len(result) > 0 else []
            if not detections:
                return extracted_info

            for detection in detections:
                if not detection or len(detection) != 2:
                    continue

                try:
                    # 解析：detection = [bbox, (text, confidence)]
                    bbox, text_conf = detection

                    if not isinstance(text_conf, (tuple, list)) or len(text_conf) != 2:
                        continue

                    text, confidence = text_conf

                    # 类型检查和置信度过滤
                    if not isinstance(text, str) or not isinstance(confidence, (int, float)):
                        continue

                    if confidence <= 0.5:
                        continue

                    # 文本提取逻辑
                    if label == "UTC":
                        utc_time = self._extract_utc_time(text)
                        if utc_time:
                            extracted_info["UTC"] = utc_time
                            break

                    elif label == "Lat":
                        lat_value = self._extract_latitude(text)
                        if lat_value is not None:
                            extracted_info["Lat"] = lat_value
                            break

                    elif label == "Lon":
                        lon_value = self._extract_longitude(text)
                        if lon_value is not None:
                            extracted_info["Lon"] = lon_value
                            break

                except Exception:
                    continue

        except Exception:
            pass

        return extracted_info

    def _extract_utc_time(self, text: str) -> Optional[str]:
        """提取UTC时间 - Python 3.11类型注解"""
        utc_patterns = [
            r'(\d{4}-\d{2}-\d{2}\d{2}:\d{2}:\d{2})', # 2021-02-0518:44:59
            r'(\d{2}:\d{2}:\d{2})', # HH:MM:SS
            r'(\d{2}-\d{2}-\d{2})', # HH-MM-SS
            r'UTC[:\s]*(\d{2}:\d{2}:\d{2})', # UTC:HH:MM:SS
        ]

        for pattern in utc_patterns:
            match = re.search(pattern, text)
            if match:
                utc_time = match.group(1)
                # 格式化特殊情况
                if len(utc_time) > 10 and '-' in utc_time and utc_time.count(':') == 2:
                    if re.match(r'\d{4}-\d{2}-\d{2}\d{2}:\d{2}:\d{2}', utc_time):
                        utc_time = utc_time[:10] + ' ' + utc_time[10:]
                return utc_time
        return None

    def _extract_latitude(self, text: str) -> Optional[float]:
        """提取纬度 - Python 3.11类型注解"""
        lat_patterns = [
            r'Lat[:\s]*([+-]?\d+\.?\d*)', # Lat:31.65249
            r'([NS])\s*(\d+)°(\d+)\'(\d+\.?\d*)"', # N 31°65'24.9"
            r'([+-]?\d+\.?\d*)[°\s]*[NS]?', # 31.65249°
        ]

        for pattern in lat_patterns:
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                try:
                    if len(groups) == 1: # 十进制度
                        return float(groups[0])
                    elif len(groups) == 4: # 度分秒
                        direction, degrees, minutes, seconds = groups
                        lat_decimal = float(degrees) + float(minutes)/60 + float(seconds)/3600
                        return -lat_decimal if direction == 'S' else lat_decimal
                except (ValueError, TypeError):
                    continue
        return None

    def _extract_longitude(self, text: str) -> Optional[float]:
        """提取经度 - Python 3.11类型注解"""
        lon_patterns = [
            r'Lon[:\s]*([+-]?\d+\.?\d*)', # Lon:-103.776149
            r'([EW])\s*(\d+)°(\d+)\'(\d+\.?\d*)"', # E 123°45'67.8"
            r'([+-]?\d+\.?\d*)[°\s]*[EW]?', # 123.45678°
        ]

        for pattern in lon_patterns:
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                try:
                    if len(groups) == 1: # 十进制度
                        return float(groups[0])
                    elif len(groups) == 4: # 度分秒
                        direction, degrees, minutes, seconds = groups
                        lon_decimal = float(degrees) + float(minutes)/60 + float(seconds)/3600
                        return -lon_decimal if direction == 'W' else lon_decimal
                except (ValueError, TypeError):
                    continue
        return None

    def process_frame(self, frame: np.ndarray) -> Dict[str, Optional[Any]]:
        """处理单帧图像并提取OCR信息"""
        if not self._ocr_initialized:
            self._initialize_ocr()

        if self.ocr is None:
            return {"UTC": None, "Lat": None, "Lon": None}

        # 定义三个不同的掩模区域
        regions = [
            (np.array([[0, 0], [285, 0], [285, 14], [0, 14]]), "UTC"),
            (np.array([[0, 16], [160, 16], [160, 30], [0, 30]]), "Lat"),
            (np.array([[0, 31], [185, 31], [185, 44], [0, 44]]), "Lon")
        ]

        extracted_info = {"UTC": None, "Lat": None, "Lon": None}

        # 处理每个区域
        for points, label in regions:
            try:
                # 创建掩模并提取ROI
                mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                cv2.fillPoly(mask, [points], 255)
                masked_frame = cv2.bitwise_and(frame, frame, mask=mask)

                # OCR识别 - 移除不支持的cls参数
                result = self.ocr.ocr(masked_frame)

                # 提取文本信息
                text_info = self._extract_text_info(result, label)
                extracted_info.update(text_info)

            except Exception:
                logger.error(f"Error processing region {label}: {Exception}")
                logger.error(f"Exception traceback: {traceback.format_exc()}")
                continue
        return extracted_info

    def cleanup(self) -> None:
        """清理OCR资源"""
        if self.ocr is not None:
            try:
                del self.ocr
                self.ocr = None
                logger.info("OCR resources cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up OCR: {e}")

        self._ocr_initialized = False