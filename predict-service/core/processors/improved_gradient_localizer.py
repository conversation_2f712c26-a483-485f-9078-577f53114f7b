#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版梯度增强泄漏源定位器

主要改进：
1. 时序一致性约束 - 卡尔曼滤波跟踪
2. 多尺度特征融合 - 拉普拉斯+梯度+发散度
3. 自适应参数调整 - 基于场景动态优化
4. 鲁棒数值计算 - 高斯加权发散度
5. 置信度重新设计 - 基于历史稳定性

Author: Trae AI Assistant
Date: 2024
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from sklearn.cluster import DBSCAN
from collections import deque
import logging

logger = logging.getLogger(__name__)


class KalmanTracker:
    """
    卡尔曼滤波器用于跟踪泄漏源点的时序一致性

    功能：
    - 预测下一帧泄漏源位置
    - 平滑位置估计，减少漂移
    - 评估跟踪稳定性
    """

    def __init__(self, initial_x: float, initial_y: float):
        """
        初始化卡尔曼滤波器

        Args:
            initial_x: 初始x坐标
            initial_y: 初始y坐标
        """
        # 状态向量: [x, y, vx, vy] (位置和速度)
        self.kalman = cv2.KalmanFilter(4, 2)

        # 状态转移矩阵 (匀速运动模型)
        self.kalman.transitionMatrix = np.array([
            [1, 0, 1, 0],
            [0, 1, 0, 1],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ], dtype=np.float32)

        # 观测矩阵 (只观测位置)
        self.kalman.measurementMatrix = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ], dtype=np.float32)

        # 过程噪声协方差
        self.kalman.processNoiseCov = np.eye(4, dtype=np.float32) * 0.1

        # 测量噪声协方差
        self.kalman.measurementNoiseCov = np.eye(2, dtype=np.float32) * 1.0

        # 初始状态
        self.kalman.statePre = np.array([initial_x, initial_y, 0, 0], dtype=np.float32)
        self.kalman.statePost = np.array([initial_x, initial_y, 0, 0], dtype=np.float32)

        # 跟踪历史
        self.history = deque(maxlen=10)
        self.stability_score = 0.0

    def predict(self) -> Tuple[float, float]:
        """
        预测下一帧位置

        Returns:
            预测的(x, y)坐标
        """
        prediction = self.kalman.predict()
        return float(prediction[0]), float(prediction[1])

    def update(self, x: float, y: float) -> Tuple[float, float]:
        """
        更新滤波器状态

        Args:
            x: 观测到的x坐标
            y: 观测到的y坐标

        Returns:
            校正后的(x, y)坐标
        """
        measurement = np.array([x, y], dtype=np.float32)
        corrected = self.kalman.correct(measurement)

        # 更新历史记录
        self.history.append((float(corrected[0]), float(corrected[1])))

        # 计算稳定性分数
        self._update_stability_score()

        return float(corrected[0]), float(corrected[1])

    def _update_stability_score(self):
        """
        计算跟踪稳定性分数

        基于位置变化的标准差计算稳定性
        """
        if len(self.history) < 3:
            self.stability_score = 0.0
            return

        positions = np.array(self.history)
        x_std = np.std(positions[:, 0])
        y_std = np.std(positions[:, 1])

        # 稳定性分数：标准差越小越稳定
        movement_variance = x_std + y_std
        self.stability_score = max(0.0, 1.0 - movement_variance / 20.0)


class ImprovedGradientLeakageLocalizer:
    """
    改进版梯度增强泄漏源定位器

    主要改进：
    1. 多尺度特征融合
    2. 时序一致性约束
    3. 自适应参数调整
    4. 鲁棒数值计算
    """

    def __init__(self, debug_mode: bool = False):
        """
        初始化改进版定位器

        Args:
            debug_mode: 是否启用调试模式
        """
        self.debug_mode = debug_mode
        self.debug_images = {}

        # 改进的参数设置
        self.gradient_threshold = 15.0 # 提高梯度阈值
        self.divergence_threshold = 0.8 # 提高发散度阈值
        self.laplacian_threshold = 20.0 # 新增：拉普拉斯阈值

        # 多尺度参数
        self.scales = [1.0, 1.5, 2.0] # 多尺度分析
        self.gaussian_sigma = 2.0 # 高斯加权标准差

        # 自适应聚类参数
        self.base_clustering_eps = 15
        self.base_clustering_min_samples = 3

        # 时序跟踪
        self.trackers: List[KalmanTracker] = []
        self.max_trackers = 5
        self.tracker_match_threshold = 30.0 # 像素距离阈值

        # 历史信息
        self.frame_count = 0
        self.leak_history = deque(maxlen=10)

        # 可视化参数
        self.leak_point_size = 8

    def analyze_gradient_and_flow(self,
                                 prev_frame: np.ndarray,
                                 curr_frame: np.ndarray,
                                 yolo_mask: np.ndarray,
                                 flow: np.ndarray,
                                 flow_magnitude: np.ndarray,
                                 flow_angle: np.ndarray) -> Dict[str, Any]:
        """
        改进的主要分析函数

        Args:
            prev_frame: 前一帧图像 (BGR格式)
            curr_frame: 当前帧图像 (BGR格式)
            yolo_mask: YOLOv8输出的气体mask区域
            flow: 光流向量场
            flow_magnitude: 光流幅度
            flow_angle: 光流角度

        Returns:
            包含改进分析结果的字典
        """
        try:
            self.frame_count += 1

            # 1. 多尺度特征提取
            multi_scale_features = self._extract_multi_scale_features(
                curr_frame, yolo_mask
            )

            # 2. 改进的发散度计算
            improved_divergence = self._compute_robust_divergence(
                flow, yolo_mask
            )

            # 3. 特征融合和候选点生成
            candidate_points = self._fuse_features_and_generate_candidates(
                multi_scale_features, improved_divergence, flow_magnitude, yolo_mask
            )

            # 4. 自适应聚类
            clustered_sources = self._adaptive_clustering(
                candidate_points, yolo_mask
            )

            # 5. 时序跟踪和稳定性分析
            tracked_sources = self._temporal_tracking_and_stabilization(
                clustered_sources
            )

            # 6. 生成改进的可视化
            overlay_frame = self._create_improved_visualization(
                curr_frame, flow, flow_magnitude, tracked_sources, yolo_mask
            )

            # 7. 更新历史记录
            self.leak_history.append(tracked_sources)

            result = {
                'leak_source_points': tracked_sources,
                'overlay_frame': overlay_frame,
                'multi_scale_features': multi_scale_features,
                'robust_divergence': improved_divergence,
                'candidate_points': candidate_points,
                'tracking_stability': self._get_tracking_stability_report()
            }

            if self.debug_mode:
                result['debug_images'] = self.debug_images.copy()

            return result

        except Exception as e:
            logger.error(f"改进版梯度分析失败: {e}")
            return {
                'leak_source_points': [],
                'overlay_frame': curr_frame.copy(),
                'error': str(e)
            }

    def _extract_multi_scale_features(self, frame: np.ndarray,
                                     mask: np.ndarray) -> Dict[str, np.ndarray]:
        """
        多尺度特征提取

        结合梯度、拉普拉斯和多尺度分析

        Args:
            frame: 输入图像
            mask: 气体检测mask

        Returns:
            多尺度特征字典
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        mask_binary = (mask > 127).astype(np.uint8)

        features = {
            'gradient_magnitude': np.zeros_like(gray, dtype=np.float32),
            'laplacian_response': np.zeros_like(gray, dtype=np.float32),
            'multi_scale_response': np.zeros_like(gray, dtype=np.float32)
        }

        # 多尺度分析
        for scale in self.scales:
            # 缩放图像
            if scale != 1.0:
                h, w = gray.shape
                new_h, new_w = int(h / scale), int(w / scale)
                scaled_gray = cv2.resize(gray, (new_w, new_h))
                scaled_mask = cv2.resize(mask_binary, (new_w, new_h))
            else:
                scaled_gray = gray
                scaled_mask = mask_binary

            # 高斯模糊
            kernel_size = int(3 * scale) * 2 + 1
            blurred = cv2.GaussianBlur(scaled_gray, (kernel_size, kernel_size), scale)

            # Sobel梯度
            grad_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
            gradient_mag = np.sqrt(grad_x**2 + grad_y**2)

            # 拉普拉斯算子（检测泄漏源中心）
            laplacian = cv2.Laplacian(blurred, cv2.CV_64F, ksize=3)
            laplacian_abs = np.abs(laplacian)

            # 应用mask
            gradient_mag *= scaled_mask
            laplacian_abs *= scaled_mask

            # 缩放回原尺寸
            if scale != 1.0:
                gradient_mag = cv2.resize(gradient_mag, (w, h))
                laplacian_abs = cv2.resize(laplacian_abs, (w, h))

            # 累积多尺度响应
            weight = 1.0 / scale # 小尺度权重更高
            features['gradient_magnitude'] += gradient_mag * weight
            features['laplacian_response'] += laplacian_abs * weight
            features['multi_scale_response'] += (gradient_mag + laplacian_abs) * weight

        # 归一化
        for key in features:
            features[key] = features[key] / len(self.scales)

        # 调试可视化
        if self.debug_mode:
            for key, feature in features.items():
                vis = cv2.normalize(feature, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
                self.debug_images[f'multi_scale_{key}'] = cv2.applyColorMap(vis, cv2.COLORMAP_JET)

        return features

    def _compute_robust_divergence(self, flow: np.ndarray,
                                  mask: np.ndarray) -> Dict[str, Any]:
        """
        鲁棒的发散度计算

        使用高斯加权的局部发散度，减少噪声影响

        Args:
            flow: 光流向量场
            mask: 气体检测mask

        Returns:
            改进的发散度信息
        """
        h, w = flow.shape[:2]
        mask_binary = (mask > 127).astype(np.uint8)

        # 提取光流分量
        u = flow[:, :, 0]
        v = flow[:, :, 1]

        # 使用更稳定的差分方法
        # Scharr算子比简单差分更鲁棒
        du_dx = cv2.Scharr(u, cv2.CV_64F, 1, 0) / 8.0
        dv_dy = cv2.Scharr(v, cv2.CV_64F, 0, 1) / 8.0

        # 计算发散度
        divergence = du_dx + dv_dy

        # 高斯加权平滑
        kernel_size = int(6 * self.gaussian_sigma + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        divergence_smooth = cv2.GaussianBlur(
            divergence, (kernel_size, kernel_size), self.gaussian_sigma
        )

        # 应用mask
        divergence_masked = divergence_smooth * mask_binary

        # 寻找鲁棒的候选点
        candidate_points = []

        # 使用更严格的阈值
        high_div_mask = (divergence_masked > self.divergence_threshold) & (mask_binary > 0)

        if np.any(high_div_mask):
            y_coords, x_coords = np.where(high_div_mask)

            for i in range(len(x_coords)):
                x, y = int(x_coords[i]), int(y_coords[i])
                div_value = float(divergence_masked[y, x])

                # 计算局部一致性（周围8邻域的发散度一致性）
                local_consistency = self._compute_local_consistency(
                    divergence_masked, x, y, radius=3
                )

                candidate_points.append({
                    'x': x,
                    'y': y,
                    'divergence': div_value,
                    'local_consistency': local_consistency
                })

        # 调试可视化
        if self.debug_mode:
            div_vis = cv2.normalize(
                np.abs(divergence_masked), None, 0, 255, cv2.NORM_MINMAX
            ).astype(np.uint8)
            self.debug_images['robust_divergence'] = cv2.applyColorMap(div_vis, cv2.COLORMAP_JET)

        return {
            'divergence_map': divergence_masked,
            'candidate_points': candidate_points,
            'du_dx': du_dx,
            'dv_dy': dv_dy
        }

    def _compute_local_consistency(self, divergence_map: np.ndarray,
                                  x: int, y: int, radius: int = 3) -> float:
        """
        计算局部发散度一致性

        Args:
            divergence_map: 发散度图
            x, y: 中心点坐标
            radius: 邻域半径

        Returns:
            局部一致性分数 (0-1)
        """
        h, w = divergence_map.shape

        # 提取邻域
        y_min = max(0, y - radius)
        y_max = min(h, y + radius + 1)
        x_min = max(0, x - radius)
        x_max = min(w, x + radius + 1)

        neighborhood = divergence_map[y_min:y_max, x_min:x_max]

        if neighborhood.size == 0:
            return 0.0

        # 计算一致性（标准差的倒数）
        std_dev = np.std(neighborhood)
        consistency = 1.0 / (1.0 + std_dev)

        return float(consistency)

    # TODO: 实现特征融合和候选点生成
    def _fuse_features_and_generate_candidates(self,
                                              multi_scale_features: Dict[str, np.ndarray],
                                              divergence_result: Dict[str, Any],
                                              flow_magnitude: np.ndarray,
                                              mask: np.ndarray) -> List[Dict[str, Any]]:
        """
        融合多种特征生成候选点

        结合梯度、拉普拉斯、发散度和光流幅度
        """
        try:
            candidates = []
            h, w = mask.shape[:2]
            
            # 获取各种特征图
            gradient_mag = multi_scale_features.get('gradient_magnitude', np.zeros((h, w)))
            laplacian = multi_scale_features.get('laplacian', np.zeros((h, w)))
            divergence_map = divergence_result.get('divergence_map', np.zeros((h, w)))
            
            # 归一化特征图到[0,1]范围
            gradient_mag_norm = cv2.normalize(gradient_mag, None, 0, 1, cv2.NORM_MINMAX)
            laplacian_norm = cv2.normalize(np.abs(laplacian), None, 0, 1, cv2.NORM_MINMAX)
            divergence_norm = cv2.normalize(np.abs(divergence_map), None, 0, 1, cv2.NORM_MINMAX)
            flow_mag_norm = cv2.normalize(flow_magnitude, None, 0, 1, cv2.NORM_MINMAX)
            
            # 特征融合权重
            w_gradient = 0.3
            w_laplacian = 0.2
            w_divergence = 0.4
            w_flow = 0.1
            
            # 融合特征图
            fused_features = (
                w_gradient * gradient_mag_norm +
                w_laplacian * laplacian_norm +
                w_divergence * divergence_norm +
                w_flow * flow_mag_norm
            )
            
            # 应用mask
            fused_features = fused_features * (mask > 0)
            
            # 自适应阈值
            threshold = np.percentile(fused_features[mask > 0], 85) if np.any(mask > 0) else 0.5
            
            # 寻找候选点
            candidate_mask = fused_features > threshold
            
            # 使用形态学操作去除噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            candidate_mask = cv2.morphologyEx(candidate_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel)
            
            # 寻找连通区域
            contours, _ = cv2.findContours(candidate_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                if cv2.contourArea(contour) < 5:  # 过滤太小的区域
                    continue
                    
                # 计算质心
                M = cv2.moments(contour)
                if M['m00'] > 0:
                    cx = int(M['m10'] / M['m00'])
                    cy = int(M['m01'] / M['m00'])
                    
                    # 确保在图像范围内
                    if 0 <= cx < w and 0 <= cy < h:
                        # 计算该点的综合置信度
                        confidence = float(fused_features[cy, cx])
                        
                        candidates.append({
                            'x': cx,
                            'y': cy,
                            'confidence': confidence,
                            'gradient_strength': float(gradient_mag_norm[cy, cx]),
                            'divergence_strength': float(divergence_norm[cy, cx]),
                            'flow_magnitude': float(flow_mag_norm[cy, cx]),
                            'area': cv2.contourArea(contour)
                        })
            
            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence'], reverse=True)
            
            # 限制候选点数量
            max_candidates = 20
            candidates = candidates[:max_candidates]
            
            if self.debug_mode:
                debug_img = cv2.applyColorMap((fused_features * 255).astype(np.uint8), cv2.COLORMAP_JET)
                self.debug_images['fused_features'] = debug_img
                
            return candidates
            
        except Exception as e:
            logger.error(f"特征融合失败: {e}")
            return []

    def _adaptive_clustering(self, candidate_points: List[Dict[str, Any]],
                           mask: np.ndarray) -> List[Dict[str, Any]]:
        """
        基于mask区域大小自适应调整聚类参数
        """
        if not candidate_points:
            return []
            
        try:
            # 计算mask区域大小
            mask_area = np.sum(mask > 0)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area if total_area > 0 else 0
            
            # 根据mask大小自适应调整聚类参数
            if mask_ratio < 0.01:  # 很小的区域
                eps = 5
                min_samples = 1
            elif mask_ratio < 0.05:  # 小区域
                eps = 8
                min_samples = 2
            elif mask_ratio < 0.2:  # 中等区域
                eps = 12
                min_samples = 2
            else:  # 大区域
                eps = 15
                min_samples = 3
                
            # 提取候选点坐标
            points = np.array([[p['x'], p['y']] for p in candidate_points])
            
            if len(points) < min_samples:
                # 如果候选点太少，直接返回所有点作为独立的源
                clustered_sources = []
                for i, point in enumerate(candidate_points):
                    clustered_sources.append({
                        'x': point['x'],
                        'y': point['y'],
                        'confidence': point['confidence'],
                        'cluster_id': i,
                        'cluster_size': 1,
                        'gradient_strength': point.get('gradient_strength', 0),
                        'divergence_strength': point.get('divergence_strength', 0),
                        'flow_magnitude': point.get('flow_magnitude', 0)
                    })
                return clustered_sources
            
            # 执行DBSCAN聚类
            clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
            labels = clustering.labels_
            
            # 处理聚类结果
            clustered_sources = []
            unique_labels = set(labels)
            
            for label in unique_labels:
                if label == -1:  # 噪声点
                    # 将噪声点作为独立的源
                    noise_indices = np.where(labels == label)[0]
                    for idx in noise_indices:
                        point = candidate_points[idx]
                        clustered_sources.append({
                            'x': point['x'],
                            'y': point['y'],
                            'confidence': point['confidence'],
                            'cluster_id': -1,
                            'cluster_size': 1,
                            'gradient_strength': point.get('gradient_strength', 0),
                            'divergence_strength': point.get('divergence_strength', 0),
                            'flow_magnitude': point.get('flow_magnitude', 0)
                        })
                else:
                    # 计算聚类中心
                    cluster_indices = np.where(labels == label)[0]
                    cluster_points = [candidate_points[i] for i in cluster_indices]
                    
                    # 使用加权平均计算聚类中心
                    total_weight = sum(p['confidence'] for p in cluster_points)
                    if total_weight > 0:
                        weighted_x = sum(p['x'] * p['confidence'] for p in cluster_points) / total_weight
                        weighted_y = sum(p['y'] * p['confidence'] for p in cluster_points) / total_weight
                        avg_confidence = sum(p['confidence'] for p in cluster_points) / len(cluster_points)
                        avg_gradient = sum(p.get('gradient_strength', 0) for p in cluster_points) / len(cluster_points)
                        avg_divergence = sum(p.get('divergence_strength', 0) for p in cluster_points) / len(cluster_points)
                        avg_flow = sum(p.get('flow_magnitude', 0) for p in cluster_points) / len(cluster_points)
                        
                        clustered_sources.append({
                            'x': int(weighted_x),
                            'y': int(weighted_y),
                            'confidence': avg_confidence,
                            'cluster_id': int(label),
                            'cluster_size': len(cluster_points),
                            'gradient_strength': avg_gradient,
                            'divergence_strength': avg_divergence,
                            'flow_magnitude': avg_flow
                        })
            
            # 按置信度排序
            clustered_sources.sort(key=lambda x: x['confidence'], reverse=True)
            
            if self.debug_mode:
                logger.info(f"聚类完成: {len(candidate_points)}个候选点 -> {len(clustered_sources)}个聚类源")
                logger.info(f"聚类参数: eps={eps}, min_samples={min_samples}, mask_ratio={mask_ratio:.3f}")
            
            return clustered_sources
            
        except Exception as e:
            logger.error(f"自适应聚类失败: {e}")
            # 失败时返回原始候选点
            return [{
                'x': p['x'],
                'y': p['y'],
                'confidence': p['confidence'],
                'cluster_id': i,
                'cluster_size': 1,
                'gradient_strength': p.get('gradient_strength', 0),
                'divergence_strength': p.get('divergence_strength', 0),
                'flow_magnitude': p.get('flow_magnitude', 0)
            } for i, p in enumerate(candidate_points)]

    def _temporal_tracking_and_stabilization(self,
                                            sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用卡尔曼滤波进行时序跟踪和稳定化
        """
        if not sources:
            return []
            
        try:
            tracked_sources = []
            
            # 如果没有现有跟踪器，为每个源创建新的跟踪器
            if not self.trackers:
                for i, source in enumerate(sources):
                    tracker = KalmanTracker(source['x'], source['y'])
                    tracker.tracker_id = i
                    tracker.last_update_frame = self.frame_count
                    self.trackers.append(tracker)
                    
                    tracked_sources.append({
                        'x': source['x'],
                        'y': source['y'],
                        'confidence': source['confidence'],
                        'tracker_id': tracker.tracker_id,
                        'stability_score': tracker.stability_score,
                        'prediction_error': 0.0,
                        'cluster_id': source.get('cluster_id', -1),
                        'cluster_size': source.get('cluster_size', 1),
                        'gradient_strength': source.get('gradient_strength', 0),
                        'divergence_strength': source.get('divergence_strength', 0),
                        'flow_magnitude': source.get('flow_magnitude', 0)
                    })
                return tracked_sources
            
            # 计算距离矩阵进行数据关联
            distance_threshold = 30.0  # 像素距离阈值
            
            # 预测所有跟踪器的位置
            predictions = []
            for tracker in self.trackers:
                pred_x, pred_y = tracker.predict()
                predictions.append((pred_x, pred_y))
            
            # 匈牙利算法进行最优匹配
            matched_pairs = []
            unmatched_sources = list(range(len(sources)))
            unmatched_trackers = list(range(len(self.trackers)))
            
            # 简化的贪心匹配算法
            for src_idx, source in enumerate(sources):
                best_tracker_idx = -1
                min_distance = float('inf')
                
                for tracker_idx in unmatched_trackers:
                    pred_x, pred_y = predictions[tracker_idx]
                    distance = np.sqrt((source['x'] - pred_x)**2 + (source['y'] - pred_y)**2)
                    
                    if distance < distance_threshold and distance < min_distance:
                        min_distance = distance
                        best_tracker_idx = tracker_idx
                
                if best_tracker_idx != -1:
                    matched_pairs.append((src_idx, best_tracker_idx))
                    unmatched_sources.remove(src_idx)
                    unmatched_trackers.remove(best_tracker_idx)
            
            # 更新匹配的跟踪器
            for src_idx, tracker_idx in matched_pairs:
                source = sources[src_idx]
                tracker = self.trackers[tracker_idx]
                
                # 更新跟踪器
                updated_x, updated_y = tracker.update(source['x'], source['y'])
                tracker.last_update_frame = self.frame_count
                
                # 计算预测误差
                pred_x, pred_y = predictions[tracker_idx]
                prediction_error = np.sqrt((source['x'] - pred_x)**2 + (source['y'] - pred_y)**2)
                
                tracked_sources.append({
                    'x': int(updated_x),
                    'y': int(updated_y),
                    'confidence': source['confidence'],
                    'tracker_id': tracker.tracker_id,
                    'stability_score': tracker.stability_score,
                    'prediction_error': prediction_error,
                    'cluster_id': source.get('cluster_id', -1),
                    'cluster_size': source.get('cluster_size', 1),
                    'gradient_strength': source.get('gradient_strength', 0),
                    'divergence_strength': source.get('divergence_strength', 0),
                    'flow_magnitude': source.get('flow_magnitude', 0)
                })
            
            # 为未匹配的源创建新跟踪器
            for src_idx in unmatched_sources:
                source = sources[src_idx]
                tracker = KalmanTracker(source['x'], source['y'])
                tracker.tracker_id = len(self.trackers)
                tracker.last_update_frame = self.frame_count
                self.trackers.append(tracker)
                
                tracked_sources.append({
                    'x': source['x'],
                    'y': source['y'],
                    'confidence': source['confidence'],
                    'tracker_id': tracker.tracker_id,
                    'stability_score': tracker.stability_score,
                    'prediction_error': 0.0,
                    'cluster_id': source.get('cluster_id', -1),
                    'cluster_size': source.get('cluster_size', 1),
                    'gradient_strength': source.get('gradient_strength', 0),
                    'divergence_strength': source.get('divergence_strength', 0),
                    'flow_magnitude': source.get('flow_magnitude', 0)
                })
            
            # 移除长时间未更新的跟踪器
            max_frames_without_update = 10
            self.trackers = [t for t in self.trackers 
                           if self.frame_count - t.last_update_frame <= max_frames_without_update]
            
            # 按稳定性和置信度排序
            tracked_sources.sort(key=lambda x: (x['stability_score'], x['confidence']), reverse=True)
            
            if self.debug_mode:
                logger.info(f"时序跟踪: {len(sources)}个源 -> {len(tracked_sources)}个跟踪源")
                logger.info(f"活跃跟踪器数量: {len(self.trackers)}")
                avg_stability = np.mean([s['stability_score'] for s in tracked_sources]) if tracked_sources else 0
                logger.info(f"平均稳定性: {avg_stability:.3f}")
            
            return tracked_sources
            
        except Exception as e:
            logger.error(f"时序跟踪失败: {e}")
            # 失败时返回原始源点
            return [{
                'x': s['x'],
                'y': s['y'],
                'confidence': s['confidence'],
                'tracker_id': -1,
                'stability_score': 0.0,
                'prediction_error': 0.0,
                'cluster_id': s.get('cluster_id', -1),
                'cluster_size': s.get('cluster_size', 1),
                'gradient_strength': s.get('gradient_strength', 0),
                'divergence_strength': s.get('divergence_strength', 0),
                'flow_magnitude': s.get('flow_magnitude', 0)
            } for s in sources]

    def _create_improved_visualization(self,
                                     frame: np.ndarray,
                                     flow: np.ndarray,
                                     flow_magnitude: np.ndarray,
                                     sources: List[Dict[str, Any]],
                                     mask: np.ndarray) -> np.ndarray:
        """
        创建包含稳定性信息的改进可视化
        """
        try:
            overlay = frame.copy()
            h, w = frame.shape[:2]
            
            # 1. 绘制光流可视化（背景）
            if flow is not None and flow_magnitude is not None:
                # 创建光流可视化
                flow_vis = np.zeros((h, w, 3), dtype=np.uint8)
                
                # 使用HSV颜色空间表示光流方向和强度
                hsv = np.zeros((h, w, 3), dtype=np.uint8)
                hsv[..., 1] = 255  # 饱和度设为最大
                
                # 计算光流角度和强度
                magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
                angle = np.arctan2(flow[..., 1], flow[..., 0])
                
                # 角度映射到色调
                hsv[..., 0] = (angle * 180 / np.pi / 2).astype(np.uint8)
                # 强度映射到亮度
                hsv[..., 2] = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
                
                # 转换为BGR
                flow_bgr = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
                
                # 只在mask区域显示光流
                mask_3ch = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) if len(mask.shape) == 2 else mask
                flow_vis = cv2.bitwise_and(flow_bgr, mask_3ch)
                
                # 与原图融合
                overlay = cv2.addWeighted(overlay, 0.7, flow_vis, 0.3, 0)
            
            # 2. 绘制mask轮廓
            if mask is not None:
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                cv2.drawContours(overlay, contours, -1, (0, 255, 255), 2)  # 黄色轮廓
            
            # 3. 绘制泄漏源点
            for i, source in enumerate(sources):
                x, y = source['x'], source['y']
                confidence = source['confidence']
                stability = source.get('stability_score', 0.0)
                tracker_id = source.get('tracker_id', -1)
                prediction_error = source.get('prediction_error', 0.0)
                
                # 根据稳定性选择颜色
                if stability > 0.8:
                    color = (0, 255, 0)  # 绿色 - 高稳定性
                elif stability > 0.5:
                    color = (0, 165, 255)  # 橙色 - 中等稳定性
                else:
                    color = (0, 0, 255)  # 红色 - 低稳定性
                
                # 根据置信度调整圆圈大小
                radius = max(5, int(10 + confidence * 10))
                
                # 绘制主圆圈
                cv2.circle(overlay, (x, y), radius, color, 2)
                
                # 绘制中心点
                cv2.circle(overlay, (x, y), 2, color, -1)
                
                # 绘制稳定性指示器（外圈）
                if stability > 0.3:
                    stability_radius = radius + 5
                    cv2.circle(overlay, (x, y), stability_radius, color, 1)
                
                # 添加文本信息
                text_y = y - radius - 10
                
                # 显示跟踪器ID和置信度
                if tracker_id >= 0:
                    text = f"T{tracker_id}: {confidence:.2f}"
                else:
                    text = f"New: {confidence:.2f}"
                
                cv2.putText(overlay, text, (x - 30, text_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                
                # 显示稳定性分数
                if stability > 0:
                    stability_text = f"S:{stability:.2f}"
                    cv2.putText(overlay, stability_text, (x - 25, text_y - 15), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
                
                # 显示预测误差（如果有）
                if prediction_error > 0:
                    error_text = f"E:{prediction_error:.1f}"
                    cv2.putText(overlay, error_text, (x - 20, text_y - 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
                
                # 绘制序号
                cv2.putText(overlay, str(i + 1), (x + radius + 5, y + 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 4. 添加图例和统计信息
            legend_y = 30
            cv2.putText(overlay, "Leak Source Tracking", (10, legend_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            legend_y += 25
            cv2.putText(overlay, "Green: High Stability", (10, legend_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            legend_y += 20
            cv2.putText(overlay, "Orange: Medium Stability", (10, legend_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 165, 255), 1)
            
            legend_y += 20
            cv2.putText(overlay, "Red: Low Stability", (10, legend_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
            
            # 显示统计信息
            stats_y = h - 60
            cv2.putText(overlay, f"Sources: {len(sources)}", (10, stats_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            stats_y += 20
            active_trackers = len([s for s in sources if s.get('tracker_id', -1) >= 0])
            cv2.putText(overlay, f"Active Trackers: {active_trackers}", (10, stats_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            stats_y += 20
            if sources:
                avg_stability = np.mean([s.get('stability_score', 0) for s in sources])
                cv2.putText(overlay, f"Avg Stability: {avg_stability:.2f}", (10, stats_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 5. 绘制跟踪轨迹（如果有历史数据）
            if len(self.leak_history) > 1:
                self._draw_tracking_trails(overlay, sources)
            
            return overlay
            
        except Exception as e:
            logger.error(f"可视化创建失败: {e}")
            return frame.copy()
    
    def _draw_tracking_trails(self, overlay: np.ndarray, current_sources: List[Dict[str, Any]]):
        """
        绘制跟踪轨迹
        """
        try:
            # 为每个当前源绘制历史轨迹
            for source in current_sources:
                tracker_id = source.get('tracker_id', -1)
                if tracker_id < 0:
                    continue
                
                # 收集该跟踪器的历史位置
                trail_points = []
                # 安全地获取最近10帧的历史记录
                recent_history = list(self.leak_history)[-10:] if len(self.leak_history) > 0 else []
                for hist_sources in recent_history:
                    for hist_source in hist_sources:
                        if hist_source.get('tracker_id') == tracker_id:
                            trail_points.append((hist_source['x'], hist_source['y']))
                            break
                
                # 绘制轨迹线
                if len(trail_points) > 1:
                    points = np.array(trail_points, dtype=np.int32)
                    cv2.polylines(overlay, [points], False, (255, 255, 0), 1)  # 青色轨迹
                    
        except Exception as e:
            import traceback
            logger.error(f"轨迹绘制失败: {e}\n{traceback.format_exc()}")
            logger.error(f"轨迹绘制失败: {e}")

    def _get_tracking_stability_report(self) -> Dict[str, Any]:
        """
        获取跟踪稳定性报告

        Returns:
            包含稳定性指标的字典
        """
        if not self.trackers:
            return {'average_stability': 0.0, 'active_trackers': 0}

        stabilities = [tracker.stability_score for tracker in self.trackers]

        return {
            'average_stability': float(np.mean(stabilities)),
            'active_trackers': len(self.trackers),
            'individual_stabilities': stabilities
        }


def enhance_optical_flow_with_improved_gradient_analysis(prev_frame: np.ndarray,
                                                       curr_frame: np.ndarray,
                                                       yolo_mask: np.ndarray,
                                                       existing_flow_result: Dict[str, Any],
                                                       debug_mode: bool = False) -> Dict[str, Any]:
    """
    改进版光流分析的梯度增强扩展函数
    
    这是与原有enhance_optical_flow_with_gradient_analysis兼容的接口函数，
    使用改进版的ImprovedGradientLeakageLocalizer实现。
    
    Args:
        prev_frame: 前一帧图像 (BGR格式)
        curr_frame: 当前帧图像 (BGR格式)
        yolo_mask: YOLOv8输出的气体mask区域 (uint8, 255为mask)
        existing_flow_result: 现有光流分析结果，包含'image'和'velocity'键
        debug_mode: 是否启用调试模式
        
    Returns:
        增强后的分析结果字典，包含改进的跟踪和稳定性信息
    """
    try:
        # 创建改进版定位器实例
        localizer = ImprovedGradientLeakageLocalizer(debug_mode=debug_mode)
        
        # 从现有结果中提取光流数据
        flow = existing_flow_result.get('velocity', np.zeros((curr_frame.shape[0], curr_frame.shape[1], 2)))
        flow_magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
        flow_angle = np.arctan2(flow[..., 1], flow[..., 0])
        
        # 执行改进版分析
        improved_result = localizer.analyze_gradient_and_flow(
            prev_frame, curr_frame, yolo_mask, flow, flow_magnitude, flow_angle
        )
        
        # 合并结果，保持与原接口的兼容性
        result = existing_flow_result.copy()
        result.update({
            'enhanced_analysis': improved_result,
            'leak_source_points': improved_result.get('leak_source_points', []),
            'gradient_enhanced_overlay': improved_result.get('overlay_frame'),
            'tracking_stability': improved_result.get('tracking_stability', {}),
            'multi_scale_features': improved_result.get('multi_scale_features', {}),
            'robust_divergence': improved_result.get('robust_divergence', {})
        })
        
        # 如果有改进的可视化，使用它替换原始图像
        if improved_result.get('overlay_frame') is not None:
            result['image'] = improved_result['overlay_frame']
            
        return result
        
    except Exception as e:
        logger.error(f"改进版梯度分析失败: {e}")
        # 失败时返回原始结果
        return existing_flow_result


# 使用示例
if __name__ == "__main__":
    """
    改进版使用示例

    主要改进点：
    1. 更稳定的泄漏源定位
    2. 时序一致性保证
    3. 自适应参数调整
    4. 更好的噪声抑制
    """

    # 创建改进版定位器
    improved_localizer = ImprovedGradientLeakageLocalizer(debug_mode=True)

    # 在实际使用中替换原有的GradientEnhancedLeakageLocalizer
    print("改进版梯度增强泄漏源定位器已准备就绪")
    print("主要改进：时序跟踪、多尺度特征、自适应参数、鲁棒计算")
    print("兼容接口函数: enhance_optical_flow_with_improved_gradient_analysis")