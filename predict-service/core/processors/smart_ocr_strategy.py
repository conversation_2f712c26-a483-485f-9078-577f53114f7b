#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import configparser
import os
from typing import Dict, Optional, Any
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class OCRStrategyConfig:
    """OCR策略配置"""
    # 早期检测阶段配置
    early_detection_frames: int = 30 # 前30帧进行早期检测
    early_detection_interval: int = 10 # 每10帧进行一次OCR
    early_detection_attempts: int = 3 # 前30帧总共尝试3次

    # 成功后的策略配置
    success_interval: int = 10 # OCR成功后每10帧执行一次

    # 失败后的策略配置
    failure_skip_threshold: int = 3 # 连续失败3次后跳过后续OCR
    failure_retry_interval: int = 100 # 跳过后每100帧重试一次（可选）

    # 全局配置
    enable_failure_retry: bool = False # 是否在失败后偶尔重试


class SmartOCRStrategy:
    """
    智能OCR策略管理器

    策略说明：
    1. 前30帧每10帧进行一次OCR识别（总共3次尝试）
    2. 如果3次都失败，则跳过后续所有OCR处理
    3. 如果任意一次成功，则继续按正常间隔进行OCR
    """

    def __init__(self, config: Optional[OCRStrategyConfig] = None):
        self.config = config or OCRStrategyConfig()

        # 状态跟踪
        self.early_detection_attempts = 0 # 早期检测尝试次数
        self.early_detection_success = False # 早期检测是否成功
        self.ocr_permanently_disabled = False # OCR是否被永久禁用
        self.first_success_frame = None # 首次成功的帧数

        # 统计信息
        self.total_ocr_attempts = 0
        self.total_ocr_successes = 0
        self.total_frames_processed = 0

        logger.info(f"智能OCR策略初始化完成")
        logger.info(f" ├─ 早期检测帧数: {self.config.early_detection_frames}")
        logger.info(f" ├─ 早期检测间隔: {self.config.early_detection_interval}")
        logger.info(f" ├─ 早期检测尝试次数: {self.config.early_detection_attempts}")
        logger.info(f" └─ 失败后重试: {'启用' if self.config.enable_failure_retry else '禁用'}")

    def should_perform_ocr(self, frame_number: int) -> bool:
        """
        判断当前帧是否应该执行OCR

        Args:
            frame_number: 当前帧数（从0开始）

        Returns:
            bool: 是否应该执行OCR
        """
        self.total_frames_processed += 1

        # 如果OCR已被永久禁用，直接返回False
        if self.ocr_permanently_disabled:
            # 可选：偶尔重试机制
            if self.config.enable_failure_retry and frame_number % self.config.failure_retry_interval == 0:
                logger.debug(f"帧 {frame_number}: OCR重试机制触发")
                return True
            return False

        # 早期检测阶段（前30帧）
        if frame_number < self.config.early_detection_frames:
            # 检查是否到了执行OCR的时机
            if frame_number % self.config.early_detection_interval == 0:
                # 检查是否还有剩余尝试次数
                if self.early_detection_attempts < self.config.early_detection_attempts:
                    logger.debug(f"帧 {frame_number}: 早期检测阶段OCR (尝试 {self.early_detection_attempts + 1}/{self.config.early_detection_attempts})")
                    return True
                else:
                    logger.debug(f"帧 {frame_number}: 早期检测已达到最大尝试次数")
                    return False
            return False

        # 早期检测阶段结束后的处理
        if frame_number == self.config.early_detection_frames:
            # 检查早期检测是否成功
            if not self.early_detection_success:
                logger.warning(f" 早期OCR检测失败 - 前{self.config.early_detection_frames}帧内{self.early_detection_attempts}次尝试均无成功")
                logger.warning(f" OCR功能已禁用，后续帧将跳过OCR处理以节省计算资源")
                self.ocr_permanently_disabled = True
                return False
            else:
                logger.info(f" 早期OCR检测成功 - 继续按正常策略进行OCR")

        # 正常阶段：早期检测成功后的处理
        if self.early_detection_success and not self.ocr_permanently_disabled:
            # 按配置间隔执行OCR
            should_execute = frame_number % self.config.success_interval == 0
            if should_execute:
                logger.debug(f"帧 {frame_number}: 正常阶段OCR")
            return should_execute

        return False

    def record_ocr_result(self, frame_number: int, ocr_result: Dict[str, Any]) -> None:
        """
        记录OCR结果并更新策略状态

        Args:
            frame_number: 当前帧数
            ocr_result: OCR识别结果
        """
        self.total_ocr_attempts += 1

        # 判断OCR是否成功（至少有一个字段有内容）
        is_success = bool(ocr_result and any(
            value is not None and str(value).strip()
            for value in ocr_result.values()
        ))

        if is_success:
            self.total_ocr_successes += 1

            # 如果在早期检测阶段成功
            if frame_number < self.config.early_detection_frames:
                if not self.early_detection_success:
                    self.early_detection_success = True
                    self.first_success_frame = frame_number
                    logger.info(f" 首次OCR成功！帧数: {frame_number}, 尝试次数: {self.early_detection_attempts + 1}")
                    logger.info(f" 识别内容: {ocr_result}")

        # 在早期检测阶段，更新尝试次数
        if frame_number < self.config.early_detection_frames:
            # 只有实际执行了OCR才计入尝试次数
            if frame_number % self.config.early_detection_interval == 0:
                self.early_detection_attempts += 1

                logger.debug(f"早期检测进度: {self.early_detection_attempts}/{self.config.early_detection_attempts}, "
                           f"成功: {is_success}, 帧: {frame_number}")

    def get_strategy_status(self) -> Dict[str, Any]:
        """
        获取策略状态信息

        Returns:
            dict: 包含策略状态的字典
        """
        return {
            "ocr_permanently_disabled": self.ocr_permanently_disabled,
            "early_detection_success": self.early_detection_success,
            "early_detection_attempts": self.early_detection_attempts,
            "first_success_frame": self.first_success_frame,
            "total_ocr_attempts": self.total_ocr_attempts,
            "total_ocr_successes": self.total_ocr_successes,
            "total_frames_processed": self.total_frames_processed,
            "ocr_success_rate": (
                self.total_ocr_successes / self.total_ocr_attempts * 100
                if self.total_ocr_attempts > 0 else 0
            ),
            "performance_optimization": {
                "frames_saved_from_ocr": self._calculate_saved_frames(),
                "estimated_time_saved_seconds": self._estimate_time_saved(),
            }
        }

    def _calculate_saved_frames(self) -> int:
        """计算节省的OCR处理帧数"""
        if not self.ocr_permanently_disabled:
            return 0

        # 计算如果按原策略（每10帧一次）会处理多少帧
        frames_after_early_detection = max(0, self.total_frames_processed - self.config.early_detection_frames)
        would_have_processed = frames_after_early_detection // 10
        actually_processed = 0 # 禁用后实际处理的帧数

        return would_have_processed - actually_processed

    def _estimate_time_saved(self) -> float:
        """估算节省的时间（秒）"""
        saved_frames = self._calculate_saved_frames()
        # 假设每次OCR耗时约0.1秒
        avg_ocr_time_per_frame = 0.1
        return saved_frames * avg_ocr_time_per_frame

    def get_summary_report(self) -> str:
        """
        生成策略执行摘要报告

        Returns:
            str: 格式化的报告文本
        """
        status = self.get_strategy_status()

        report = f"""
 智能OCR策略执行报告
{'=' * 50}
 策略状态:
   ├─ OCR状态: {' 已禁用' if status['ocr_permanently_disabled'] else ' 正常'}
   ├─ 早期检测: {' 成功' if status['early_detection_success'] else ' 失败'}
   ├─ 尝试次数: {status['early_detection_attempts']}/{self.config.early_detection_attempts}
   └─ 首次成功帧: {status['first_success_frame'] or 'N/A'}

 处理统计:
   ├─ 总处理帧数: {status['total_frames_processed']}
   ├─ OCR尝试次数: {status['total_ocr_attempts']}
   ├─ OCR成功次数: {status['total_ocr_successes']}
   └─ OCR成功率: {status['ocr_success_rate']:.1f}%

 性能优化:
   ├─ 节省OCR帧数: {status['performance_optimization']['frames_saved_from_ocr']}
   └─ 预估节省时间: {status['performance_optimization']['estimated_time_saved_seconds']:.1f}秒
"""
        return report.strip()


# 从配置文件加载OCR策略配置
def load_ocr_config_from_file(config_file_path: Optional[str] = None) -> OCRStrategyConfig:
    """
    从配置文件加载OCR策略配置

    Args:
        config_file_path: 配置文件路径，如果为None则自动检测

    Returns:
        OCRStrategyConfig: 配置对象
    """
    # 默认配置
    default_config = OCRStrategyConfig()

    if config_file_path is None:
        # 自动检测配置文件
        possible_paths = [
            os.path.join(os.path.dirname(__file__), '../../config/config-dev.ini'),
            os.path.join(os.path.dirname(__file__), '../../config/config-prod.ini'),
            os.path.join(os.path.dirname(__file__), '../../config/config.ini'),
            './config/config-dev.ini',
            './config/config-prod.ini',
            './config/config.ini'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                config_file_path = path
                break

    if not config_file_path or not os.path.exists(config_file_path):
        logger.warning(f"配置文件未找到，使用默认OCR策略配置")
        return default_config

    try:
        config = configparser.ConfigParser()
        config.read(config_file_path, encoding='utf-8')

        if 'smart_ocr' not in config:
            logger.warning(f"配置文件中未找到[smart_ocr]段落，使用默认配置")
            return default_config

        ocr_section = config['smart_ocr']

        # 读取配置参数
        early_detection_frames = ocr_section.getint('early_detection_frames', default_config.early_detection_frames)
        early_detection_interval = ocr_section.getint('early_detection_interval', default_config.early_detection_interval)
        success_interval = ocr_section.getint('success_interval', default_config.success_interval)
        enable_failure_retry = ocr_section.getboolean('enable_failure_retry', default_config.enable_failure_retry)
        failure_retry_interval = ocr_section.getint('failure_retry_interval', default_config.failure_retry_interval)

        # 计算尝试次数
        early_detection_attempts = early_detection_frames // early_detection_interval

        loaded_config = OCRStrategyConfig(
            early_detection_frames=early_detection_frames,
            early_detection_interval=early_detection_interval,
            early_detection_attempts=early_detection_attempts,
            success_interval=success_interval,
            failure_retry_interval=failure_retry_interval,
            enable_failure_retry=enable_failure_retry
        )

        logger.info(f" 从配置文件加载OCR策略配置: {config_file_path}")
        logger.info(f" ├─ 早期检测帧数: {early_detection_frames}")
        logger.info(f" ├─ 早期检测间隔: {early_detection_interval}")
        logger.info(f" ├─ 成功后间隔: {success_interval}")
        logger.info(f" └─ 失败重试: {'启用' if enable_failure_retry else '禁用'}")

        return loaded_config

    except Exception as e:
        logger.error(f"读取配置文件失败: {e}，使用默认配置")
        return default_config


# 创建全局策略实例的工厂函数
def create_smart_ocr_strategy(
    config_file_path: Optional[str] = None,
    early_detection_frames: Optional[int] = None,
    early_detection_interval: Optional[int] = None,
    enable_failure_retry: Optional[bool] = None
) -> SmartOCRStrategy:
    """
    创建智能OCR策略实例

    Args:
        config_file_path: 配置文件路径，如果提供则从文件加载配置
        early_detection_frames: 早期检测的帧数范围（覆盖配置文件）
        early_detection_interval: 早期检测的间隔（覆盖配置文件）
        enable_failure_retry: 是否启用失败后重试（覆盖配置文件）

    Returns:
        SmartOCRStrategy: 策略实例
    """
    # 从配置文件加载基础配置
    config = load_ocr_config_from_file(config_file_path)

    # 使用提供的参数覆盖配置文件设置
    if early_detection_frames is not None:
        config.early_detection_frames = early_detection_frames
        config.early_detection_attempts = early_detection_frames // config.early_detection_interval

    if early_detection_interval is not None:
        config.early_detection_interval = early_detection_interval
        config.early_detection_attempts = config.early_detection_frames // early_detection_interval

    if enable_failure_retry is not None:
        config.enable_failure_retry = enable_failure_retry

    return SmartOCRStrategy(config)