#!/usr/bin/env python
# -*- coding: utf-8 -*-

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass, field
from collections import defaultdict, deque
import time
import math

logger = logging.getLogger(__name__)

@dataclass
class TrackedObject:
    """追踪对象的数据结构"""
    id: int
    class_id: int
    class_name: str
    bbox: List[float] # [x1, y1, x2, y2]
    confidence: float
    center: Tuple[float, float] # (x, y)
    world_position: Optional[Tuple[float, float]] = None # 世界坐标系位置
    velocity: Tuple[float, float] = (0.0, 0.0) # (vx, vy)
    track_history: deque = field(default_factory=lambda: deque(maxlen=30)) # 轨迹历史
    confidence_history: deque = field(default_factory=lambda: deque(maxlen=10)) # 置信度历史
    last_seen_frame: int = 0
    consecutive_miss: int = 0
    age: int = 0 # 目标存在的总帧数
    stable_frames: int = 0 # 连续稳定帧数
    is_confirmed: bool = False # 是否确认为有效目标

    def update_position(self, bbox: List[float], confidence: float, frame_num: int):
        """更新目标位置"""
        old_center = self.center
        self.bbox = bbox
        self.confidence = confidence
        self.center = ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2)
        self.last_seen_frame = frame_num
        self.consecutive_miss = 0
        self.age += 1
        self.stable_frames += 1

        # 更新轨迹历史
        self.track_history.append(self.center)
        self.confidence_history.append(confidence)

        # 计算速度
        if old_center != (0, 0):
            self.velocity = (
                self.center[0] - old_center[0],
                self.center[1] - old_center[1]
            )

        # 确认目标（连续3帧以上且置信度稳定）
        if self.stable_frames >= 3 and np.mean(list(self.confidence_history)) > 0.6:
            self.is_confirmed = True

    def predict_next_position(self) -> Tuple[float, float]:
        """预测下一帧位置"""
        return (
            self.center[0] + self.velocity[0],
            self.center[1] + self.velocity[1]
        )

    def get_smoothed_position(self) -> Tuple[float, float]:
        """获取平滑后的位置"""
        if len(self.track_history) < 3:
            return self.center

        # 使用简单的移动平均
        recent_positions = list(self.track_history)[-3:]
        avg_x = sum(pos[0] for pos in recent_positions) / len(recent_positions)
        avg_y = sum(pos[1] for pos in recent_positions) / len(recent_positions)
        return (avg_x, avg_y)

@dataclass
class CameraMotion:
    """镜头运动信息"""
    translation: Tuple[float, float] = (0.0, 0.0) # 平移 (dx, dy)
    rotation: float = 0.0 # 旋转角度
    scale: float = 1.0 # 缩放比例
    confidence: float = 0.0 # 运动估计置信度

class CameraMotionCompensator:
    """镜头运动补偿器"""

    def __init__(self, max_features: int = 1000, quality_level: float = 0.01, min_distance: int = 10):
        self.max_features = max_features
        self.quality_level = quality_level
        self.min_distance = min_distance
        self.prev_gray = None
        self.prev_points = None
        self.motion_history = deque(maxlen=10)

        # Lucas-Kanade光流参数
        self.lk_params = dict(
            winSize=(15, 15),
            maxLevel=2,
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
        )

    def detect_camera_motion(self, frame: np.ndarray) -> CameraMotion:
        """检测镜头运动"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame

        if self.prev_gray is None:
            self.prev_gray = gray
            # 检测特征点
            self.prev_points = cv2.goodFeaturesToTrack(
                gray, self.max_features, self.quality_level, self.min_distance
            )
            return CameraMotion()

        try:
            # 使用Lucas-Kanade光流跟踪特征点
            if self.prev_points is not None and len(self.prev_points) > 10:
                curr_points, status, error = cv2.calcOpticalFlowPyrLK(
                    self.prev_gray, gray, self.prev_points, None, **self.lk_params
                )

                # 筛选有效的特征点
                valid_old = self.prev_points[status == 1]
                valid_new = curr_points[status == 1]

                if len(valid_old) >= 8:
                    # 估计仿射变换矩阵
                    transform_matrix, inliers = cv2.estimateAffinePartial2D(
                        valid_old, valid_new, method=cv2.RANSAC, ransacReprojThreshold=3.0
                    )

                    if transform_matrix is not None:
                        # 提取运动参数
                        dx = transform_matrix[0, 2]
                        dy = transform_matrix[1, 2]

                        # 计算旋转角度
                        rotation = math.atan2(transform_matrix[1, 0], transform_matrix[0, 0])

                        # 计算缩放比例
                        scale_x = math.sqrt(transform_matrix[0, 0]**2 + transform_matrix[1, 0]**2)
                        scale_y = math.sqrt(transform_matrix[0, 1]**2 + transform_matrix[1, 1]**2)
                        scale = (scale_x + scale_y) / 2

                        # 计算置信度（基于内点比例）
                        confidence = np.sum(inliers) / len(valid_old) if inliers is not None else 0.0

                        motion = CameraMotion(
                            translation=(dx, dy),
                            rotation=rotation,
                            scale=scale,
                            confidence=confidence
                        )

                        self.motion_history.append(motion)

                        # 更新特征点
                        self.prev_points = cv2.goodFeaturesToTrack(
                            gray, self.max_features, self.quality_level, self.min_distance
                        )
                        self.prev_gray = gray

                        return motion

            # 如果运动估计失败，重新检测特征点
            self.prev_points = cv2.goodFeaturesToTrack(
                gray, self.max_features, self.quality_level, self.min_distance
            )
            self.prev_gray = gray

            return CameraMotion()

        except Exception as e:
            logger.warning(f"相机运动估计失败: {e}")
            self.prev_gray = gray
            return CameraMotion()

    def get_smoothed_motion(self) -> CameraMotion:
        """获取平滑后的运动参数"""
        if not self.motion_history:
            return CameraMotion()

        # 使用指数加权移动平均
        weights = np.exp(np.linspace(-1, 0, len(self.motion_history)))
        weights /= weights.sum()

        avg_translation = (0.0, 0.0)
        avg_rotation = 0.0
        avg_scale = 1.0
        avg_confidence = 0.0

        for i, motion in enumerate(self.motion_history):
            w = weights[i]
            avg_translation = (
                avg_translation[0] + motion.translation[0] * w,
                avg_translation[1] + motion.translation[1] * w
            )
            avg_rotation += motion.rotation * w
            avg_scale += motion.scale * w
            avg_confidence += motion.confidence * w

        return CameraMotion(
            translation=avg_translation,
            rotation=avg_rotation,
            scale=avg_scale,
            confidence=avg_confidence
        )

class ObjectTracker:
    """增强的目标追踪器，支持镜头运动补偿和目标记忆"""

    def __init__(self, max_disappeared: int = 10, max_distance: float = 100,
                 min_confidence: float = 0.5, iou_threshold: float = 0.3):
        """
        初始化目标追踪器

        Args:
            max_disappeared: 目标消失的最大帧数
            max_distance: 关联目标的最大距离
            min_confidence: 最小置信度阈值
            iou_threshold: IoU阈值用于目标关联
        """
        self.next_id = 1
        self.tracked_objects: Dict[int, TrackedObject] = {}
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance
        self.min_confidence = min_confidence
        self.iou_threshold = iou_threshold
        self.frame_count = 0

        # 镜头运动补偿器
        self.motion_compensator = CameraMotionCompensator()

        logger.info("目标追踪器初始化完成")

    def update(self, detections: List[Dict], frame: np.ndarray) -> Tuple[List[TrackedObject], CameraMotion]:
        """
        更新追踪器状态

        Args:
            detections: 检测结果列表，每个检测包含 bbox, confidence, class_id, class_name
            frame: 当前帧图像

        Returns:
            (tracked_objects, camera_motion): 追踪对象列表和镜头运动信息
        """
        self.frame_count += 1

        # 检测镜头运动
        camera_motion = self.motion_compensator.detect_camera_motion(frame)

        # 补偿镜头运动对现有目标的影响
        self._compensate_camera_motion(camera_motion)

        # 过滤低置信度检测
        valid_detections = [
            det for det in detections
            if det.get('confidence', 0) >= self.min_confidence
        ]

        # 如果没有检测结果，更新现有目标的消失计数
        if not valid_detections:
            self._handle_no_detections()
            return list(self.tracked_objects.values()), camera_motion

        # 将检测结果与现有目标关联
        matched_pairs, unmatched_detections, unmatched_trackers = self._associate_detections(
            valid_detections
        )

        # 更新匹配的目标
        for det_idx, track_id in matched_pairs:
            detection = valid_detections[det_idx]
            self.tracked_objects[track_id].update_position(
                detection['bbox'],
                detection['confidence'],
                self.frame_count
            )

        # 为未匹配的检测创建新目标
        for det_idx in unmatched_detections:
            self._create_new_target(valid_detections[det_idx])

        # 更新未匹配目标的消失计数
        for track_id in unmatched_trackers:
            self.tracked_objects[track_id].consecutive_miss += 1

        # 移除长时间消失的目标
        self._remove_disappeared_targets()

        return list(self.tracked_objects.values()), camera_motion

    def _compensate_camera_motion(self, camera_motion: CameraMotion):
        """补偿镜头运动对目标位置的影响"""
        if camera_motion.confidence < 0.3: # 低置信度的运动估计不进行补偿
            return

        dx, dy = camera_motion.translation
        rotation = camera_motion.rotation
        scale = camera_motion.scale

        for track_id, obj in self.tracked_objects.items():
            # 补偿平移
            new_center = (
                obj.center[0] - dx,
                obj.center[1] - dy
            )

            # 如果需要，可以添加旋转和缩放补偿
            # 这里简化处理，只考虑平移

            # 更新边界框
            bbox_width = obj.bbox[2] - obj.bbox[0]
            bbox_height = obj.bbox[3] - obj.bbox[1]

            new_bbox = [
                new_center[0] - bbox_width / 2,
                new_center[1] - bbox_height / 2,
                new_center[0] + bbox_width / 2,
                new_center[1] + bbox_height / 2
            ]

            obj.bbox = new_bbox
            obj.center = new_center

    def _associate_detections(self, detections: List[Dict]) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """关联检测结果与现有目标"""
        if not self.tracked_objects:
            return [], list(range(len(detections))), []

        # 计算IoU矩阵
        iou_matrix = np.zeros((len(detections), len(self.tracked_objects)))
        track_ids = list(self.tracked_objects.keys())

        for i, detection in enumerate(detections):
            det_bbox = detection['bbox']
            for j, track_id in enumerate(track_ids):
                track_bbox = self.tracked_objects[track_id].bbox
                iou_matrix[i, j] = self._calculate_iou(det_bbox, track_bbox)

        # 使用匈牙利算法进行最优匹配
        matched_pairs = []
        unmatched_detections = []
        unmatched_trackers = []

        # 简化的贪婪匹配算法
        used_detection_indices = set()
        used_tracker_indices = set()

        # 按IoU降序排序
        iou_pairs = []
        for i in range(iou_matrix.shape[0]):
            for j in range(iou_matrix.shape[1]):
                if iou_matrix[i, j] > self.iou_threshold:
                    iou_pairs.append((i, j, iou_matrix[i, j]))

        iou_pairs.sort(key=lambda x: x[2], reverse=True)

        for det_idx, track_idx, iou_score in iou_pairs:
            if det_idx not in used_detection_indices and track_idx not in used_tracker_indices:
                matched_pairs.append((det_idx, track_ids[track_idx]))
                used_detection_indices.add(det_idx)
                used_tracker_indices.add(track_idx)

        # 找出未匹配的检测和追踪器
        for i in range(len(detections)):
            if i not in used_detection_indices:
                unmatched_detections.append(i)

        for j, track_id in enumerate(track_ids):
            if j not in used_tracker_indices:
                unmatched_trackers.append(track_id)

        return matched_pairs, unmatched_detections, unmatched_trackers

    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算两个边界框的IoU"""
        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2

        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)

        # 计算并集
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def _handle_no_detections(self):
        """处理没有检测结果的情况"""
        for track_id in list(self.tracked_objects.keys()):
            self.tracked_objects[track_id].consecutive_miss += 1

    def _create_new_target(self, detection: Dict):
        """创建新的追踪目标"""
        bbox = detection['bbox']
        center = ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2)

        new_target = TrackedObject(
            id=self.next_id,
            class_id=detection.get('class_id', 0),
            class_name=detection.get('class_name', 'unknown'),
            bbox=bbox,
            confidence=detection['confidence'],
            center=center,
            last_seen_frame=self.frame_count
        )

        self.tracked_objects[self.next_id] = new_target
        logger.debug(f"创建新目标 ID={self.next_id}, class={detection.get('class_name')}")
        self.next_id += 1

    def _remove_disappeared_targets(self):
        """移除长时间消失的目标"""
        to_remove = []

        for track_id, obj in self.tracked_objects.items():
            if obj.consecutive_miss > self.max_disappeared:
                to_remove.append(track_id)

        for track_id in to_remove:
            del self.tracked_objects[track_id]
            logger.debug(f"移除目标 ID={track_id}")

    def get_leakage_summary(self) -> Dict[str, Any]:
        """获取检测点汇总信息（已移除泄漏量计算）"""
        active_detection_points = {}

        # 活跃的检测点
        for track_id, obj in self.tracked_objects.items():
            if obj.is_confirmed:
                active_detection_points[track_id] = {
                    'position': obj.get_smoothed_position(),
                    'confidence': obj.confidence,
                    'age': obj.age,
                    'class_name': obj.class_name
                }

        return {
            'active_detection_points': active_detection_points,
            # Volume calculations removed as per Issue #79
            # 'total_active_volume': 0.0, # Removed
            # 'total_historical_volume': 0.0, # Removed
            'total_points_detected': len(active_detection_points)
        }

    def draw_tracking_info(self, frame: np.ndarray) -> np.ndarray:
        """在帧上绘制追踪信息"""
        result_frame = frame.copy()

        # 绘制活跃目标
        for track_id, obj in self.tracked_objects.items():
            if obj.is_confirmed:
                # 绘制边界框
                bbox = obj.bbox
                color = (0, 255, 0) if obj.class_name.lower() in ['leak', 'leakage', '泄漏'] else (255, 0, 0)
                cv2.rectangle(result_frame,
                            (int(bbox[0]), int(bbox[1])),
                            (int(bbox[2]), int(bbox[3])),
                            color, 2)

                # 绘制ID和信息
                label = f"ID:{track_id} {obj.class_name} {obj.confidence:.2f}"

                y_offset = 0
                for line in label.split('\n'):
                    cv2.putText(result_frame, line,
                              (int(bbox[0]), int(bbox[1]) - 10 - y_offset),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                    y_offset += 20

                # 绘制轨迹
                if len(obj.track_history) > 1:
                    points = [(int(p[0]), int(p[1])) for p in obj.track_history]
                    for i in range(1, len(points)):
                        cv2.line(result_frame, points[i-1], points[i], color, 1)

        return result_frame

    def reset(self):
        """重置追踪器状态"""
        self.tracked_objects.clear()
        self.motion_compensator = CameraMotionCompensator()
        self.frame_count = 0
        self.next_id = 1
        logger.info("目标追踪器已重置")