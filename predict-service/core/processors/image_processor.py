import logging
import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>
from typing import <PERSON>ple, Optional

from utils.video_utils import annotate_frame
from utils.cuda_utils import safe_cuda_empty_cache

logger = logging.getLogger(__name__)

class ImageProcessor:
    def __init__(self, config):
        self.config = config

    def process_image(self, image_path: str, model_path: str,
                     confidence_threshold: float = 0.5,
                     poly_color=(0, 0, 255),
                     poly_thickness=2) -> Tuple[Optional[np.ndarray], Optional[str]]:
        """处理单张图片"""
        # 加载图片
        frame = cv2.imread(image_path)
        if frame is None:
            logger.error(f'Failed to read image file: {image_path}')
            return None, 'Failed to read image file'

        try:
            # 加载模型
            model = YOLO(model_path)
            model = model.cuda() if self.config.enable_cuda else model.cpu()

            # 处理图片
            results = model(frame, conf=confidence_threshold)
            overlay = np.zeros_like(frame)

            for r in results:
                if not r.masks:
                    continue
                masks = r.masks
                boxes = r.boxes
                masks_xy = masks.xy
                boxes_cls = boxes.cls.cpu().numpy().astype(int)

                for i in range(len(masks_xy)):
                    mask = masks_xy[i]
                    cls = boxes_cls[i]
                    for point in mask:
                        cv2.circle(overlay, (int(point[0]), int(point[1])), 1,
                                 (0, 255, 0) if cls == 0 else (0, 0, 255), -1)
                    cv2.polylines(overlay, [np.array(mask, dtype=np.int32)],
                                isClosed=True, color=poly_color, thickness=poly_thickness)

            detection_result = cv2.addWeighted(frame, 1, overlay, 0.5, 0)
            annotate_frame(detection_result, 'Detection', (10, 30))

            return detection_result, None

        except Exception as e:
            logger.error(f'Error processing image: {str(e)}')
            return None, str(e)

        finally:
            if self.config.enable_cuda and 'model' in locals():
                del model
                import torch
                safe_cuda_empty_cache()
