#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
气体追踪最终数据处理器
Gas Tracking Finalizer
====================

专门处理视频处理完成后的最终气体追踪数据汇总，包括：
1. 追踪轨迹去重和合并
2. 稳定泄漏点识别
3. 风险评估和统计分析
4. 数据导出和格式化
"""

import logging
import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
import time
import math
from pathlib import Path
import csv

logger = logging.getLogger(__name__)


@dataclass
class FinalLeakagePoint:
    """最终泄漏点数据结构"""
    track_id: int # 追踪ID
    class_name: str # 检测类别
    confidence_stats: Dict[str, float] # 置信度统计
    trajectory_summary: Dict[str, Any] # 轨迹汇总
    temporal_info: Dict[str, Any] # 时间信息
    spatial_info: Dict[str, Any] # 空间信息
    stability_analysis: Dict[str, Any] # 稳定性分析
    risk_assessment: Dict[str, Any] # 风险评估
    dispersion_analysis: Dict[str, Any] # 扩散分析
    dispersion_behavior: Dict[str, Any] # 气体扩散行为分类（任务2.2）
    confidence_distribution: Dict[str, Any] # 置信度区间统计（任务2.3）
    camera_compensation_stats: Dict[str, Any] # 镜头补偿统计
    raw_data: List[Dict[str, Any]] = field(default_factory=list) # 原始数据


@dataclass
class GasTrackingFinalSummary:
    """气体追踪最终汇总"""
    processing_info: Dict[str, Any] # 处理信息
    overall_statistics: Dict[str, Any] # 总体统计
    risk_assessment: Dict[str, Any] # 整体风险评估
    final_leakage_points: List[FinalLeakagePoint] # 最终泄漏点列表
    temporal_analysis: Dict[str, Any] # 时间分析
    spatial_analysis: Dict[str, Any] # 空间分析
    camera_motion_analysis: Dict[str, Any] # 镜头运动分析
    quality_metrics: Dict[str, Any] # 质量指标


class GasTrackingFinalizer:
    """
    气体追踪最终数据处理器
    Gas Tracking Finalizer
    """

    def __init__(self,
                 confidence_threshold: float = 0.3,
                 stability_threshold: float = 0.7,
                 min_track_duration: int = 10,
                 spatial_merge_threshold: float = 50.0):
        """
        初始化最终数据处理器

        Args:
            confidence_threshold: 置信度阈值
            stability_threshold: 稳定性阈值
            min_track_duration: 最小追踪持续时间（帧数）
            spatial_merge_threshold: 空间合并阈值（像素）
        """
        self.confidence_threshold = confidence_threshold
        self.stability_threshold = stability_threshold
        self.min_track_duration = min_track_duration
        self.spatial_merge_threshold = spatial_merge_threshold

        logger.info(f" 气体追踪最终数据处理器初始化完成")
        logger.info(f" 置信度阈值: {confidence_threshold}")
        logger.info(f" 稳定性阈值: {stability_threshold}")
        logger.info(f" 最小追踪时长: {min_track_duration} 帧")
        logger.info(f" 空间合并阈值: {spatial_merge_threshold} 像素")

    def process_final_gas_tracking_data(self,
                                      gas_tracking_results: List[Dict[str, Any]],

                                      video_info: Optional[Dict[str, Any]] = None) -> GasTrackingFinalSummary:
        """
        处理最终气体追踪数据

        Args:
            gas_tracking_results: 所有帧的气体追踪结果列表
            video_info: 视频信息（帧率、分辨率等）

        Returns:
            最终汇总结果
        """
        if not gas_tracking_results:
            return self._create_empty_summary("no_tracking_data")

        logger.info(f" 开始处理最终气体追踪数据，共 {len(gas_tracking_results)} 帧")

        try:
            start_time = time.time()

            # 1. 预处理和数据清洗
            cleaned_data = self._preprocess_tracking_data(gas_tracking_results)
            logger.info(f" 数据清洗完成，有效数据: {len(cleaned_data)} 帧")

            # 2. 轨迹提取和去重
            unique_tracks = self._extract_unique_tracks(cleaned_data)
            logger.info(f" 轨迹去重完成，唯一轨迹: {len(unique_tracks)} 条")

            # 3. 生成最终泄漏点
            final_leakage_points = self._generate_final_leakage_points(unique_tracks, video_info)
            logger.info(f" 最终泄漏点生成完成: {len(final_leakage_points)} 个")

            # 4. 整体统计分析
            overall_statistics = self._compute_overall_statistics(final_leakage_points, cleaned_data)

            # 5. 风险评估
            risk_assessment = self._assess_overall_risk(final_leakage_points, overall_statistics)

            # 6. 时间和空间分析
            temporal_analysis = self._analyze_temporal_patterns(cleaned_data, video_info)
            spatial_analysis = self._analyze_spatial_patterns(final_leakage_points)

            # 7. 镜头运动分析
            camera_motion_analysis = self._analyze_camera_motion(cleaned_data)

            # 8. 质量指标计算
            quality_metrics = self._compute_quality_metrics(cleaned_data, final_leakage_points)

            processing_time = time.time() - start_time

            # 构建最终汇总
            summary = GasTrackingFinalSummary(
                processing_info={
                    "status": "success",
                    "processing_time": processing_time,
                    "total_frames_processed": len(gas_tracking_results),
                    "valid_frames": len(cleaned_data),
                    "timestamp": time.time(),
                    "algorithm_version": "YOLOv8 + ByteTrack + GasFinalizer v1.0"
                },
                overall_statistics=overall_statistics,
                risk_assessment=risk_assessment,
                final_leakage_points=final_leakage_points,
                temporal_analysis=temporal_analysis,
                spatial_analysis=spatial_analysis,
                camera_motion_analysis=camera_motion_analysis,
                quality_metrics=quality_metrics
            )

            logger.info(f" 最终气体追踪数据处理完成，耗时: {processing_time:.2f}s")
            return summary

        except Exception as e:
            logger.error(f" 最终气体追踪数据处理失败: {e}")
            return self._create_empty_summary("processing_error", str(e))

    def _preprocess_tracking_data(self, tracking_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理和清洗追踪数据"""
        cleaned_data = []

        for frame_result in tracking_results:
            if not isinstance(frame_result, dict):
                continue

            # 提取结果数据
            result = frame_result.get('result', {})
            if not result:
                continue

            # 检查必要字段
            if 'frame_number' not in frame_result or 'tracked_objects' not in result:
                continue

            # 过滤低质量追踪对象
            tracked_objects = result.get('tracked_objects', [])
            filtered_objects = []

            for obj in tracked_objects:
                if (obj.get('confidence', 0) >= self.confidence_threshold and
                    obj.get('total_lifetime', 0) >= self.min_track_duration):
                    filtered_objects.append(obj)

            if filtered_objects:
                cleaned_result = frame_result.copy()
                cleaned_result['result']['tracked_objects'] = filtered_objects
                cleaned_data.append(cleaned_result)

        return cleaned_data

    def _extract_unique_tracks(self, cleaned_data: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        提取唯一轨迹并进行智能合并（优化版本）

        Args:
            cleaned_data: 清洗后的追踪数据

        Returns:
            唯一轨迹字典 {track_id: [track_objects]}
        """
        logger.info(" 开始提取唯一轨迹...")

        # 1. 按track_id分组收集所有轨迹数据
        raw_tracks = defaultdict(list)

        for frame_data in cleaned_data:
            frame_result = frame_data.get('result', {})
            tracked_objects = frame_result.get('tracked_objects', [])

            for obj in tracked_objects:
                track_id = obj.get('track_id')
                if track_id is not None and track_id >= 0:
                    # 添加帧信息
                    obj_with_frame = obj.copy()
                    obj_with_frame['frame_number'] = frame_data.get('frame_number', 0)
                    obj_with_frame['timestamp'] = frame_data.get('timestamp', 0)
                    raw_tracks[track_id].append(obj_with_frame)

        logger.info(f" 原始轨迹数量: {len(raw_tracks)}")

        # 2. 过滤短轨迹
        filtered_tracks = {}
        for track_id, track_data in raw_tracks.items():
            if len(track_data) >= self.min_track_duration:
                # 按帧号排序
                track_data.sort(key=lambda x: x.get('frame_number', 0))
                filtered_tracks[track_id] = track_data
            else:
                logger.debug(f" 过滤短轨迹 ID={track_id}, 长度={len(track_data)}")

        logger.info(f" 过滤后轨迹数量: {len(filtered_tracks)}")

        # 3. 智能合并相似轨迹（新增功能）
        merged_tracks = self._merge_similar_tracks(filtered_tracks)

        logger.info(f" 合并后最终轨迹数量: {len(merged_tracks)}")

        return merged_tracks

    def _merge_similar_tracks(self, tracks: Dict[int, List[Dict[str, Any]]]) -> Dict[int, List[Dict[str, Any]]]:
        """
         智能合并相似轨迹（解决ByteTrack多ID问题）

        Args:
            tracks: 原始轨迹字典

        Returns:
            合并后的轨迹字典
        """
        logger.info(" 开始智能合并相似轨迹...")

        if len(tracks) <= 1:
            return tracks

        track_ids = list(tracks.keys())
        merged_tracks = tracks.copy()
        merge_pairs = []

        # 1. 寻找需要合并的轨迹对
        for i in range(len(track_ids)):
            for j in range(i + 1, len(track_ids)):
                track_id_1, track_id_2 = track_ids[i], track_ids[j]

                if track_id_1 not in merged_tracks or track_id_2 not in merged_tracks:
                    continue

                track_1 = merged_tracks[track_id_1]
                track_2 = merged_tracks[track_id_2]

                # 检查是否应该合并
                if self._should_merge_tracks(track_1, track_2):
                    merge_pairs.append((track_id_1, track_id_2))
                    logger.debug(f" 发现需要合并的轨迹对: {track_id_1} <-> {track_id_2}")

        # 2. 执行合并
        for track_id_1, track_id_2 in merge_pairs:
            if track_id_1 in merged_tracks and track_id_2 in merged_tracks:
                # 合并轨迹数据
                combined_track = merged_tracks[track_id_1] + merged_tracks[track_id_2]
                # 按帧号排序
                combined_track.sort(key=lambda x: x.get('frame_number', 0))

                # 保留较小的ID，删除较大的ID
                keep_id = min(track_id_1, track_id_2)
                remove_id = max(track_id_1, track_id_2)

                merged_tracks[keep_id] = combined_track
                del merged_tracks[remove_id]

                logger.info(f" 合并轨迹: {remove_id} -> {keep_id} (总长度: {len(combined_track)})")

        logger.info(f" 完成轨迹合并，合并了 {len(merge_pairs)} 对轨迹")
        return merged_tracks

    def _should_merge_tracks(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> bool:
        """
         增强版：判断两个轨迹是否应该合并（解决多ID追踪同一气体源问题）

        Args:
            track_1: 轨迹1的数据
            track_2: 轨迹2的数据

        Returns:
            是否应该合并
        """
        if not track_1 or not track_2:
            return False

        # 1. 时间重叠分析（增强版）
        temporal_analysis = self._analyze_track_temporal_overlap(track_1, track_2)

        # 如果时间间隔太大，不合并
        if temporal_analysis['gap'] > 30: # 超过30帧间隔
            return False

        # 如果有时间重叠且重叠比例高，优先合并
        if temporal_analysis['overlap'] and temporal_analysis['overlap_ratio'] > 0.3:
            logger.debug(f" 时间重叠比例高: {temporal_analysis['overlap_ratio']:.2f}")

        # 2. 空间位置聚类检查（增强版）
        spatial_similarity = self._calculate_spatial_similarity(track_1, track_2)
        if spatial_similarity < 0.4: # 空间相似度阈值
            return False

        # 3. IoU重叠度检查（针对边界框）
        iou_similarity = self._calculate_track_iou_similarity(track_1, track_2)
        if iou_similarity < 0.2: # 降低IoU阈值，更容易合并
            return False

        # 4. 轮廓形状相似度检查（针对不规则气体）
        contour_similarity = self._calculate_contour_similarity(track_1, track_2)

        # 5. 中心点轨迹聚类检查
        trajectory_similarity = self._calculate_trajectory_similarity(track_1, track_2)

        # 6. 置信度和类别检查
        confidences_1 = [obj.get('detection', {}).get('confidence', 0) for obj in track_1]
        confidences_2 = [obj.get('detection', {}).get('confidence', 0) for obj in track_2]

        avg_conf_1 = np.mean(confidences_1) if confidences_1 else 0
        avg_conf_2 = np.mean(confidences_2) if confidences_2 else 0

        # 置信度差异检查（放宽条件）
        if abs(avg_conf_1 - avg_conf_2) > 0.4:
            return False

        # 类别必须相同
        class_1 = track_1[0].get('detection', {}).get('class_name', '')
        class_2 = track_2[0].get('detection', {}).get('class_name', '')
        if class_1 != class_2:
            return False

        # 综合评分决策
        merge_score = (
            spatial_similarity * 0.3 +
            iou_similarity * 0.25 +
            contour_similarity * 0.2 +
            trajectory_similarity * 0.15 +
            (1.0 - abs(avg_conf_1 - avg_conf_2)) * 0.1
        )

        # 如果有时间重叠，降低合并阈值
        merge_threshold = 0.4 if temporal_analysis['overlap'] else 0.5

        should_merge = merge_score >= merge_threshold

        if should_merge:
            logger.debug(f" 合并决策: 得分={merge_score:.3f}, 阈值={merge_threshold:.3f}")
            logger.debug(f" 详细得分: 空间={spatial_similarity:.3f}, IoU={iou_similarity:.3f}, 轮廓={contour_similarity:.3f}, 轨迹={trajectory_similarity:.3f}")

        return should_merge

    def _calculate_spatial_similarity(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> float:
        """
         计算空间位置相似度（中心点聚类）

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            空间相似度 (0.0-1.0)
        """
        try:
            # 提取中心点
            centers_1 = []
            centers_2 = []

            for obj in track_1:
                detection = obj.get('detection', {})
                bbox = detection.get('bbox', [])
                if len(bbox) >= 4:
                    cx = (bbox[0] + bbox[2]) / 2
                    cy = (bbox[1] + bbox[3]) / 2
                    centers_1.append((cx, cy))

            for obj in track_2:
                detection = obj.get('detection', {})
                bbox = detection.get('bbox', [])
                if len(bbox) >= 4:
                    cx = (bbox[0] + bbox[2]) / 2
                    cy = (bbox[1] + bbox[3]) / 2
                    centers_2.append((cx, cy))

            if not centers_1 or not centers_2:
                return 0.0

            # 计算平均中心点
            avg_center_1 = (np.mean([c[0] for c in centers_1]), np.mean([c[1] for c in centers_1]))
            avg_center_2 = (np.mean([c[0] for c in centers_2]), np.mean([c[1] for c in centers_2]))

            # 计算距离
            distance = math.sqrt(
                (avg_center_1[0] - avg_center_2[0])**2 +
                (avg_center_1[1] - avg_center_2[1])**2
            )

            # 转换为相似度（距离越小，相似度越高）
            max_distance = self.spatial_merge_threshold
            similarity = max(0.0, 1.0 - (distance / max_distance))

            return similarity

        except Exception as e:
            logger.warning(f"计算空间相似度失败: {e}")
            return 0.0

    def _calculate_trajectory_similarity(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> float:
        """
         计算轨迹相似度（运动模式分析）

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            轨迹相似度 (0.0-1.0)
        """
        try:
            # 提取轨迹点
            trajectory_1 = []
            trajectory_2 = []

            for obj in track_1:
                detection = obj.get('detection', {})
                bbox = detection.get('bbox', [])
                frame_num = obj.get('frame_number', 0)
                if len(bbox) >= 4:
                    cx = (bbox[0] + bbox[2]) / 2
                    cy = (bbox[1] + bbox[3]) / 2
                    trajectory_1.append((frame_num, cx, cy))

            for obj in track_2:
                detection = obj.get('detection', {})
                bbox = detection.get('bbox', [])
                frame_num = obj.get('frame_number', 0)
                if len(bbox) >= 4:
                    cx = (bbox[0] + bbox[2]) / 2
                    cy = (bbox[1] + bbox[3]) / 2
                    trajectory_2.append((frame_num, cx, cy))

            if len(trajectory_1) < 2 or len(trajectory_2) < 2:
                return 0.5 # 轨迹太短，给中等相似度

            # 按帧号排序
            trajectory_1.sort(key=lambda x: x[0])
            trajectory_2.sort(key=lambda x: x[0])

            # 计算运动方向相似度
            direction_1 = self._calculate_movement_direction(trajectory_1)
            direction_2 = self._calculate_movement_direction(trajectory_2)

            if direction_1 is None or direction_2 is None:
                return 0.5

            # 计算方向角度差异
            angle_diff = abs(direction_1 - direction_2)
            angle_diff = min(angle_diff, 360 - angle_diff) # 处理角度环形

            # 转换为相似度
            direction_similarity = 1.0 - (angle_diff / 180.0)

            return max(0.0, direction_similarity)

        except Exception as e:
            logger.warning(f"计算轨迹相似度失败: {e}")
            return 0.5

    def _calculate_movement_direction(self, trajectory: List[tuple]) -> float:
        """
        计算轨迹的主要运动方向

        Args:
            trajectory: 轨迹点列表 [(frame, x, y), ...]

        Returns:
            运动方向角度（度）
        """
        if len(trajectory) < 2:
            return None

        # 计算总体位移
        start_point = trajectory[0]
        end_point = trajectory[-1]

        dx = end_point[1] - start_point[1]
        dy = end_point[2] - start_point[2]

        if abs(dx) < 1e-6 and abs(dy) < 1e-6:
            return 0.0 # 静止

        # 计算角度
        angle = math.atan2(dy, dx) * 180 / math.pi
        return angle % 360

    def _calculate_track_iou_similarity(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> float:
        """
         计算两个轨迹的IoU相似度（针对不规则气体形状）

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            IoU相似度 (0.0-1.0)
        """
        try:
            # 提取边界框
            bboxes_1 = [obj.get('bbox', []) for obj in track_1 if obj.get('bbox')]
            bboxes_2 = [obj.get('bbox', []) for obj in track_2 if obj.get('bbox')]

            if not bboxes_1 or not bboxes_2:
                return 0.0

            # 计算平均边界框
            avg_bbox_1 = [
                np.mean([bbox[0] for bbox in bboxes_1]), # x1
                np.mean([bbox[1] for bbox in bboxes_1]), # y1
                np.mean([bbox[2] for bbox in bboxes_1]), # x2
                np.mean([bbox[3] for bbox in bboxes_1]) # y2
            ]

            avg_bbox_2 = [
                np.mean([bbox[0] for bbox in bboxes_2]), # x1
                np.mean([bbox[1] for bbox in bboxes_2]), # y1
                np.mean([bbox[2] for bbox in bboxes_2]), # x2
                np.mean([bbox[3] for bbox in bboxes_2]) # y2
            ]

            # 计算IoU
            x1 = max(avg_bbox_1[0], avg_bbox_2[0])
            y1 = max(avg_bbox_1[1], avg_bbox_2[1])
            x2 = min(avg_bbox_1[2], avg_bbox_2[2])
            y2 = min(avg_bbox_1[3], avg_bbox_2[3])

            if x2 <= x1 or y2 <= y1:
                return 0.0

            intersection = (x2 - x1) * (y2 - y1)

            area_1 = (avg_bbox_1[2] - avg_bbox_1[0]) * (avg_bbox_1[3] - avg_bbox_1[1])
            area_2 = (avg_bbox_2[2] - avg_bbox_2[0]) * (avg_bbox_2[3] - avg_bbox_2[1])

            union = area_1 + area_2 - intersection

            if union <= 0:
                return 0.0

            return intersection / union

        except Exception as e:
            logger.warning(f"计算轨迹IoU相似度失败: {e}")
            return 0.0

    def _calculate_contour_similarity(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> float:
        """
         计算轮廓相似度（针对不规则气体形状的高级检查）

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            轮廓相似度 (0.0-1.0)
        """
        try:
            # 提取轮廓数据
            contours_1 = [obj.get('contour') for obj in track_1 if obj.get('contour') is not None]
            contours_2 = [obj.get('contour') for obj in track_2 if obj.get('contour') is not None]

            if not contours_1 or not contours_2:
                return 0.0

            # 计算形状复杂度相似性
            complexities_1 = []
            complexities_2 = []

            for contour in contours_1:
                if len(contour) > 0:
                    area = cv2.contourArea(contour)
                    perimeter = cv2.arcLength(contour, True)
                    if area > 0:
                        complexity = (perimeter ** 2) / area
                        complexities_1.append(complexity)

            for contour in contours_2:
                if len(contour) > 0:
                    area = cv2.contourArea(contour)
                    perimeter = cv2.arcLength(contour, True)
                    if area > 0:
                        complexity = (perimeter ** 2) / area
                        complexities_2.append(complexity)

            if not complexities_1 or not complexities_2:
                return 0.0

            # 计算复杂度相似性
            avg_complexity_1 = np.mean(complexities_1)
            avg_complexity_2 = np.mean(complexities_2)

            complexity_diff = abs(avg_complexity_1 - avg_complexity_2)
            max_complexity = max(avg_complexity_1, avg_complexity_2)

            if max_complexity == 0:
                return 1.0

            similarity = 1.0 - (complexity_diff / max_complexity)
            return max(0.0, min(1.0, similarity))

        except Exception as e:
            logger.warning(f"计算轮廓相似度失败: {e}")
            return 0.0

    def _analyze_track_temporal_overlap(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
         分析两个轨迹的时间重叠情况

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            时间重叠分析结果
        """
        frames_1 = [obj.get('frame_number', 0) for obj in track_1]
        frames_2 = [obj.get('frame_number', 0) for obj in track_2]

        if not frames_1 or not frames_2:
            return {"overlap": False, "gap": float('inf'), "overlap_ratio": 0.0}

        min_frame_1, max_frame_1 = min(frames_1), max(frames_1)
        min_frame_2, max_frame_2 = min(frames_2), max(frames_2)

        # 计算重叠
        overlap_start = max(min_frame_1, min_frame_2)
        overlap_end = min(max_frame_1, max_frame_2)

        if overlap_start <= overlap_end:
            # 有重叠
            overlap_frames = overlap_end - overlap_start + 1
            total_frames = max(max_frame_1, max_frame_2) - min(min_frame_1, min_frame_2) + 1
            overlap_ratio = overlap_frames / total_frames if total_frames > 0 else 0.0

            return {
                "overlap": True,
                "gap": 0,
                "overlap_ratio": overlap_ratio,
                "overlap_frames": overlap_frames
            }
        else:
            # 无重叠，计算间隔
            gap = min(abs(min_frame_2 - max_frame_1), abs(min_frame_1 - max_frame_2))
            return {
                "overlap": False,
                "gap": gap,
                "overlap_ratio": 0.0,
                "overlap_frames": 0
            }

    def _merge_track_data_intelligently(self, track_1: List[Dict[str, Any]], track_2: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
         智能合并两个轨迹的数据（处理重叠和冲突）

        Args:
            track_1: 轨迹1
            track_2: 轨迹2

        Returns:
            合并后的轨迹数据
        """
        # 合并所有数据
        combined_data = track_1 + track_2

        # 按帧号排序
        combined_data.sort(key=lambda x: x.get('frame_number', 0))

        # 处理重复帧（选择置信度更高的）
        frame_data_map = {}

        for obj in combined_data:
            frame_num = obj.get('frame_number', 0)
            confidence = obj.get('confidence', 0)

            if frame_num not in frame_data_map:
                frame_data_map[frame_num] = obj
            else:
                # 如果当前对象置信度更高，替换
                if confidence > frame_data_map[frame_num].get('confidence', 0):
                    frame_data_map[frame_num] = obj

        # 转换回列表并排序
        merged_data = list(frame_data_map.values())
        merged_data.sort(key=lambda x: x.get('frame_number', 0))

        return merged_data

    def _generate_final_leakage_points(self,
                                     unique_tracks: Dict[int, List[Dict[str, Any]]],
                                     video_info: Optional[Dict[str, Any]]) -> List[FinalLeakagePoint]:
        """生成最终泄漏点"""
        final_points = []
        fps = video_info.get('fps', 30.0) if video_info else 30.0

        for track_id, track_data in unique_tracks.items():
            if not track_data:
                continue

            try:
                # 基本信息
                first_obj = track_data[0]
                last_obj = track_data[-1]

                # 置信度统计
                confidences = [obj.get('confidence', 0) for obj in track_data]
                confidence_stats = {
                    "average": np.mean(confidences),
                    "max": np.max(confidences),
                    "min": np.min(confidences),
                    "std": np.std(confidences),
                    "trend": self._calculate_confidence_trend(confidences)
                }

                # 轨迹汇总
                trajectory_summary = self._summarize_trajectory(track_data)

                # 时间信息（任务2.1：泄漏点时间段精确化）
                start_frame = first_obj.get('first_detected_frame', 0)
                end_frame = last_obj.get('last_seen_frame', 0)
                total_frames = last_obj.get('total_lifetime', 0)
                
                # 找到峰值置信度的帧
                peak_confidence_obj = max(track_data, key=lambda x: x.get('confidence', 0)) if track_data else first_obj
                peak_frame = peak_confidence_obj.get('frame_number', start_frame)
                
                temporal_info = {
                    "start_frame": start_frame,
                    "end_frame": end_frame,
                    "total_frames": total_frames,
                    "duration_seconds": total_frames / fps if fps > 0 else 0.0,
                    "start_seconds": start_frame / fps if fps > 0 else 0.0,
                    "end_seconds": end_frame / fps if fps > 0 else 0.0,
                    "peak_confidence_frame": peak_frame,
                    "peak_confidence_seconds": peak_frame / fps if fps > 0 else 0.0,
                    "active_periods": self._identify_active_periods_for_track(track_data, fps),
                    "continuity_analysis": self._analyze_leakage_continuity(track_data)
                }

                # 空间信息
                spatial_info = self._analyze_spatial_characteristics(track_data)

                # 稳定性分析
                stability_analysis = self._analyze_stability(track_data)

                # 风险评估
                risk_assessment = self._assess_individual_risk(track_data, confidence_stats, stability_analysis)

                # 扩散分析（任务2.2：气体扩散行为分类）
                dispersion_analysis = self._analyze_dispersion_pattern(track_data)
                dispersion_behavior = self._classify_dispersion_behavior(track_data)
                
                # 置信度分布统计（任务2.3：置信值区间统计）
                confidence_distribution = self._calculate_confidence_distribution(track_data)

                # 镜头补偿统计
                camera_compensation_stats = self._analyze_camera_compensation(track_data)

                final_point = FinalLeakagePoint(
                    track_id=track_id,
                    class_name=first_obj.get('class_name', 'gas_leak'),
                    confidence_stats=confidence_stats,
                    trajectory_summary=trajectory_summary,
                    temporal_info=temporal_info,
                    spatial_info=spatial_info,
                    stability_analysis=stability_analysis,
                    risk_assessment=risk_assessment,
                    dispersion_analysis=dispersion_analysis,
                    dispersion_behavior=dispersion_behavior,
                    confidence_distribution=confidence_distribution,
                    camera_compensation_stats=camera_compensation_stats,
                    raw_data=track_data
                )

                final_points.append(final_point)

            except Exception as e:
                logger.warning(f"生成最终泄漏点失败 track_id={track_id}: {e}")
                continue

        # 按风险等级和置信度排序
        final_points.sort(key=lambda x: (
            x.risk_assessment.get('risk_score', 0),
            x.confidence_stats.get('average', 0)
        ), reverse=True)

        return final_points

    def _calculate_confidence_trend(self, confidences: List[float]) -> str:
        """计算置信度趋势"""
        if len(confidences) < 3:
            return "insufficient_data"

        # 使用线性回归计算趋势
        x = np.arange(len(confidences))
        y = np.array(confidences)

        try:
            slope, _ = np.polyfit(x, y, 1)

            if slope > 0.01:
                return "increasing"
            elif slope < -0.01:
                return "decreasing"
            else:
                return "stable"
        except:
            return "unknown"

    def _summarize_trajectory(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总轨迹信息"""
        centers = [obj.get('center', [0, 0]) for obj in track_data if obj.get('center')]

        if not centers:
            return {"status": "no_trajectory_data"}

        centers_array = np.array(centers)

        # 计算轨迹统计
        start_center = centers[0]
        end_center = centers[-1]
        total_distance = self._calculate_total_distance(centers)
        displacement = np.linalg.norm(np.array(end_center) - np.array(start_center))

        return {
            "start_position": start_center,
            "end_position": end_center,
            "center_of_mass": np.mean(centers_array, axis=0).tolist(),
            "bounding_box": {
                "min_x": float(np.min(centers_array[:, 0])),
                "max_x": float(np.max(centers_array[:, 0])),
                "min_y": float(np.min(centers_array[:, 1])),
                "max_y": float(np.max(centers_array[:, 1]))
            },
            "total_distance": total_distance,
            "displacement": displacement,
            "path_efficiency": displacement / max(total_distance, 1.0),
            "movement_pattern": self._classify_movement_pattern(centers)
        }

    def _calculate_total_distance(self, centers: List[List[float]]) -> float:
        """计算总移动距离"""
        if len(centers) < 2:
            return 0.0

        total_distance = 0.0
        for i in range(1, len(centers)):
            distance = np.linalg.norm(np.array(centers[i]) - np.array(centers[i-1]))
            total_distance += distance

        return total_distance

    def _classify_movement_pattern(self, centers: List[List[float]]) -> str:
        """分类运动模式"""
        if len(centers) < 3:
            return "insufficient_data"

        total_distance = self._calculate_total_distance(centers)
        displacement = np.linalg.norm(np.array(centers[-1]) - np.array(centers[0]))

        if total_distance < 10:
            return "stationary"
        elif displacement / max(total_distance, 1.0) > 0.8:
            return "linear"
        elif displacement / max(total_distance, 1.0) < 0.3:
            return "circular"
        else:
            return "irregular"

    def _analyze_spatial_characteristics(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析空间特征"""
        areas = [obj.get('area', 0) for obj in track_data if obj.get('area', 0) > 0]

        if not areas:
            return {"status": "no_spatial_data"}

        return {
            "area_stats": {
                "average": np.mean(areas),
                "max": np.max(areas),
                "min": np.min(areas),
                "std": np.std(areas),
                "growth_rate": (areas[-1] - areas[0]) / max(areas[0], 1.0) if len(areas) > 1 else 0.0
            },
            "shape_consistency": self._calculate_shape_consistency(track_data),
            "expansion_pattern": self._analyze_expansion_pattern(areas)
        }

    def _calculate_shape_consistency(self, track_data: List[Dict[str, Any]]) -> float:
        """计算形状一致性"""
        shape_features = []

        for obj in track_data:
            features = obj.get('shape_features', {})
            if features:
                # 提取形状特征向量
                feature_vector = [
                    features.get('aspect_ratio', 1.0),
                    features.get('extent', 0.5),
                    features.get('solidity', 0.5),
                    features.get('compactness', 0.5)
                ]
                shape_features.append(feature_vector)

        if len(shape_features) < 2:
            return 1.0

        # 计算特征变异系数
        shape_array = np.array(shape_features)
        cv_values = np.std(shape_array, axis=0) / (np.mean(shape_array, axis=0) + 1e-6)

        # 一致性分数（变异系数越小，一致性越高）
        consistency = 1.0 - np.mean(cv_values)
        return max(0.0, min(1.0, consistency))

    def _analyze_expansion_pattern(self, areas: List[float]) -> str:
        """分析扩张模式"""
        if len(areas) < 3:
            return "insufficient_data"

        # 计算面积变化趋势
        x = np.arange(len(areas))
        y = np.array(areas)

        try:
            slope, _ = np.polyfit(x, y, 1)

            if slope > np.mean(areas) * 0.05: # 5%增长率阈值
                return "expanding"
            elif slope < -np.mean(areas) * 0.05:
                return "contracting"
            else:
                return "stable"
        except:
            return "unknown"

    def _analyze_stability(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析稳定性"""
        stability_scores = [obj.get('stability_score', 0) for obj in track_data if 'stability_score' in obj]

        if not stability_scores:
            # 基于其他特征计算稳定性
            confidences = [obj.get('confidence', 0) for obj in track_data]
            areas = [obj.get('area', 0) for obj in track_data if obj.get('area', 0) > 0]

            # 综合稳定性评分
            confidence_stability = 1.0 - (np.std(confidences) / max(np.mean(confidences), 1e-6))
            area_stability = 1.0 - (np.std(areas) / max(np.mean(areas), 1e-6)) if areas else 0.5

            overall_stability = (confidence_stability + area_stability) / 2.0
            stability_scores = [overall_stability] * len(track_data)

        avg_stability = np.mean(stability_scores)
        is_stable = avg_stability >= self.stability_threshold

        return {
            "average_stability": avg_stability,
            "max_stability": np.max(stability_scores),
            "min_stability": np.min(stability_scores),
            "is_stable": is_stable,
            "stability_trend": self._calculate_stability_trend(stability_scores),
            "stable_frame_ratio": sum(1 for s in stability_scores if s >= self.stability_threshold) / len(stability_scores)
        }

    def _calculate_stability_trend(self, stability_scores: List[float]) -> str:
        """计算稳定性趋势"""
        if len(stability_scores) < 3:
            return "insufficient_data"

        x = np.arange(len(stability_scores))
        y = np.array(stability_scores)

        try:
            slope, _ = np.polyfit(x, y, 1)

            if slope > 0.01:
                return "improving"
            elif slope < -0.01:
                return "degrading"
            else:
                return "stable"
        except:
            return "unknown"

    def _assess_individual_risk(self,
                              track_data: List[Dict[str, Any]],
                              confidence_stats: Dict[str, Any],
                              stability_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估单个泄漏点的风险"""

        # 风险因子计算
        confidence_factor = min(confidence_stats.get('average', 0) / 0.8, 1.0) # 归一化到0.8
        stability_factor = stability_analysis.get('average_stability', 0)
        duration_factor = min(len(track_data) / 100.0, 1.0) # 归一化到100帧

        # 空间因子（面积大小）
        areas = [obj.get('area', 0) for obj in track_data if obj.get('area', 0) > 0]
        avg_area = np.mean(areas) if areas else 0
        area_factor = min(avg_area / 10000.0, 1.0) # 归一化到10000像素

        # 综合风险评分
        risk_score = (
            confidence_factor * 0.3 +
            stability_factor * 0.3 +
            duration_factor * 0.2 +
            area_factor * 0.2
        )

        # 风险等级
        if risk_score >= 0.8:
            risk_level = "high"
        elif risk_score >= 0.6:
            risk_level = "medium"
        else:
            risk_level = "low"

        return {
            "risk_score": risk_score,
            "risk_level": risk_level,
            "risk_factors": {
                "confidence": confidence_factor,
                "stability": stability_factor,
                "duration": duration_factor,
                "area": area_factor
            },
            "is_confirmed_leak": risk_score >= 0.7 and stability_analysis.get('is_stable', False)
        }

    def _analyze_dispersion_pattern(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析扩散模式"""
        dispersion_data = []

        for obj in track_data:
            dispersion_analysis = obj.get('dispersion_analysis', {})
            if dispersion_analysis:
                dispersion_data.append(dispersion_analysis)

        if not dispersion_data:
            return {"status": "no_dispersion_data"}

        # 分析扩散方向一致性
        directions = []
        expansion_trends = []

        for data in dispersion_data:
            direction = data.get('dispersion_direction', [])
            if direction and len(direction) >= 2:
                directions.append(direction)

            expansion = data.get('expansion_trend', 0)
            if expansion is not None:
                expansion_trends.append(expansion)

        return {
            "direction_consistency": self._calculate_direction_consistency(directions),
            "expansion_trend": {
                "average": np.mean(expansion_trends) if expansion_trends else 0,
                "pattern": self._classify_expansion_trend(expansion_trends)
            },
            "dispersion_stability": np.std(expansion_trends) if len(expansion_trends) > 1 else 0
        }

    def _calculate_direction_consistency(self, directions: List[List[float]]) -> float:
        """计算扩散方向一致性"""
        if len(directions) < 2:
            return 1.0

        # 计算方向向量的余弦相似度
        similarities = []

        for i in range(len(directions) - 1):
            v1 = np.array(directions[i])
            v2 = np.array(directions[i + 1])

            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)

            if norm1 > 1e-6 and norm2 > 1e-6:
                similarity = np.dot(v1, v2) / (norm1 * norm2)
                similarities.append(max(0, similarity)) # 确保非负

        return np.mean(similarities) if similarities else 0.0

    def _classify_expansion_trend(self, expansion_trends: List[float]) -> str:
        """分类扩张趋势"""
        if not expansion_trends:
            return "unknown"

        avg_expansion = np.mean(expansion_trends)

        if avg_expansion > 0.1:
            return "expanding"
        elif avg_expansion < -0.1:
            return "contracting"
        else:
            return "stable"

    def _analyze_camera_compensation(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析镜头补偿统计"""
        compensated_count = 0
        compensation_confidences = []

        for obj in track_data:
            if obj.get('camera_motion_compensated', False):
                compensated_count += 1
                conf = obj.get('compensation_confidence', 0)
                if conf > 0:
                    compensation_confidences.append(conf)

        total_frames = len(track_data)
        compensation_ratio = compensated_count / max(total_frames, 1)

        return {
            "compensation_ratio": compensation_ratio,
            "compensated_frames": compensated_count,
            "total_frames": total_frames,
            "average_compensation_confidence": np.mean(compensation_confidences) if compensation_confidences else 0.0,
            "compensation_effectiveness": "high" if compensation_ratio > 0.7 else "medium" if compensation_ratio > 0.3 else "low"
        }

    def _compute_overall_statistics(self,
                                  final_leakage_points: List[FinalLeakagePoint],
                                  cleaned_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算整体统计信息"""
        if not final_leakage_points:
            return {"status": "no_final_points"}

        # 基础统计
        total_points = len(final_leakage_points)
        confirmed_leaks = sum(1 for point in final_leakage_points
                            if point.risk_assessment.get('is_confirmed_leak', False))

        # 风险等级分布
        risk_distribution = defaultdict(int)
        for point in final_leakage_points:
            risk_level = point.risk_assessment.get('risk_level', 'unknown')
            risk_distribution[risk_level] += 1

        # 置信度统计
        avg_confidences = [point.confidence_stats.get('average', 0) for point in final_leakage_points]

        # 持续时间统计
        durations = [point.temporal_info.get('duration_seconds', 0) for point in final_leakage_points]

        # 面积统计
        avg_areas = [point.spatial_info.get('area_stats', {}).get('average', 0)
                    for point in final_leakage_points
                    if point.spatial_info.get('area_stats')]

        return {
            "total_leakage_points": total_points,
            "confirmed_leaks": confirmed_leaks,
            "confirmation_rate": confirmed_leaks / max(total_points, 1),
            "risk_distribution": dict(risk_distribution),
            "confidence_statistics": {
                "average": np.mean(avg_confidences),
                "max": np.max(avg_confidences),
                "min": np.min(avg_confidences),
                "std": np.std(avg_confidences)
            },
            "duration_statistics": {
                "average_seconds": np.mean(durations),
                "max_seconds": np.max(durations),
                "total_seconds": np.sum(durations)
            },
            "area_statistics": {
                "average_area": np.mean(avg_areas) if avg_areas else 0,
                "total_area": np.sum(avg_areas) if avg_areas else 0
            },
            "frames_processed": len(cleaned_data)
        }

    def _assess_overall_risk(self,
                           final_leakage_points: List[FinalLeakagePoint],
                           overall_statistics: Dict[str, Any]) -> Dict[str, Any]:
        """评估整体风险"""
        if not final_leakage_points:
            return {"overall_risk_level": "none", "risk_score": 0.0}

        # 风险因子
        confirmed_leaks = overall_statistics.get('confirmed_leaks', 0)
        total_points = overall_statistics.get('total_leakage_points', 1)
        avg_confidence = overall_statistics.get('confidence_statistics', {}).get('average', 0)
        total_duration = overall_statistics.get('duration_statistics', {}).get('total_seconds', 0)

        # 高风险点数量
        high_risk_count = overall_statistics.get('risk_distribution', {}).get('high', 0)

        # 综合风险评分
        confirmation_factor = confirmed_leaks / max(total_points, 1)
        confidence_factor = min(avg_confidence / 0.8, 1.0)
        duration_factor = min(total_duration / 300.0, 1.0) # 5分钟归一化
        high_risk_factor = high_risk_count / max(total_points, 1)

        overall_risk_score = (
            confirmation_factor * 0.4 +
            confidence_factor * 0.2 +
            duration_factor * 0.2 +
            high_risk_factor * 0.2
        )

        # 风险等级
        if overall_risk_score >= 0.8:
            risk_level = "critical"
        elif overall_risk_score >= 0.6:
            risk_level = "high"
        elif overall_risk_score >= 0.4:
            risk_level = "medium"
        else:
            risk_level = "low"

        return {
            "overall_risk_level": risk_level,
            "risk_score": overall_risk_score,
            "risk_factors": {
                "confirmation_rate": confirmation_factor,
                "confidence": confidence_factor,
                "duration": duration_factor,
                "high_risk_points": high_risk_factor
            },
            "recommendations": self._generate_risk_recommendations(risk_level, overall_statistics)
        }

    def _generate_risk_recommendations(self, risk_level: str, statistics: Dict[str, Any]) -> List[str]:
        """生成风险建议"""
        recommendations = []

        if risk_level in ["critical", "high"]:
            recommendations.append("立即进行现场检查和维修")
            recommendations.append("加强监控频率")
            recommendations.append("考虑暂停相关操作")
        elif risk_level == "medium":
            recommendations.append("计划定期检查")
            recommendations.append("监控泄漏发展趋势")
            recommendations.append("准备应急预案")
        else:
            recommendations.append("继续常规监控")
            recommendations.append("定期回顾检测结果")

        # 基于统计信息的特定建议
        confirmed_leaks = statistics.get('confirmed_leaks', 0)
        if confirmed_leaks > 0:
            recommendations.append(f"发现{confirmed_leaks}个确认泄漏点，需要重点关注")

        return recommendations

    def _analyze_temporal_patterns(self,
                                 cleaned_data: List[Dict[str, Any]],
                                 video_info: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """分析时间模式"""
        if not cleaned_data:
            return {"status": "no_temporal_data"}

        fps = video_info.get('fps', 30.0) if video_info else 30.0

        # 提取时间相关数据
        frame_numbers = [result['frame_number'] for result in cleaned_data]
        detection_counts = [len(result['result'].get('tracked_objects', [])) for result in cleaned_data]

        # 时间统计
        start_frame = min(frame_numbers)
        end_frame = max(frame_numbers)
        total_duration = (end_frame - start_frame) / fps

        # 检测密度分析
        avg_detections_per_frame = np.mean(detection_counts)
        max_simultaneous_detections = max(detection_counts)

        # 活跃期分析
        active_periods = self._identify_active_periods(detection_counts, frame_numbers, fps)

        return {
            "duration_analysis": {
                "total_duration_seconds": total_duration,
                "start_frame": start_frame,
                "end_frame": end_frame,
                "effective_monitoring_time": len(cleaned_data) / fps
            },
            "detection_frequency": {
                "average_detections_per_frame": avg_detections_per_frame,
                "max_simultaneous_detections": max_simultaneous_detections,
                "detection_rate": np.sum(detection_counts) / len(cleaned_data)
            },
            "active_periods": active_periods,
            "temporal_consistency": self._calculate_temporal_consistency(detection_counts)
        }

    def _identify_active_periods(self,
                               detection_counts: List[int],
                               frame_numbers: List[int],
                               fps: float) -> List[Dict[str, Any]]:
        """识别活跃期间"""
        active_periods = []
        current_period_start = None
        current_period_detections = []

        for i, (frame_num, count) in enumerate(zip(frame_numbers, detection_counts)):
            if count > 0:
                if current_period_start is None:
                    current_period_start = frame_num
                current_period_detections.append(count)
            else:
                if current_period_start is not None:
                    # 结束当前活跃期
                    period_end = frame_numbers[i-1] if i > 0 else frame_num
                    duration = (period_end - current_period_start) / fps

                    active_periods.append({
                        "start_frame": current_period_start,
                        "end_frame": period_end,
                        "duration_seconds": duration,
                        "average_detections": np.mean(current_period_detections),
                        "max_detections": max(current_period_detections),
                        "total_detections": sum(current_period_detections)
                    })

                    current_period_start = None
                    current_period_detections = []

        # 处理最后一个活跃期
        if current_period_start is not None:
            period_end = frame_numbers[-1]
            duration = (period_end - current_period_start) / fps

            active_periods.append({
                "start_frame": current_period_start,
                "end_frame": period_end,
                "duration_seconds": duration,
                "average_detections": np.mean(current_period_detections),
                "max_detections": max(current_period_detections),
                "total_detections": sum(current_period_detections)
            })

        return active_periods

    def _calculate_temporal_consistency(self, detection_counts: List[int]) -> float:
        """计算时间一致性"""
        if len(detection_counts) < 2:
            return 1.0

        # 计算检测数量的变异系数
        mean_count = np.mean(detection_counts)
        if mean_count == 0:
            return 1.0

        cv = np.std(detection_counts) / mean_count
        consistency = max(0.0, 1.0 - cv)

        return consistency

    def _analyze_spatial_patterns(self, final_leakage_points: List[FinalLeakagePoint]) -> Dict[str, Any]:
        """分析空间模式"""
        if not final_leakage_points:
            return {"status": "no_spatial_data"}

        # 提取中心点
        centers = []
        for point in final_leakage_points:
            trajectory = point.trajectory_summary
            if trajectory and 'center_of_mass' in trajectory:
                centers.append(trajectory['center_of_mass'])

        if not centers:
            return {"status": "no_center_data"}

        centers_array = np.array(centers)

        # 空间分布分析
        spatial_distribution = {
            "center_of_distribution": np.mean(centers_array, axis=0).tolist(),
            "distribution_spread": {
                "x_range": float(np.max(centers_array[:, 0]) - np.min(centers_array[:, 0])),
                "y_range": float(np.max(centers_array[:, 1]) - np.min(centers_array[:, 1]))
            },
            "clustering_analysis": self._analyze_clustering(centers_array)
        }

        return spatial_distribution

    def _analyze_clustering(self, centers: np.ndarray) -> Dict[str, Any]:
        """分析聚类模式"""
        if len(centers) < 2:
            return {"pattern": "single_point"}

        # 计算点间距离
        distances = []
        for i in range(len(centers)):
            for j in range(i + 1, len(centers)):
                distance = np.linalg.norm(centers[i] - centers[j])
                distances.append(distance)

        avg_distance = np.mean(distances)
        min_distance = np.min(distances)

        # 聚类判断
        if min_distance < self.spatial_merge_threshold:
            pattern = "clustered"
        elif avg_distance > self.spatial_merge_threshold * 3:
            pattern = "dispersed"
        else:
            pattern = "distributed"

        return {
            "pattern": pattern,
            "average_distance": avg_distance,
            "min_distance": min_distance,
            "max_distance": np.max(distances),
            "potential_clusters": self._identify_potential_clusters(centers)
        }

    def _identify_potential_clusters(self, centers: np.ndarray) -> int:
        """识别潜在聚类数量"""
        if len(centers) < 2:
            return 1

        # 简单的基于距离的聚类
        clusters = []
        used_points = set()

        for i, center in enumerate(centers):
            if i in used_points:
                continue

            cluster = [i]
            used_points.add(i)

            for j, other_center in enumerate(centers):
                if j in used_points:
                    continue

                distance = np.linalg.norm(center - other_center)
                if distance < self.spatial_merge_threshold:
                    cluster.append(j)
                    used_points.add(j)

            clusters.append(cluster)

        return len(clusters)
    
    def _identify_active_periods_for_track(self, track_data: List[Dict[str, Any]], fps: float) -> List[Dict[str, Any]]:
        """识别单个轨迹的活跃期间（任务2.1）"""
        if not track_data:
            return []
        
        # 按帧号排序
        sorted_data = sorted(track_data, key=lambda x: x.get('frame_number', 0))
        
        active_periods = []
        current_period_start = None
        current_period_data = []
        
        # 设置置信度阈值
        confidence_threshold = 0.5
        
        for i, obj in enumerate(sorted_data):
            frame_num = obj.get('frame_number', 0)
            confidence = obj.get('confidence', 0)
            
            if confidence >= confidence_threshold:
                if current_period_start is None:
                    current_period_start = frame_num
                current_period_data.append(obj)
            else:
                if current_period_start is not None:
                    # 结束当前活跃期
                    period_end = sorted_data[i-1].get('frame_number', frame_num) if i > 0 else frame_num
                    duration = (period_end - current_period_start) / fps if fps > 0 else 0
                    
                    confidences = [d.get('confidence', 0) for d in current_period_data]
                    
                    active_periods.append({
                        "start_frame": current_period_start,
                        "end_frame": period_end,
                        "start_seconds": current_period_start / fps if fps > 0 else 0,
                        "end_seconds": period_end / fps if fps > 0 else 0,
                        "duration_seconds": duration,
                        "frame_count": len(current_period_data),
                        "average_confidence": np.mean(confidences),
                        "max_confidence": np.max(confidences),
                        "min_confidence": np.min(confidences)
                    })
                    
                    current_period_start = None
                    current_period_data = []
        
        # 处理最后一个活跃期
        if current_period_start is not None and current_period_data:
            period_end = sorted_data[-1].get('frame_number', current_period_start)
            duration = (period_end - current_period_start) / fps if fps > 0 else 0
            
            confidences = [d.get('confidence', 0) for d in current_period_data]
            
            active_periods.append({
                "start_frame": current_period_start,
                "end_frame": period_end,
                "start_seconds": current_period_start / fps if fps > 0 else 0,
                "end_seconds": period_end / fps if fps > 0 else 0,
                "duration_seconds": duration,
                "frame_count": len(current_period_data),
                "average_confidence": np.mean(confidences),
                "max_confidence": np.max(confidences),
                "min_confidence": np.min(confidences)
            })
        
        return active_periods
    
    def _analyze_leakage_continuity(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析泄漏连续性（任务2.1）"""
        if not track_data:
            return {"status": "no_data"}
        
        # 按帧号排序
        sorted_data = sorted(track_data, key=lambda x: x.get('frame_number', 0))
        
        # 计算帧间隔
        frame_gaps = []
        for i in range(1, len(sorted_data)):
            gap = sorted_data[i].get('frame_number', 0) - sorted_data[i-1].get('frame_number', 0)
            frame_gaps.append(gap)
        
        # 分析连续性
        total_gaps = len(frame_gaps)
        continuous_gaps = len([gap for gap in frame_gaps if gap <= 2])  # 允许1-2帧的间隔
        interruptions = len([gap for gap in frame_gaps if gap > 5])  # 超过5帧视为中断
        
        continuity_ratio = continuous_gaps / max(total_gaps, 1)
        
        # 计算置信度稳定性
        confidences = [obj.get('confidence', 0) for obj in sorted_data]
        confidence_stability = 1.0 - (np.std(confidences) / max(np.mean(confidences), 0.1))
        
        return {
            "is_continuous": continuity_ratio > 0.8,
            "continuity_ratio": continuity_ratio,
            "interruption_count": interruptions,
            "average_frame_gap": np.mean(frame_gaps) if frame_gaps else 1.0,
            "max_frame_gap": np.max(frame_gaps) if frame_gaps else 1.0,
            "confidence_stability": max(0.0, min(1.0, confidence_stability)),
            "total_detections": len(sorted_data)
        }
    
    def _classify_dispersion_behavior(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分类气体扩散行为（任务2.2）"""
        if not track_data or len(track_data) < 3:
            return {
                "behavior_type": "insufficient_data",
                "description": "数据不足，无法分析扩散行为",
                "confidence": 0.0
            }
        
        # 按帧号排序
        sorted_data = sorted(track_data, key=lambda x: x.get('frame_number', 0))
        
        # 提取位置和面积数据
        positions = []
        areas = []
        
        for obj in sorted_data:
            bbox = obj.get('bbox', {})
            if bbox:
                center_x = bbox.get('x', 0) + bbox.get('width', 0) / 2
                center_y = bbox.get('y', 0) + bbox.get('height', 0) / 2
                area = bbox.get('width', 0) * bbox.get('height', 0)
                
                positions.append((center_x, center_y))
                areas.append(area)
        
        if len(positions) < 3:
            return {
                "behavior_type": "insufficient_data",
                "description": "位置数据不足，无法分析扩散行为",
                "confidence": 0.0
            }
        
        # 计算位置变化和面积变化
        position_changes = []
        area_changes = []
        
        for i in range(1, len(positions)):
            pos_change = np.sqrt((positions[i][0] - positions[i-1][0])**2 + 
                               (positions[i][1] - positions[i-1][1])**2)
            position_changes.append(pos_change)
            
            if i < len(areas):
                area_change = abs(areas[i] - areas[i-1]) / max(areas[i-1], 1)
                area_changes.append(area_change)
        
        # 计算统计指标
        avg_position_change = np.mean(position_changes)
        std_position_change = np.std(position_changes)
        avg_area_change = np.mean(area_changes) if area_changes else 0
        area_growth_trend = (areas[-1] - areas[0]) / max(areas[0], 1) if areas else 0
        
        # 计算方向性
        direction_vectors = []
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            if dx != 0 or dy != 0:
                direction_vectors.append((dx, dy))
        
        # 计算方向一致性
        direction_consistency = 0.0
        if len(direction_vectors) > 1:
            angles = []
            for i in range(1, len(direction_vectors)):
                v1 = np.array(direction_vectors[i-1])
                v2 = np.array(direction_vectors[i])
                
                # 计算角度差
                dot_product = np.dot(v1, v2)
                norms = np.linalg.norm(v1) * np.linalg.norm(v2)
                if norms > 0:
                    cos_angle = dot_product / norms
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle = np.arccos(cos_angle)
                    angles.append(angle)
            
            if angles:
                direction_consistency = 1.0 - (np.mean(angles) / np.pi)
        
        # 分类逻辑
        behavior_type = "irregular"
        description = "不规则扩散"
        confidence = 0.5
        
        # 静止型：位置变化小，面积变化也小
        if avg_position_change < 10 and avg_area_change < 0.1:
            behavior_type = "stationary"
            description = "气体在固定区域内静止或缓慢扩散"
            confidence = 0.8
        
        # 扩散型：面积持续增长，位置变化适中
        elif area_growth_trend > 0.2 and avg_area_change > 0.1:
            behavior_type = "expansion"
            description = "气体向四周均匀扩散，覆盖面积持续增长"
            confidence = 0.7
        
        # 定向型：方向一致性高，位置变化明显
        elif direction_consistency > 0.6 and avg_position_change > 15:
            behavior_type = "directional"
            description = "气体沿特定方向定向移动或扩散"
            confidence = 0.75
        
        # 不规则型：位置变化大但方向不一致
        elif std_position_change > avg_position_change * 0.5:
            behavior_type = "irregular"
            description = "气体扩散模式不规则，可能受风向或障碍物影响"
            confidence = 0.6
        
        return {
             "behavior_type": behavior_type,
             "description": description,
             "confidence": confidence,
             "metrics": {
                 "average_position_change": avg_position_change,
                 "position_change_stability": 1.0 - (std_position_change / max(avg_position_change, 1)),
                 "average_area_change": avg_area_change,
                 "area_growth_trend": area_growth_trend,
                 "direction_consistency": direction_consistency,
                 "total_frames": len(sorted_data)
             }
         }
    
    def _calculate_confidence_distribution(self, track_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算置信度区间统计（任务2.3）"""
        if not track_data:
            return {
                "total_frames": 0,
                "average_confidence": 0.0,
                "confidence_stability": 0.0,
                "distribution": {},
                "percentiles": {}
            }
        
        # 提取所有置信度值
        confidences = [obj.get('confidence', 0.0) for obj in track_data]
        confidences = [c for c in confidences if c > 0]  # 过滤无效值
        
        if not confidences:
            return {
                "total_frames": len(track_data),
                "average_confidence": 0.0,
                "confidence_stability": 0.0,
                "distribution": {},
                "percentiles": {}
            }
        
        # 定义置信度区间
        intervals = [
            (0.0, 0.3, "low"),
            (0.3, 0.5, "medium_low"),
            (0.5, 0.7, "medium"),
            (0.7, 0.8, "medium_high"),
            (0.8, 0.9, "high"),
            (0.9, 1.0, "very_high")
        ]
        
        # 统计各区间的帧数
        distribution = {}
        for min_conf, max_conf, label in intervals:
            count = len([c for c in confidences if min_conf <= c < max_conf])
            # 处理最后一个区间包含1.0的情况
            if label == "very_high":
                count = len([c for c in confidences if min_conf <= c <= max_conf])
            
            distribution[label] = {
                "range": f"{min_conf:.1f}-{max_conf:.1f}",
                "count": count,
                "percentage": (count / len(confidences)) * 100 if confidences else 0
            }
        
        # 计算基本统计指标
        avg_confidence = np.mean(confidences)
        std_confidence = np.std(confidences)
        min_confidence = np.min(confidences)
        max_confidence = np.max(confidences)
        
        # 计算置信度稳定性（变异系数的倒数）
        confidence_stability = 1.0 - (std_confidence / max(avg_confidence, 0.1))
        confidence_stability = max(0.0, min(1.0, confidence_stability))
        
        # 计算百分位数
        percentiles = {
            "p25": np.percentile(confidences, 25),
            "p50": np.percentile(confidences, 50),  # 中位数
            "p75": np.percentile(confidences, 75),
            "p90": np.percentile(confidences, 90),
            "p95": np.percentile(confidences, 95)
        }
        
        # 计算置信度趋势（线性回归斜率）
        if len(confidences) > 1:
            x = np.arange(len(confidences))
            slope, _ = np.polyfit(x, confidences, 1)
            confidence_trend = "increasing" if slope > 0.001 else "decreasing" if slope < -0.001 else "stable"
        else:
            slope = 0.0
            confidence_trend = "stable"
        
        return {
            "total_frames": len(confidences),
            "average_confidence": float(avg_confidence),
            "min_confidence": float(min_confidence),
            "max_confidence": float(max_confidence),
            "std_confidence": float(std_confidence),
            "confidence_stability": float(confidence_stability),
            "confidence_trend": confidence_trend,
            "trend_slope": float(slope),
            "distribution": distribution,
            "percentiles": {k: float(v) for k, v in percentiles.items()},
            "quality_assessment": {
                "high_confidence_ratio": (distribution["high"]["count"] + distribution["very_high"]["count"]) / len(confidences),
                "low_confidence_ratio": (distribution["low"]["count"] + distribution["medium_low"]["count"]) / len(confidences),
                "is_reliable": avg_confidence > 0.7 and confidence_stability > 0.6
            }
        }

    def _analyze_camera_motion(self, cleaned_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析镜头运动"""
        camera_motion_data = []

        for frame_result in cleaned_data:
            result = frame_result.get('result', {})
            camera_motion = result.get('camera_motion', {})

            if camera_motion and isinstance(camera_motion, dict):
                camera_motion_data.append(camera_motion)

        if not camera_motion_data:
            return {"status": "no_camera_motion_data"}

        # 提取运动统计
        confidences = [cm.get('confidence', 0) for cm in camera_motion_data]
        displacements = []

        for cm in camera_motion_data:
            displacement = cm.get('displacement_magnitude', 0)
            if displacement is not None:
                displacements.append(displacement)

        return {
            "motion_detection_rate": len([c for c in confidences if c > 0.3]) / len(confidences),
            "average_motion_confidence": np.mean(confidences),
            "displacement_statistics": {
                "average": np.mean(displacements) if displacements else 0,
                "max": np.max(displacements) if displacements else 0,
                "total": np.sum(displacements) if displacements else 0
            },
            "stability_assessment": self._assess_camera_stability(confidences, displacements)
        }

    def _assess_camera_stability(self, confidences: List[float], displacements: List[float]) -> str:
        """评估镜头稳定性"""
        avg_confidence = np.mean(confidences) if confidences else 0
        avg_displacement = np.mean(displacements) if displacements else 0

        if avg_confidence < 0.2 and avg_displacement < 10:
            return "very_stable"
        elif avg_confidence < 0.4 and avg_displacement < 25:
            return "stable"
        elif avg_confidence < 0.6 and avg_displacement < 50:
            return "moderate"
        else:
            return "unstable"

    def _compute_quality_metrics(self,
                               cleaned_data: List[Dict[str, Any]],
                               final_leakage_points: List[FinalLeakagePoint]) -> Dict[str, Any]:
        """计算质量指标"""

        # 数据完整性指标
        total_frames = len(cleaned_data)
        frames_with_detections = len([d for d in cleaned_data
                                    if len(d['result'].get('tracked_objects', [])) > 0])

        # 追踪质量指标
        if final_leakage_points:
            avg_confidence = np.mean([point.confidence_stats.get('average', 0)
                                    for point in final_leakage_points])
            avg_stability = np.mean([point.stability_analysis.get('average_stability', 0)
                                   for point in final_leakage_points])
        else:
            avg_confidence = 0.0
            avg_stability = 0.0

        # 算法性能指标
        processing_times = []
        for frame_result in cleaned_data:
            result = frame_result.get('result', {})
            proc_time = result.get('processing_time', 0)
            if proc_time > 0:
                processing_times.append(proc_time)

        return {
            "data_completeness": {
                "total_frames": total_frames,
                "frames_with_detections": frames_with_detections,
                "detection_rate": frames_with_detections / max(total_frames, 1)
            },
            "tracking_quality": {
                "average_confidence": avg_confidence,
                "average_stability": avg_stability,
                "confidence_grade": self._grade_confidence(avg_confidence),
                "stability_grade": self._grade_stability(avg_stability)
            },
            "performance_metrics": {
                "average_processing_time": np.mean(processing_times) if processing_times else 0,
                "max_processing_time": np.max(processing_times) if processing_times else 0,
                "processing_efficiency": "high" if np.mean(processing_times) < 0.1 else "medium" if np.mean(processing_times) < 0.5 else "low"
            }
        }

    def _grade_confidence(self, avg_confidence: float) -> str:
        """给置信度打分"""
        if avg_confidence >= 0.8:
            return "excellent"
        elif avg_confidence >= 0.6:
            return "good"
        elif avg_confidence >= 0.4:
            return "fair"
        else:
            return "poor"

    def _grade_stability(self, avg_stability: float) -> str:
        """给稳定性打分"""
        if avg_stability >= 0.8:
            return "excellent"
        elif avg_stability >= 0.6:
            return "good"
        elif avg_stability >= 0.4:
            return "fair"
        else:
            return "poor"

    def _create_empty_summary(self, status: str, error_message: str = None) -> GasTrackingFinalSummary:
        """创建空的汇总结果"""
        return GasTrackingFinalSummary(
            processing_info={
                "status": status,
                "error_message": error_message,
                "timestamp": time.time(),
                "algorithm_version": "GasFinalizer v1.0"
            },
            overall_statistics={},
            risk_assessment={"overall_risk_level": "none", "risk_score": 0.0},
            final_leakage_points=[],
            temporal_analysis={},
            spatial_analysis={},
            camera_motion_analysis={},
            quality_metrics={}
        )

    def export_summary_to_dict(self, summary: GasTrackingFinalSummary) -> Dict[str, Any]:
        """将汇总结果导出为字典格式"""
        return {
            "processing_info": summary.processing_info,
            "overall_statistics": summary.overall_statistics,
            "risk_assessment": summary.risk_assessment,
            "final_leakage_points": [
                {
                    "track_id": point.track_id,
                    "class_name": point.class_name,
                    "confidence_stats": point.confidence_stats,
                    "trajectory_summary": point.trajectory_summary,
                    "temporal_info": point.temporal_info,
                    "spatial_info": point.spatial_info,
                    "stability_analysis": point.stability_analysis,
                    "risk_assessment": point.risk_assessment,
                    "dispersion_analysis": point.dispersion_analysis,
                    "camera_compensation_stats": point.camera_compensation_stats
                }
                for point in summary.final_leakage_points
            ],
            "temporal_analysis": summary.temporal_analysis,
            "spatial_analysis": summary.spatial_analysis,
            "camera_motion_analysis": summary.camera_motion_analysis,
            "quality_metrics": summary.quality_metrics
        }

    def export_to_json(self, summary: GasTrackingFinalSummary, output_path: str) -> bool:
        """导出到JSON文件"""
        try:
            summary_dict = self.export_summary_to_dict(summary)

            # 修复：使用自定义JSON编码器处理numpy和bool类型
            import json
            import numpy as np

            class CustomJSONEncoder(json.JSONEncoder):
                def default(self, obj):
                    if isinstance(obj, np.ndarray):
                        return obj.tolist()
                    elif isinstance(obj, np.bool_):
                        return bool(obj)
                    elif isinstance(obj, (np.int_, np.intc, np.intp, np.int8,
                                        np.int16, np.int32, np.int64, np.uint8,
                                        np.uint16, np.uint32, np.uint64)):
                        return int(obj)
                    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
                        return float(obj)
                    return super().default(obj)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(summary_dict, f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)

            logger.info(f" 最终汇总数据已导出到: {output_path}")
            return True

        except Exception as e:
            logger.error(f" 导出JSON文件失败: {e}")
            return False

    def export_to_csv(self, summary: GasTrackingFinalSummary, output_path: str) -> bool:
        """导出最终泄漏点到CSV文件"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if not summary.final_leakage_points:
                    csvfile.write("No leakage points detected\n")
                    return True

                fieldnames = [
                    'track_id', 'class_name', 'avg_confidence', 'max_confidence',
                    'duration_seconds', 'total_frames', 'is_stable', 'risk_level',
                    'risk_score', 'start_x', 'start_y', 'end_x', 'end_y',
                    'total_distance', 'movement_pattern', 'average_area',
                    'expansion_pattern', 'compensation_ratio'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for point in summary.final_leakage_points:
                    trajectory = point.trajectory_summary
                    row = {
                        'track_id': point.track_id,
                        'class_name': point.class_name,
                        'avg_confidence': round(point.confidence_stats.get('average', 0), 3),
                        'max_confidence': round(point.confidence_stats.get('max', 0), 3),
                        'duration_seconds': round(point.temporal_info.get('duration_seconds', 0), 2),
                        'total_frames': point.temporal_info.get('total_frames', 0),
                        'is_stable': point.stability_analysis.get('is_stable', False),
                        'risk_level': point.risk_assessment.get('risk_level', 'unknown'),
                        'risk_score': round(point.risk_assessment.get('risk_score', 0), 3),
                        'start_x': round(trajectory.get('start_position', [0, 0])[0], 1) if trajectory.get('start_position') else 0,
                        'start_y': round(trajectory.get('start_position', [0, 0])[1], 1) if trajectory.get('start_position') else 0,
                        'end_x': round(trajectory.get('end_position', [0, 0])[0], 1) if trajectory.get('end_position') else 0,
                        'end_y': round(trajectory.get('end_position', [0, 0])[1], 1) if trajectory.get('end_position') else 0,
                        'total_distance': round(trajectory.get('total_distance', 0), 1) if trajectory else 0,
                        'movement_pattern': trajectory.get('movement_pattern', 'unknown') if trajectory else 'unknown',
                        'average_area': round(point.spatial_info.get('area_stats', {}).get('average', 0), 1),
                        'expansion_pattern': point.spatial_info.get('expansion_pattern', 'unknown'),
                        'compensation_ratio': round(point.camera_compensation_stats.get('compensation_ratio', 0), 3)
                    }
                    writer.writerow(row)

            logger.info(f" 最终泄漏点数据已导出到CSV: {output_path}")
            return True

        except Exception as e:
            logger.error(f" 导出CSV文件失败: {e}")
            return False


# 便捷函数
def process_final_gas_tracking_data(gas_tracking_results: List[Dict[str, Any]],
                                   video_info: Optional[Dict[str, Any]] = None,
                                   output_dir: Optional[str] = None) -> GasTrackingFinalSummary:
    """
    便捷函数：处理最终气体追踪数据

    Args:
        gas_tracking_results: 气体追踪结果列表
        video_info: 视频信息
        output_dir: 输出目录（可选）

    Returns:
        最终汇总结果
    """
    finalizer = GasTrackingFinalizer()
    summary = finalizer.process_final_gas_tracking_data(gas_tracking_results, video_info)

    # 如果指定了输出目录，导出文件
    if output_dir and summary.processing_info.get('status') == 'success':
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        timestamp = int(time.time())
        json_path = Path(output_dir) / f"gas_tracking_final_summary_{timestamp}.json"
        csv_path = Path(output_dir) / f"gas_tracking_leakage_points_{timestamp}.csv"

        finalizer.export_to_json(summary, str(json_path))
        finalizer.export_to_csv(summary, str(csv_path))

    return summary


if __name__ == "__main__":
    # 示例使用
    logger.basicConfig(level=logging.INFO)

    # 模拟数据
    sample_data = [
        {
            "frame_number": i,
            "timestamp": i / 30.0,
            "result": {
                "tracked_objects": [
                    {
                        "track_id": 1,
                        "class_name": "gas_leak",
                        "confidence": 0.8 + 0.1 * np.sin(i * 0.1),
                        "center": [100 + i, 200],
                        "area": 1000 + 100 * np.sin(i * 0.05),
                        "total_lifetime": i,
                        "stability_score": 0.7,
                        "is_stable": True
                    }
                ]
            }
        }
        for i in range(100)
    ]

    video_info = {"fps": 30.0, "width": 1920, "height": 1080}

    summary = process_final_gas_tracking_data(sample_data, video_info, "output")
    print(f"处理完成，状态: {summary.processing_info['status']}")
    print(f"检测到 {len(summary.final_leakage_points)} 个最终泄漏点")