#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
无人机镜头运动补偿器
Drone Camera Motion Compensator
==============================

专门处理无人机飞行过程中的镜头位移，包括：
1. 光流法运动估计
2. 特征点匹配
3. 仿射变换计算
4. 实时运动补偿
5. 多种运动模式识别
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from collections import deque
import time

logger = logging.getLogger(__name__)


@dataclass
class CameraMotion:
    """镜头运动信息数据结构"""
    translation: Tuple[float, float] = (0.0, 0.0) # 平移向量 (dx, dy)
    rotation: float = 0.0 # 旋转角度 (弧度)
    scale: float = 1.0 # 缩放比例
    confidence: float = 0.0 # 运动估计置信度
    motion_type: str = "unknown" # 运动类型
    feature_count: int = 0 # 用于估计的特征点数量
    estimation_method: str = "optical_flow" # 估计方法


class CameraMotionCompensator:
    """
    无人机镜头运动补偿器
    Drone Camera Motion Compensator
    """

    def __init__(self,
                 detection_method: str = "optical_flow", # optical_flow, feature_matching, hybrid
                 max_features: int = 1000,
                 feature_quality: float = 0.01,
                 min_distance: float = 10,
                 block_size: int = 3,
                 use_harris_detector: bool = True,
                 lk_winsize: Tuple[int, int] = (15, 15),
                 lk_maxlevel: int = 2,
                 lk_criteria: Tuple = (cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03),
                 min_feature_ratio: float = 0.3,
                 max_motion_threshold: float = 100.0):
        """
        初始化镜头运动补偿器

        Args:
            detection_method: 运动检测方法
            max_features: 最大特征点数
            feature_quality: 特征点质量阈值
            min_distance: 特征点最小距离
            block_size: 特征检测块大小
            use_harris_detector: 是否使用Harris角点检测
            lk_winsize: LK光流窗口大小
            lk_maxlevel: LK金字塔层数
            lk_criteria: LK迭代终止条件
            min_feature_ratio: 最小有效特征比例
            max_motion_threshold: 最大运动阈值
        """
        self.detection_method = detection_method
        self.max_features = max_features
        self.feature_quality = feature_quality
        self.min_distance = min_distance
        self.block_size = block_size
        self.use_harris_detector = use_harris_detector
        self.lk_winsize = lk_winsize
        self.lk_maxlevel = lk_maxlevel
        self.lk_criteria = lk_criteria
        self.min_feature_ratio = min_feature_ratio
        self.max_motion_threshold = max_motion_threshold

        # 状态变量
        self.prev_frame = None
        self.prev_gray = None
        self.prev_features = None
        self.motion_history = deque(maxlen=30)

        # 运动滤波器
        self.motion_filter = MotionFilter()

        # 统计信息
        self.frame_count = 0
        self.successful_estimations = 0

        logger.info(f"镜头运动补偿器初始化: 方法={detection_method}, 最大特征={max_features}")

    def detect_camera_motion(self, frame: np.ndarray) -> CameraMotion:
        """
        检测镜头运动

        Args:
            frame: 当前帧

        Returns:
            CameraMotion: 运动信息
        """
        self.frame_count += 1

        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        motion = CameraMotion()

        if self.prev_gray is None:
            # 第一帧，初始化
            self.prev_gray = gray.copy()
            self.prev_frame = frame.copy()
            self._detect_features(gray)
            return motion

        try:
            if self.detection_method == "optical_flow":
                motion = self._detect_motion_optical_flow(gray)
            elif self.detection_method == "feature_matching":
                motion = self._detect_motion_feature_matching(gray)
            elif self.detection_method == "hybrid":
                motion = self._detect_motion_hybrid(gray)
            else:
                logger.warning(f"未知的运动检测方法: {self.detection_method}")

            # 应用运动滤波
            motion = self.motion_filter.filter_motion(motion)

            # 更新历史
            self.motion_history.append(motion)

            # 分析运动类型
            motion.motion_type = self._classify_motion_type(motion)

            if motion.confidence > 0.3:
                self.successful_estimations += 1

        except Exception as e:
            logger.warning(f"运动检测失败: {e}")
            motion.confidence = 0.0

        # 更新状态
        self.prev_gray = gray.copy()
        self.prev_frame = frame.copy()

        return motion

    def _detect_motion_optical_flow(self, gray: np.ndarray) -> CameraMotion:
        """使用光流法检测运动"""
        motion = CameraMotion(estimation_method="optical_flow")

        if self.prev_features is None or len(self.prev_features) < 10:
            self._detect_features(gray)
            return motion

        # Lucas-Kanade光流追踪
        new_features, status, error = cv2.calcOpticalFlowPyrLK(
            self.prev_gray, gray, self.prev_features, None,
            winSize=self.lk_winsize,
            maxLevel=self.lk_maxlevel,
            criteria=self.lk_criteria
        )

        # 过滤有效特征点
        good_old = self.prev_features[status == 1]
        good_new = new_features[status == 1]

        motion.feature_count = len(good_new)

        if len(good_new) < max(10, len(self.prev_features) * self.min_feature_ratio):
            # 特征点太少，重新检测
            self._detect_features(gray)
            motion.confidence = 0.1
            return motion

        # 计算运动参数
        try:
            # 使用RANSAC估计仿射变换
            transform_matrix, inliers = cv2.estimateAffinePartial2D(
                good_old, good_new,
                method=cv2.RANSAC,
                ransacReprojThreshold=3.0,
                confidence=0.99
            )

            if transform_matrix is not None and inliers is not None:
                # 提取运动参数
                dx = transform_matrix[0, 2]
                dy = transform_matrix[1, 2]
                scale = np.sqrt(transform_matrix[0, 0]**2 + transform_matrix[0, 1]**2)
                rotation = np.arctan2(transform_matrix[1, 0], transform_matrix[0, 0])

                # 检查运动是否合理
                motion_magnitude = np.sqrt(dx**2 + dy**2)
                if motion_magnitude < self.max_motion_threshold:
                    motion.translation = (float(dx), float(dy))
                    motion.rotation = float(rotation)
                    motion.scale = float(scale)

                    # 计算置信度
                    inlier_ratio = np.sum(inliers) / len(good_old)
                    motion.confidence = min(inlier_ratio * 1.2, 1.0)
                else:
                    motion.confidence = 0.0
            else:
                motion.confidence = 0.0

        except Exception as e:
            logger.warning(f"仿射变换估计失败: {e}")
            motion.confidence = 0.0

        # 更新特征点
        self.prev_features = good_new.reshape(-1, 1, 2)

        # 如果特征点数量减少太多，重新检测
        if len(self.prev_features) < self.max_features * 0.5:
            self._detect_features(gray)

        return motion

    def _detect_motion_feature_matching(self, gray: np.ndarray) -> CameraMotion:
        """使用特征匹配检测运动"""
        motion = CameraMotion(estimation_method="feature_matching")

        # ORB特征检测和匹配
        orb = cv2.ORB_create(nfeatures=self.max_features)

        # 检测关键点和描述符
        kp1, des1 = orb.detectAndCompute(self.prev_gray, None)
        kp2, des2 = orb.detectAndCompute(gray, None)

        if des1 is None or des2 is None:
            return motion

        # 特征匹配
        bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        matches = bf.match(des1, des2)
        matches = sorted(matches, key=lambda x: x.distance)

        motion.feature_count = len(matches)

        if len(matches) < 10:
            motion.confidence = 0.0
            return motion

        # 提取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)

        # 估计运动
        try:
            transform_matrix, inliers = cv2.estimateAffinePartial2D(
                src_pts, dst_pts,
                method=cv2.RANSAC,
                ransacReprojThreshold=3.0
            )

            if transform_matrix is not None:
                dx = transform_matrix[0, 2]
                dy = transform_matrix[1, 2]
                scale = np.sqrt(transform_matrix[0, 0]**2 + transform_matrix[0, 1]**2)
                rotation = np.arctan2(transform_matrix[1, 0], transform_matrix[0, 0])

                motion.translation = (float(dx), float(dy))
                motion.rotation = float(rotation)
                motion.scale = float(scale)

                if inliers is not None:
                    inlier_ratio = np.sum(inliers) / len(matches)
                    motion.confidence = min(inlier_ratio * 0.8, 1.0) # 特征匹配置信度稍低
                else:
                    motion.confidence = 0.3

        except Exception as e:
            logger.warning(f"特征匹配运动估计失败: {e}")
            motion.confidence = 0.0

        return motion

    def _detect_motion_hybrid(self, gray: np.ndarray) -> CameraMotion:
        """混合方法检测运动"""
        # 先尝试光流法
        optical_flow_motion = self._detect_motion_optical_flow(gray)

        # 如果光流法失败，使用特征匹配
        if optical_flow_motion.confidence < 0.3:
            feature_matching_motion = self._detect_motion_feature_matching(gray)

            if feature_matching_motion.confidence > optical_flow_motion.confidence:
                feature_matching_motion.estimation_method = "hybrid_feature"
                return feature_matching_motion

        optical_flow_motion.estimation_method = "hybrid_optical"
        return optical_flow_motion

    def _detect_features(self, gray: np.ndarray):
        """检测特征点"""
        # Shi-Tomasi角点检测
        features = cv2.goodFeaturesToTrack(
            gray,
            maxCorners=self.max_features,
            qualityLevel=self.feature_quality,
            minDistance=self.min_distance,
            blockSize=self.block_size,
            useHarrisDetector=self.use_harris_detector,
            k=0.04
        )

        self.prev_features = features

    def _classify_motion_type(self, motion: CameraMotion) -> str:
        """分类运动类型"""
        if motion.confidence < 0.3:
            return "uncertain"

        dx, dy = motion.translation
        motion_magnitude = np.sqrt(dx**2 + dy**2)
        rotation_deg = abs(np.degrees(motion.rotation))
        scale_change = abs(motion.scale - 1.0)

        # 分类运动
        if motion_magnitude < 2 and rotation_deg < 1 and scale_change < 0.02:
            return "static"
        elif rotation_deg > 5:
            return "rotation"
        elif scale_change > 0.05:
            return "zoom"
        elif motion_magnitude > 10:
            return "fast_translation"
        else:
            return "slow_translation"

    def get_motion_statistics(self) -> Dict[str, Any]:
        """获取运动统计信息"""
        if not self.motion_history:
            return {"status": "no_data"}

        recent_motions = list(self.motion_history)[-10:]

        # 计算平均运动
        avg_translation = (
            np.mean([m.translation[0] for m in recent_motions]),
            np.mean([m.translation[1] for m in recent_motions])
        )

        avg_confidence = np.mean([m.confidence for m in recent_motions])

        # 运动类型统计
        motion_types = [m.motion_type for m in recent_motions]
        type_counts = {t: motion_types.count(t) for t in set(motion_types)}

        return {
            "frame_count": self.frame_count,
            "successful_estimations": self.successful_estimations,
            "success_rate": self.successful_estimations / max(self.frame_count, 1),
            "avg_translation": avg_translation,
            "avg_confidence": float(avg_confidence),
            "motion_type_distribution": type_counts,
            "recent_motion_magnitude": float(np.sqrt(avg_translation[0]**2 + avg_translation[1]**2))
        }

    def reset(self):
        """重置补偿器状态"""
        self.prev_frame = None
        self.prev_gray = None
        self.prev_features = None
        self.motion_history.clear()
        self.motion_filter.reset()
        self.frame_count = 0
        self.successful_estimations = 0
        logger.info("镜头运动补偿器已重置")


class MotionFilter:
    """运动滤波器，用于平滑运动估计结果"""

    def __init__(self,
                 translation_alpha: float = 0.3,
                 rotation_alpha: float = 0.4,
                 scale_alpha: float = 0.5,
                 outlier_threshold: float = 3.0):
        """
        初始化运动滤波器

        Args:
            translation_alpha: 平移滤波系数
            rotation_alpha: 旋转滤波系数
            scale_alpha: 缩放滤波系数
            outlier_threshold: 异常值阈值
        """
        self.translation_alpha = translation_alpha
        self.rotation_alpha = rotation_alpha
        self.scale_alpha = scale_alpha
        self.outlier_threshold = outlier_threshold

        # 滤波状态
        self.filtered_translation = (0.0, 0.0)
        self.filtered_rotation = 0.0
        self.filtered_scale = 1.0

        # 历史用于异常检测
        self.translation_history = deque(maxlen=10)
        self.rotation_history = deque(maxlen=10)
        self.scale_history = deque(maxlen=10)

    def filter_motion(self, motion: CameraMotion) -> CameraMotion:
        """应用运动滤波"""
        if motion.confidence < 0.1:
            return motion

        # 异常值检测
        if self._is_outlier(motion):
            # 如果是异常值，降低置信度但不完全丢弃
            motion.confidence *= 0.3

        # 指数移动平均滤波
        self.filtered_translation = (
            self.translation_alpha * motion.translation[0] + (1 - self.translation_alpha) * self.filtered_translation[0],
            self.translation_alpha * motion.translation[1] + (1 - self.translation_alpha) * self.filtered_translation[1]
        )

        self.filtered_rotation = (
            self.rotation_alpha * motion.rotation + (1 - self.rotation_alpha) * self.filtered_rotation
        )

        self.filtered_scale = (
            self.scale_alpha * motion.scale + (1 - self.scale_alpha) * self.filtered_scale
        )

        # 更新历史
        self.translation_history.append(motion.translation)
        self.rotation_history.append(motion.rotation)
        self.scale_history.append(motion.scale)

        # 创建滤波后的运动
        filtered_motion = CameraMotion(
            translation=self.filtered_translation,
            rotation=self.filtered_rotation,
            scale=self.filtered_scale,
            confidence=motion.confidence,
            motion_type=motion.motion_type,
            feature_count=motion.feature_count,
            estimation_method=motion.estimation_method + "_filtered"
        )

        return filtered_motion

    def _is_outlier(self, motion: CameraMotion) -> bool:
        """检测是否为异常值"""
        if len(self.translation_history) < 3:
            return False

        # 计算平移的Z-score
        recent_translations = list(self.translation_history)[-5:]
        translation_magnitudes = [np.sqrt(t[0]**2 + t[1]**2) for t in recent_translations]

        if len(translation_magnitudes) > 1:
            mean_mag = np.mean(translation_magnitudes)
            std_mag = np.std(translation_magnitudes)

            current_mag = np.sqrt(motion.translation[0]**2 + motion.translation[1]**2)

            if std_mag > 0:
                z_score = abs(current_mag - mean_mag) / std_mag
                return z_score > self.outlier_threshold

        return False

    def reset(self):
        """重置滤波器"""
        self.filtered_translation = (0.0, 0.0)
        self.filtered_rotation = 0.0
        self.filtered_scale = 1.0
        self.translation_history.clear()
        self.rotation_history.clear()
        self.scale_history.clear()