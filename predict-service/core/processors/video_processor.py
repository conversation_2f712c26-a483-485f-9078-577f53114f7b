import logging
import cv2
import numpy as np
import torch
from ultralytics import YOL<PERSON>
from typing import Optional, Tuple, Dict, Any, List
import os
import time
import tempfile
import shutil
import threading
import queue
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import subprocess
import json
from dataclasses import dataclass, field
from collections import defaultdict, deque
import math
import yaml

from .frame_processor import FrameProcessor
from .enhanced_object_tracker import EnhancedObjectTracker
from utils.video_utils import annotate_frame, compute_confidence_map, apply_heatmap
from utils.video_codec_fallback import create_robust_video_writer

from utils.cuda_utils import safe_cuda_empty_cache

logger = logging.getLogger(__name__)
# 日志级别由配置文件统一管理，不在此处硬编码


@dataclass
class LeakageTrackInfo:
    """气体泄漏追踪信息数据结构（专门处理不规则气体形状）"""
    track_id: int # 追踪ID
    class_name: str # 检测类别名称
    confidence: float # 置信度
    contour: Optional[np.ndarray] = None # 气体轮廓（不规则形状）
    bbox: List[float] = field(default_factory=list) # 外接矩形 [x1, y1, x2, y2]
    center: Tuple[float, float] = (0.0, 0.0) # 质心坐标（基于轮廓计算）
    area: float = 0.0 # 实际气体面积（基于轮廓计算）
    perimeter: float = 0.0 # 气体轮廓周长
    shape_complexity: float = 0.0 # 形状复杂度（周长²/面积）
    track_history: deque = field(default_factory=lambda: deque(maxlen=50)) # 轨迹历史
    confidence_history: deque = field(default_factory=lambda: deque(maxlen=20)) # 置信度历史
    area_history: deque = field(default_factory=lambda: deque(maxlen=30)) # 面积变化历史
    velocity: Tuple[float, float] = (0.0, 0.0) # 速度向量 (vx, vy)
    expansion_rate: float = 0.0 # 气体扩散速率（面积变化率）
    first_detected_frame: int = 0 # 首次检测到的帧数
    last_seen_frame: int = 0 # 最后见到的帧数
    consecutive_frames: int = 0 # 连续出现的帧数
    is_stable: bool = False

    def update_tracking_info(self, contour: Optional[np.ndarray], confidence: float,
                             frame_num: int, min_stable_frames: int = 5):
        """
        更新气体追踪信息（基于轮廓的不规则形状处理）

        Args:
            contour: 气体轮廓点集
            confidence: 新的置信度
            frame_num: 当前帧数
            min_stable_frames: 判定为稳定目标的最小连续帧数
        """
        old_center = self.center
        old_area = self.area

        self.confidence = confidence
        self.contour = contour

        if contour is not None and len(contour) > 0:
            # 计算外接矩形
            x, y, w, h = cv2.boundingRect(contour)
            self.bbox = [float(x), float(y), float(x + w), float(y + h)]

            # 计算质心（基于轮廓的几何中心）
            moments = cv2.moments(contour)
            if moments['m00'] != 0:
                cx = moments['m10'] / moments['m00']
                cy = moments['m01'] / moments['m00']
                self.center = (float(cx), float(cy))
            else:
                # 如果无法计算质心，使用外接矩形中心
                self.center = (float(x + w / 2), float(y + h / 2))

            # 计算实际面积和周长
            self.area = float(cv2.contourArea(contour))
            self.perimeter = float(cv2.arcLength(contour, True))

            # 计算形状复杂度（圆形为4π，越复杂数值越大）
            if self.area > 0:
                self.shape_complexity = (self.perimeter ** 2) / self.area

            # 计算扩散速率（面积变化率）
            if old_area > 0 and self.area > 0:
                self.expansion_rate = (self.area - old_area) / old_area

        # 更新时间信息
        self.last_seen_frame = frame_num
        self.consecutive_frames += 1

        # 更新历史记录
        self.track_history.append(self.center)
        self.confidence_history.append(confidence)
        self.area_history.append(self.area)

        # 计算速度（像素/帧）
        if old_center != (0.0, 0.0) and old_center != self.center:
            self.velocity = (
                self.center[0] - old_center[0],
                self.center[1] - old_center[1]
            )

        # 判断是否为稳定目标（考虑气体特性：面积稳定性和置信度）
        if (self.consecutive_frames >= min_stable_frames and
                len(self.confidence_history) >= 3 and
                np.mean(list(self.confidence_history)) > 0.5 and
                len(self.area_history) >= 3 and
                np.std(list(self.area_history)) < np.mean(list(self.area_history)) * 0.3): # 面积变化不超过30%
            self.is_stable = True


class VideoProcessor:
    """视频处理器 - 整合YOLOv8 MOT功能和气体泄漏追踪"""

    def __init__(self, config=None, ocr_processor=None, gas_leakage_mode=True):
        """
        初始化视频处理器（整合YOLOv8 MOT功能）

        Args:
            config: 配置管理器（可选）
            ocr_processor: OCR处理器
            gas_leakage_mode: 是否启用气体泄漏追踪模式
        """
        self.config = config
        self.ocr_processor = ocr_processor
        self.is_processing = False
        self.logger = logger # 添加logger属性

        # 从配置中读取CUDA设置
        self._cuda_enabled = self._get_cuda_setting()

        # 创建线程安全的帧处理器实例
        from core.processors.frame_processor import FrameProcessor
        self.frame_processor = FrameProcessor(config=config)
        self.label_service = None # 将在处理时设置

        # FFmpeg视频合成设置
        self.temp_frames_dir = None
        self.frame_count = 0

        # 气体泄漏模式相关初始化（整合YOLOv8 MOT功能）
        self.gas_leakage_mode = gas_leakage_mode

        # YOLOv8 MOT 相关配置
        self.model_path = None # 模型路径将在 initialize_processing 中设置
        self.device = self._select_device()
        # 移除硬编码的 confidence_threshold，将在 process_frame 中使用传入的参数
        self.iou_threshold = 0.7
        # 追踪器配置路径将在initialize_processing中动态设置
        self.tracker_config_path = None

        # 追踪状态管理
        self.tracked_objects: Dict[int, LeakageTrackInfo] = {} # 使用新的LeakageTrackInfo
        self.processing_times = deque(maxlen=100)
        self.fps = 30.0 # 默认帧率
        self.colors = self._generate_colors(50) # 生成追踪颜色

        # 延迟初始化的组件
        self.model = None # YOLOv8模型
        self.camera_compensator = None # 相机运动补偿器
        self.gas_tracking_results = []

        # 新增：增强型目标追踪器（融合ByteTracker + 镜头运动补偿）
        self.enhanced_tracker = None

        # 气体追踪增强帧存储
        self._current_gas_enhanced_original = None

        # 时间戳和GPS信息收集
        self.temporal_gps_data = []
        self.ocr_statistics = {
            'total_frames_processed': 0,
            'frames_with_gps': 0,
            'frames_with_timestamp': 0,
            'frames_with_complete_data': 0
        }
        
        # 处理开始时间，用于计算相对时间戳
        self.processing_start_time = None

        logger.info(f"视频处理器初始化完成，气体泄漏模式: {'启用' if gas_leakage_mode else '禁用'}")

    # ==================== 设备和模型管理 ====================

    def _get_cuda_setting(self):
        """从配置中获取CUDA设置

        Returns:
            bool: 是否启用CUDA
        """
        try:
            # 优先从配置对象中读取
            if self.config and hasattr(self.config, 'enable_cuda'):
                cuda_enabled = self.config.enable_cuda
                logger.info(f"从配置对象读取CUDA设置: {cuda_enabled}")
                return cuda_enabled

            # 从环境变量读取
            import os
            enable_cuda_env = os.environ.get('ENABLE_CUDA', 'true').lower() == 'true'
            cuda_visible_devices = os.environ.get('CUDA_VISIBLE_DEVICES', '')

            # 如果CUDA_VISIBLE_DEVICES设置为-1，则禁用CUDA
            if cuda_visible_devices == '-1':
                logger.info("CUDA_VISIBLE_DEVICES=-1，禁用CUDA")
                return False

            logger.info(f"从环境变量读取CUDA设置: ENABLE_CUDA={enable_cuda_env}")
            return enable_cuda_env

        except Exception as e:
            logger.warning(f"读取CUDA配置失败，使用默认值False: {e}")
            return False

    def _select_device(self) -> str:
        """
        选择计算设备

        Returns:
            设备字符串 ('cuda' 或 'cpu')
        """
        try:
            if torch.cuda.is_available() and torch.cuda.device_count() > 0:
                # 额外检查设备是否真实可用
                try:
                    device_name = torch.cuda.get_device_name(0)
                    logger.info(f"使用GPU设备: {device_name}")
                    return 'cuda'
                except (AssertionError, RuntimeError) as e:
                    logger.warning(f"GPU设备不可用: {e}，回退到CPU模式")
                    logger.info("使用CPU设备")
                    return 'cpu'
            else:
                logger.info("使用CPU设备")
                return 'cpu'
        except Exception as e:
            logger.warning(f"设备选择异常: {e}，默认使用CPU")
            return 'cpu'

    def _initialize_model(self):
        """
        检查模型状态（模型已在initialize_processing中初始化）
        """
        if self.model is None and self.gas_leakage_mode:
            logger.warning("模型未在initialize_processing中正确初始化，请检查初始化流程")
            raise RuntimeError("模型未正确初始化，请先调用initialize_processing方法")

    def _validate_model_state(self):
        """
        验证模型状态
        """
        if self.model is None:
            raise RuntimeError("模型未初始化")

        # 确保模型处于评估模式
        if hasattr(self.model, 'eval'):
            self.model.eval()

    def _safe_model_inference(self, frame, conf_thresh):
        """
        安全的模型推理（使用增强型ObjectTracker进行检测和追踪）

        Args:
            frame: 输入帧
            conf_thresh: 置信度阈值

        Returns:
            tuple: (增强型追踪结果, camera_motion信息)
        """
        try:
            # 检查模型是否已初始化
            if self.model is None:
                logger.error("模型未初始化，请先调用initialize_processing方法")
                return []

            with torch.no_grad():
                # 使用增强型ObjectTracker进行检测和追踪
                if self.enhanced_tracker is not None:
                    return self._enhanced_tracker_inference(frame, conf_thresh)
                else:
                    # 回退到原有的推理方法（不支持camera_motion）
                    if self._cuda_enabled:
                        return self._cuda_inference(frame, conf_thresh), None
                    else:
                        return self._cpu_inference(frame, conf_thresh), None

        except Exception as e:
            import traceback
            logger.error(f"推理异常: {e}")
            logger.error(f"异常栈堆信息: {traceback.format_exc()}")
            return [], None

    def _enhanced_tracker_inference(self, frame, conf_thresh):
        """
        使用增强型ObjectTracker进行检测和追踪

        Args:
            frame: 输入帧
            conf_thresh: 置信度阈值

        Returns:
            增强型追踪结果
        """
        try:
            # 1. 执行YOLO检测（不使用追踪）
            if self._cuda_enabled:
                results = self.model(frame, conf=conf_thresh, device='cuda', verbose=False)
            else:
                results = self.model(frame, conf=conf_thresh, device='cpu', verbose=False)

            # 2. 提取检测结果
            detections = []
            # TODO: 调试信息 - 分析YOLO检测结果
            logger.debug(f"[DEBUG] _enhanced_tracker_inference YOLO检测结果:")
            logger.debug(f"  - results数量: {len(results) if results else 0}")
            
            if results and len(results) > 0:
                result = results[0]
                logger.debug(f"  - result对象存在: {result is not None}")
                logger.debug(f"  - 有boxes属性: {hasattr(result, 'boxes')}")
                
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    logger.debug(f"  - boxes数量: {len(boxes)}")
                    
                    if len(boxes) > 0:
                        # 转换为numpy格式
                        xyxy = boxes.xyxy.cpu().numpy() if hasattr(boxes.xyxy, 'cpu') else boxes.xyxy
                        conf = boxes.conf.cpu().numpy() if hasattr(boxes.conf, 'cpu') else boxes.conf
                        cls = boxes.cls.cpu().numpy() if hasattr(boxes.cls, 'cpu') else boxes.cls
                        
                        logger.debug(f"  - xyxy shape: {xyxy.shape if hasattr(xyxy, 'shape') else 'N/A'}")
                        logger.debug(f"  - conf shape: {conf.shape if hasattr(conf, 'shape') else 'N/A'}")
                        logger.debug(f"  - cls shape: {cls.shape if hasattr(cls, 'shape') else 'N/A'}")

                        # 构建检测结果
                        for i in range(len(xyxy)):
                            detection = {
                                'bbox': xyxy[i], # [x1, y1, x2, y2]
                                'confidence': float(conf[i]), # 修正字段名称
                                'class_id': int(cls[i]) # 修正字段名称
                            }
                            detections.append(detection)
                            logger.debug(f"  - 检测 {i}: bbox={xyxy[i]}, conf={float(conf[i])}, cls={int(cls[i])}")
                else:
                    logger.warning(f"[DEBUG] result.boxes为None或不存在")
            else:
                logger.warning(f"[DEBUG] YOLO检测结果为空")
            
            logger.debug(f"[DEBUG] 最终构建的detections数量: {len(detections)}")

            # 3. 使用增强型追踪器更新
            logger.debug(f"[DEBUG] 调用enhanced_tracker.update，传入{len(detections)}个检测结果")
            tracked_objects, camera_motion = self.enhanced_tracker.update(detections, frame)
            logger.debug(f"[DEBUG] enhanced_tracker.update返回{len(tracked_objects) if tracked_objects else 0}个追踪对象")

            # 4. 转换为兼容格式
            enhanced_results = []
            for obj in tracked_objects:
                # 创建模拟的YOLO结果格式
                # 注意：EnhancedSTrack对象的属性访问方式
                bbox_xyxy = obj.xyxy
                confidence = obj.score
                class_id = obj.cls
                track_id = obj.track_id

                enhanced_result = type('Result', (), {
                    'boxes': type('Boxes', (), {
                        'xyxy': torch.tensor([bbox_xyxy]),
                        'conf': torch.tensor([confidence]),
                        'cls': torch.tensor([class_id]),
                        'id': torch.tensor([track_id])
                    })()
                })()
                enhanced_results.append(enhanced_result)

            return enhanced_results, camera_motion

        except Exception as e:
            logger.error(f"增强型追踪器推理失败: {e}")
            import traceback
            logger.error(f"异常栈堆信息: {traceback.format_exc()}")
            return [], None

    def _cuda_inference(self, frame, conf_thresh):
        """
        CUDA模式推理（支持ByteTrack追踪）

        Args:
            frame: 输入帧
            conf_thresh: 置信度阈值

        Returns:
            模型推理结果
        """
        try:
            # 根据配置选择追踪或检测模式
            tracker_config = None
            if self.gas_leakage_mode and hasattr(self, 'tracker_config_path') and self.tracker_config_path:
                tracker_config = self.tracker_config_path
                logger.debug(f"CUDA追踪模式: {self.tracker_config_path}")
            else:
                logger.debug("CUDA检测模式")

            results = self.model.track(
                frame,
                conf=conf_thresh,
                iou=self.iou_threshold,
                tracker=tracker_config,
                persist=True, # 保持追踪ID的连续性
                verbose=False
            )
            return results

        except RuntimeError as e:
            if "cuda" in str(e).lower():
                logger.warning(f"CUDA错误: {e}，清理缓存后重试")
                self._safe_cuda_empty_cache()
                try:
                    # CUDA重试：使用普通检测模式
                    results = self.model(
                        frame,
                        conf=conf_thresh,
                        iou=self.iou_threshold,
                        verbose=False
                    )
                    return results
                except Exception:
                    logger.error("CUDA重试失败，返回空结果")
                    return []
            else:
                logger.error(f"CUDA推理错误: {e}")
                return []

    def _cpu_inference(self, frame, conf_thresh):
        """
        CPU模式推理（仅检测，不支持追踪）

        Args:
            frame: 输入帧
            conf_thresh: 置信度阈值

        Returns:
            模型推理结果
        """
        try:
            logger.debug("CPU检测模式")

            # 确保模型在CPU上
            if hasattr(self.model, 'to'):
                self.model.to('cpu')
                logger.debug("模型已移动到CPU")

            # 确保输入数据在CPU上
            if isinstance(frame, torch.Tensor):
                frame = frame.cpu()
                logger.debug("输入数据已移动到CPU")

            # CPU模式下直接使用检测，不使用追踪
            results = self.model(
                frame,
                conf=conf_thresh,
                iou=self.iou_threshold,
                verbose=False
            )
            return results

        except Exception as e:
            logger.error(f"CPU推理错误: {e}")
            return []

    def _safe_cuda_empty_cache(self):
        """
        安全地清理CUDA缓存，避免内存对齐错误（只有在CUDA启用时）
        """
        if self._cuda_enabled:
            safe_cuda_empty_cache()

    # ==================== 气体泄漏检测和追踪 ====================

    def _process_gas_leakage_frame(self, frame, confidence_threshold):
        """处理单帧的气体泄漏检测（支持ByteTrack追踪）

        Args:
            frame: 输入图像帧
            confidence_threshold: 置信度阈值

        Returns:
            YOLO格式的检测结果
        """
        try:
            # 检查模型是否已初始化
            if self.model is None:
                logger.error("模型未初始化，请先调用initialize_processing方法")
                return []

            # 统一使用track方法，根据配置自动选择追踪或检测模式
            tracker_config = None
            if hasattr(self, 'tracker_config_path') and self.tracker_config_path:
                tracker_config = self.tracker_config_path
                logger.debug(f"气体泄漏追踪模式: {self.tracker_config_path}")
            else:
                logger.debug("气体泄漏检测模式")

            results = self.model.track(
                frame,
                conf=confidence_threshold,
                tracker=tracker_config,
                persist=True,
                verbose=False
            )

            return results

        except Exception as e:
            logger.error(f"气体泄漏帧处理失败: {e}")
            return []

    def _process_tracking_results(self, result, frame: np.ndarray) -> Dict[str, Any]:
        """
        处理YOLOv8追踪结果（专门处理气体不规则形状）

        Args:
            result: YOLOv8返回的结果对象
            frame: 当前帧

        Returns:
            处理后的追踪结果
        """
        detections = []
        tracked_objects = []

        # 检查是否有检测结果
        if result.boxes is None:
            return {'detections': detections, 'tracked_objects': tracked_objects}

        # 提取边界框、置信度、类别ID和追踪ID
        boxes = result.boxes.xyxy.cpu().numpy() # 边界框坐标
        confidences = result.boxes.conf.cpu().numpy() # 置信度
        class_ids = result.boxes.cls.cpu().numpy().astype(int) # 类别ID

        # 获取追踪ID（支持增强型追踪器、ByteTrack或手动分配）
        if hasattr(self, 'enhanced_tracker') and self.enhanced_tracker is not None:
            # 增强型追踪器模式：从result.boxes.id获取追踪ID
            if hasattr(result.boxes, 'id') and result.boxes.id is not None:
                track_ids = result.boxes.id.cpu().numpy().astype(int)
                logger.debug(f"使用增强型追踪器ID: {len(track_ids)}个目标")
            else:
                # 如果没有ID信息，手动分配
                track_ids = self._assign_track_ids(boxes, confidences, class_ids)
                logger.debug(f"增强型追踪器手动分配ID: {len(track_ids)}个目标")
        elif hasattr(self, 'tracker_config_path') and self.tracker_config_path and result.boxes.id is not None:
            # ByteTrack模式：直接使用返回的追踪ID
            track_ids = result.boxes.id.cpu().numpy().astype(int)
            logger.debug(f"使用ByteTrack追踪ID: {len(track_ids)}个目标")
        else:
            # 检测模式：手动分配追踪ID
            track_ids = self._assign_track_ids(boxes, confidences, class_ids)
            logger.debug(f"手动分配追踪ID: {len(track_ids)}个目标")

        # 获取类别名称
        if hasattr(result, 'names'):
            class_names = result.names
        else:
            class_names = {0: 'gas'} # 默认单一气体类别

        # 处理每个检测结果
        for i in range(len(boxes)):
            bbox = boxes[i].tolist() # [x1, y1, x2, y2]
            confidence = float(confidences[i])
            class_id = int(class_ids[i])
            class_name = class_names[class_id]

            # 获取手动分配的追踪ID
            original_track_id = track_ids[i] if track_ids is not None else -1

            # 提取气体轮廓（基于检测区域）
            contour = self._extract_gas_contour(frame, bbox)

            # 实时ID聚类检查
            clustered_track_id = self._apply_realtime_id_clustering(original_track_id, {
                'bbox': bbox,
                'confidence': confidence,
                'class_name': class_name,
                'contour': contour,
                'center': ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2),
                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                'frame_index': self.frame_count
            })

            # 使用聚类后的ID
            track_id = clustered_track_id

            # 计算气体浓度（新增功能）
            velocity = (0.0, 0.0) # 默认速度，后续会从追踪历史中计算
            if track_id in self.tracked_objects:
                velocity = self.tracked_objects[track_id].velocity

            gas_concentration = self._estimate_relative_concentration(
                frame, contour, velocity, ambient_temp=25.0
            )

            # 构建检测结果
            detection = {
                'bbox': bbox,
                'confidence': confidence,
                'class_id': class_id,
                'class_name': class_name,
                'track_id': track_id,
                'original_track_id': original_track_id, # 保留原始ID用于调试
                'center': ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2),
                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                'contour': contour,
                'gas_concentration': gas_concentration # 新增气体浓度字段
            }
            detections.append(detection)

            # 如果有有效的追踪ID，更新追踪信息
            if track_id != -1:
                self._update_tracked_object(track_id, detection, frame)

                # 添加到追踪对象列表
                if track_id in self.tracked_objects:
                    tracked_obj = self.tracked_objects[track_id]
                    tracked_objects.append({
                        'track_id': track_id,
                        'original_track_id': original_track_id, # 用于调试
                        'detection': detection,
                        'track_history': list(tracked_obj.track_history),
                        'area_history': list(tracked_obj.area_history),
                        'expansion_rate': tracked_obj.expansion_rate,
                        'shape_complexity': tracked_obj.shape_complexity,
                        'gas_concentration': gas_concentration # 新增浓度信息
                    })

        return {'detections': detections, 'tracked_objects': tracked_objects}

    # ==================== 工具方法 ====================

    def _generate_colors(self, num_colors: int) -> List[Tuple[int, int, int]]:
        """
        为不同的追踪ID生成不同颜色

        Args:
            num_colors: 需要生成的颜色数量

        Returns:
            颜色列表，每个颜色为(R, G, B)元组
        """
        colors = []
        for i in range(num_colors):
            # 使用HSV色彩空间生成均匀分布的颜色
            hue = int(180 * i / num_colors)
            color = cv2.cvtColor(
                np.uint8([[[hue, 255, 255]]])
                , cv2.COLOR_HSV2RGB
            )[0][0]
            colors.append(tuple(map(int, color)))

        return colors

    def _assign_track_ids(self, boxes, confidences, class_ids):
        """
        手动分配追踪ID（基于IoU和位置匹配）

        Args:
            boxes: 检测框数组
            confidences: 置信度数组
            class_ids: 类别ID数组

        Returns:
            List[int]: 分配的追踪ID列表
        """
        track_ids = []

        for i, bbox in enumerate(boxes):
            confidence = confidences[i]
            class_id = class_ids[i]

            # 寻找最匹配的已有追踪对象
            best_match_id = -1
            best_iou = 0.0

            for existing_id, tracked_obj in self.tracked_objects.items():
                # 检查类别是否匹配
                if hasattr(tracked_obj, 'class_id') and tracked_obj.class_id != class_id:
                    continue

                # 计算IoU
                if hasattr(tracked_obj, 'bbox'):
                    iou = self._calculate_bbox_iou(bbox.tolist(), tracked_obj.bbox)
                    if iou > best_iou and iou > 0.3: # IoU阈值
                        best_iou = iou
                        best_match_id = existing_id

            # 如果找到匹配的追踪对象，使用其ID；否则分配新ID
            if best_match_id != -1:
                track_ids.append(best_match_id)
            else:
                # 分配新的追踪ID
                new_id = max(self.tracked_objects.keys()) + 1 if self.tracked_objects else 1
                track_ids.append(new_id)

        return track_ids

    def _apply_realtime_id_clustering(self, original_track_id, detection_info):
        """
        实时ID聚类：检查当前检测是否应该与已有轨迹合并

        Args:
            original_track_id: 原始追踪ID
            detection_info: 当前检测信息

        Returns:
            int: 聚类后的追踪ID
        """
        if original_track_id == -1:
            return -1

        # 如果没有已有轨迹，直接返回原始ID
        if not self.tracked_objects:
            return original_track_id

        current_center = detection_info['center']
        current_bbox = detection_info['bbox']
        current_area = detection_info['area']
        current_confidence = detection_info['confidence']
        current_class = detection_info['class_name']

        # 寻找最相似的已有轨迹
        best_match_id = original_track_id
        best_similarity = 0.0

        for existing_id, tracked_obj in self.tracked_objects.items():
            if existing_id == original_track_id:
                continue

            # 只考虑相同类别的轨迹
            if hasattr(tracked_obj, 'class_name') and tracked_obj.class_name != current_class:
                continue

            # 计算空间相似度
            if len(tracked_obj.track_history) > 0:
                last_center = tracked_obj.track_history[-1]
                distance = np.sqrt((current_center[0] - last_center[0]) ** 2 +
                                   (current_center[1] - last_center[1]) ** 2)

                # 距离阈值：如果距离太远，不考虑合并
                max_distance = 100.0 # 像素
                if distance > max_distance:
                    continue

                spatial_similarity = max(0.0, 1.0 - distance / max_distance)
            else:
                continue

            # 计算IoU相似度
            if hasattr(tracked_obj, 'bbox'):
                iou = self._calculate_bbox_iou(current_bbox, tracked_obj.bbox)
            else:
                iou = 0.0

            # 计算面积相似度
            if hasattr(tracked_obj, 'area_history') and len(tracked_obj.area_history) > 0:
                last_area = tracked_obj.area_history[-1]
                area_ratio = min(current_area, last_area) / max(current_area, last_area)
            else:
                area_ratio = 0.0

            # 计算置信度相似度
            if hasattr(tracked_obj, 'confidence_history') and len(tracked_obj.confidence_history) > 0:
                # 修复：deque对象不支持切片，需要转换为list
                recent_confidences = list(tracked_obj.confidence_history)[-5:] # 最近5帧的平均置信度
                avg_confidence = np.mean(recent_confidences)
                confidence_similarity = min(current_confidence, avg_confidence) / max(current_confidence,
                                                                                      avg_confidence)
            else:
                confidence_similarity = 0.5

            # 综合相似度计算
            total_similarity = (
                    spatial_similarity * 0.4 +
                    iou * 0.3 +
                    area_ratio * 0.2 +
                    confidence_similarity * 0.1
            )

            # 如果相似度足够高，考虑合并
            if total_similarity > best_similarity and total_similarity > 0.6:
                best_similarity = total_similarity
                best_match_id = existing_id

        # 如果找到了更好的匹配，记录合并信息
        if best_match_id != original_track_id:
            self.logger.info(
                f"实时ID聚类：将ID {original_track_id} 合并到 ID {best_match_id}，相似度: {best_similarity:.3f}")

        return best_match_id

    def _calculate_bbox_iou(self, bbox1, bbox2):
        """
        计算两个边界框的IoU

        Args:
            bbox1, bbox2: [x1, y1, x2, y2] 格式的边界框

        Returns:
            float: IoU值
        """
        # 计算交集区域
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # 计算并集区域
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _estimate_relative_concentration(self, frame: np.ndarray, contour: Optional[np.ndarray],
                                         velocity: Tuple[float, float],
                                         ambient_temp: float = 25.0) -> float:
        """
        基于像素强度、光流速度和温度估算相对气体浓度

        Args:
            frame: 当前帧图像
            contour: 气体轮廓
            velocity: 气体运动速度 (vx, vy)
            ambient_temp: 环境温度（摄氏度），默认25°C

        Returns:
            相对气体浓度值 (0.0-1.0)
        """
        if contour is None or len(contour) == 0:
            return 0.0

        try:
            # 1. 提取气体区域的像素强度
            mask = np.zeros(frame.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [contour], 255)

            # 转换为灰度图并提取ROI
            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gas_region = cv2.bitwise_and(gray_frame, mask)

            # 计算气体区域的平均像素强度
            gas_pixels = gas_region[mask > 0]
            if len(gas_pixels) == 0:
                return 0.0

            avg_intensity = np.mean(gas_pixels)
            intensity_std = np.std(gas_pixels)

            # 2. 计算速度因子（气体运动越快，可能浓度越高）
            speed = math.sqrt(velocity[0] ** 2 + velocity[1] ** 2)
            speed_factor = min(speed / 10.0, 1.0) # 归一化到0-1

            # 3. 温度补偿因子（基于理想气体定律的简化）
            # 假设标准温度为20°C，温度越高气体扩散越快
            temp_kelvin = ambient_temp + 273.15
            standard_temp_kelvin = 20.0 + 273.15
            temp_factor = standard_temp_kelvin / temp_kelvin

            # 4. 形状复杂度因子（不规则形状可能表示更高浓度）
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            if area > 0:
                shape_complexity = (perimeter ** 2) / (4 * math.pi * area) # 圆形为1
                complexity_factor = min(shape_complexity / 2.0, 1.0)
            else:
                complexity_factor = 0.0

            # 5. 综合计算相对浓度
            # 基础浓度：基于像素强度差异
            background_intensity = np.mean(gray_frame) # 背景平均强度
            intensity_diff = abs(avg_intensity - background_intensity) / 255.0

            # 强度变化因子（气体区域内的强度变化）
            intensity_variation = intensity_std / 255.0

            # 综合计算（权重可根据实际情况调整）
            relative_concentration = (
                    0.4 * intensity_diff + # 40% 像素强度差异
                    0.2 * speed_factor + # 20% 运动速度
                    0.2 * temp_factor + # 20% 温度补偿
                    0.1 * complexity_factor + # 10% 形状复杂度
                    0.1 * intensity_variation # 10% 强度变化
            )

            # 确保结果在合理范围内
            relative_concentration = max(0.0, min(1.0, relative_concentration))

            return relative_concentration

        except Exception as e:
            logger.warning(f"气体浓度估算失败: {e}")
            return 0.0

    def _process_tracking_results(self, result, frame: np.ndarray) -> Dict[str, Any]:
        """
        处理YOLOv8追踪结果（专门处理气体不规则形状）

        Args:
            result: YOLOv8返回的结果对象
            frame: 当前帧

        Returns:
            处理后的追踪结果
        """
        detections = []
        tracked_objects = []

        # 检查是否有检测结果
        if result.boxes is None:
            return {'detections': detections, 'tracked_objects': tracked_objects}

        # 提取边界框、置信度、类别ID和追踪ID
        boxes = result.boxes.xyxy.cpu().numpy() # 边界框坐标
        confidences = result.boxes.conf.cpu().numpy() # 置信度
        class_ids = result.boxes.cls.cpu().numpy().astype(int) # 类别ID

        # 获取追踪ID（如果存在）
        track_ids = None
        if hasattr(result.boxes, 'id') and result.boxes.id is not None:
            track_ids = result.boxes.id.cpu().numpy().astype(int)

        # 获取类别名称
        class_names = ['gas'] * len(class_ids) # 默认单一气体类别

        # 处理每个检测结果
        for i in range(len(boxes)):
            bbox = boxes[i].tolist() # [x1, y1, x2, y2]
            confidence = float(confidences[i])
            class_id = int(class_ids[i])
            class_name = class_names[class_id]

            # 获取追踪ID
            original_track_id = int(track_ids[i]) if track_ids is not None else -1

            # 提取气体轮廓（基于检测区域）
            contour = self._extract_gas_contour(frame, bbox)

            # 实时ID聚类检查
            clustered_track_id = self._apply_realtime_id_clustering(original_track_id, {
                'bbox': bbox,
                'confidence': confidence,
                'class_name': class_name,
                'contour': contour,
                'center': ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2),
                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                'frame_index': self.frame_count
            })

            # 使用聚类后的ID
            track_id = clustered_track_id

            # 计算气体浓度（新增功能）
            velocity = (0.0, 0.0) # 默认速度，后续会从追踪历史中计算
            if track_id in self.tracked_objects:
                velocity = self.tracked_objects[track_id].velocity

            gas_concentration = self._estimate_relative_concentration(
                frame, contour, velocity, ambient_temp=25.0
            )

            # 构建检测结果
            detection = {
                'bbox': bbox,
                'confidence': confidence,
                'class_id': class_id,
                'class_name': class_name,
                'track_id': track_id,
                'original_track_id': original_track_id, # 保留原始ID用于调试
                'center': ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2),
                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                'contour': contour,
                'gas_concentration': gas_concentration # 新增气体浓度字段
            }
            detections.append(detection)

            # 如果有有效的追踪ID，更新追踪信息
            if track_id != -1:
                self._update_tracked_object(track_id, detection, frame)

                # 添加到追踪对象列表
                if track_id in self.tracked_objects:
                    tracked_obj = self.tracked_objects[track_id]
                    tracked_objects.append({
                        'track_id': track_id,
                        'original_track_id': original_track_id, # 用于调试
                        'detection': detection,
                        'track_history': list(tracked_obj.track_history),
                        'area_history': list(tracked_obj.area_history),
                        'expansion_rate': tracked_obj.expansion_rate,
                        'shape_complexity': tracked_obj.shape_complexity,
                        'gas_concentration': gas_concentration # 新增浓度信息
                    })

        return {'detections': detections, 'tracked_objects': tracked_objects}

    def _extract_gas_contour(self, frame: np.ndarray, bbox: List[float]) -> Optional[np.ndarray]:
        """简化的轮廓提取 - 直接返回边界框轮廓"""
        try:
            x1, y1, x2, y2 = map(int, bbox)
            # 简化：直接使用边界框作为轮廓
            contour = np.array([[x1, y1], [x2, y1], [x2, y2], [x1, y2]], dtype=np.int32)
            return contour.reshape(-1, 1, 2)
        except Exception:
            return None

    def _update_tracked_object(self, track_id: int, detection: Dict, frame: np.ndarray):
        """
        更新追踪对象信息

        Args:
            track_id: 追踪ID
            detection: 检测结果
            frame: 当前帧
        """
        # 如果是新的追踪对象，创建新记录
        if track_id not in self.tracked_objects:
            self.tracked_objects[track_id] = LeakageTrackInfo(
                track_id=track_id,
                class_name=detection['class_name'],
                confidence=detection['confidence'],
                contour=detection.get('contour'),
                first_detected_frame=self.frame_count
            )
            logger.info(f"新的气体泄漏目标创建: ID={track_id}, 类别={detection['class_name']}")

        # 更新现有追踪对象
        tracked_obj = self.tracked_objects[track_id]
        tracked_obj.update_tracking_info(
            detection.get('contour'),
            detection['confidence'],
            self.frame_count
        )

        # 存储bbox信息用于ID聚类
        tracked_obj.bbox = detection['bbox']
        tracked_obj.class_name = detection['class_name']

    def _analyze_gas_dispersion(self) -> Dict[str, Any]:
        """简化的气体扩散分析"""
        return {
            'active_leakage_count': len([obj for obj in self.tracked_objects.values() if obj.is_stable]),
            'risk_level': 'low' if len(self.tracked_objects) < 3 else 'medium'
        }

    def draw_tracking_results(self, frame: np.ndarray, tracking_result: Dict[str, Any]) -> np.ndarray:
        """
        绘制气体追踪结果

        Args:
            frame: 输入帧
            tracking_result: 追踪结果

        Returns:
            绘制了追踪结果的帧
        """
        annotated_frame = frame.copy()

        for obj_data in tracking_result.get('tracked_objects', []):
            track_id = obj_data['track_id']
            detection = obj_data['detection']
            bbox = detection['bbox']
            confidence = detection['confidence']

            # 选择颜色
            color = self.colors[track_id % len(self.colors)] if self.colors else (0, 255, 0)

            # 绘制边界框
            cv2.rectangle(annotated_frame,
                          (int(bbox[0]), int(bbox[1])),
                          (int(bbox[2]), int(bbox[3])),
                          color, 2)

            # 绘制轮廓（如果存在）
            if detection.get('contour') is not None:
                cv2.drawContours(annotated_frame, [detection['contour']], -1, color, 2)

            # 绘制追踪ID和置信度
            label = f"Gas-{track_id}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(annotated_frame,
                          (int(bbox[0]), int(bbox[1]) - label_size[1] - 10),
                          (int(bbox[0]) + label_size[0], int(bbox[1])),
                          color, -1)
            cv2.putText(annotated_frame, label,
                        (int(bbox[0]), int(bbox[1]) - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制轨迹历史
            if track_id in self.tracked_objects:
                track_history = list(self.tracked_objects[track_id].track_history)
                if len(track_history) > 1:
                    for i in range(1, len(track_history)):
                        cv2.line(annotated_frame,
                                 (int(track_history[i - 1][0]), int(track_history[i - 1][1])),
                                 (int(track_history[i][0]), int(track_history[i][1])),
                                 color, 2)

        return annotated_frame

    def _get_tracking_statistics(self) -> Dict[str, Any]:
        """
        获取追踪统计信息

        Returns:
            追踪统计信息字典
        """
        active_tracks = [obj for obj in self.tracked_objects.values() if obj.is_stable]
        recent_tracks = [obj for obj in self.tracked_objects.values()
                         if self.frame_count - obj.last_seen_frame < 30]

        return {
            'total_tracks': len(self.tracked_objects),
            'active_stable_tracks': len(active_tracks),
            'recent_tracks': len(recent_tracks),
            'current_frame': self.frame_count,
            'average_confidence': np.mean([obj.confidence for obj in active_tracks]) if active_tracks else 0.0,
            'max_consecutive_frames': max([obj.consecutive_frames for obj in active_tracks], default=0),
            'average_area': np.mean([obj.area for obj in active_tracks]) if active_tracks else 0.0
        }

    def _calculate_leakage_summary(self, fps: float = 30.0) -> Dict[str, Any]:
        """
        计算泄漏汇总信息

        Args:
            fps: 视频帧率

        Returns:
            泄漏汇总信息字典
        """
        active_leakage_objects = [obj for obj in self.tracked_objects.values() if obj.is_stable]

        total_leakage_area = sum(obj.area for obj in active_leakage_objects)
        average_expansion_rate = np.mean(
            [obj.expansion_rate for obj in active_leakage_objects]) if active_leakage_objects else 0.0

        # 计算持续时间
        max_duration = 0
        if active_leakage_objects:
            max_duration = max(obj.consecutive_frames for obj in active_leakage_objects) / fps

        return {
            'total_active_leaks': len(active_leakage_objects),
            'total_leakage_area': total_leakage_area,
            'average_expansion_rate': average_expansion_rate,
            'max_leak_duration': max_duration,
            'severity_level': self._assess_severity_level(active_leakage_objects),
            'leak_locations': [(obj.center[0], obj.center[1]) for obj in active_leakage_objects]
        }

    def _assess_severity_level(self, active_objects: List[LeakageTrackInfo]) -> str:
        """
        Assess leakage severity level

        Args:
            active_objects: List of active leakage objects

        Returns:
            Severity level in English
        """
        if not active_objects:
            return "No Leakage"

        total_area = sum(obj.area for obj in active_objects)
        max_expansion = max(obj.expansion_rate for obj in active_objects) if active_objects else 0

        if total_area > 10000 or max_expansion > 0.5: # Large area or rapid expansion
            return "Severe"
        elif total_area > 5000 or max_expansion > 0.2:
            return "Medium"
        else:
            return "Minor"

    def _calculate_shape_features(self):
        """计算形状特征"""
        if self.contour is not None:
            hull = cv2.convexHull(self.contour)
            hull_area = cv2.contourArea(hull)
            solidity = float(self.area) / hull_area if hull_area > 0 else 0
            aspect_ratio = float(self.bbox[2] - self.bbox[0]) / (self.bbox[3] - self.bbox[1]) if (self.bbox[3] -
                                                                                                  self.bbox[
                                                                                                      1]) > 0 else 0
            self.shape_features = {'solidity': solidity, 'aspect_ratio': aspect_ratio}

    def _analyze_motion_pattern(self):
        """分析运动模式"""
        if len(self.track_history) >= 3:
            recent_velocities = [(self.track_history[i + 1][0] - self.track_history[i][0],
                                  self.track_history[i + 1][1] - self.track_history[i][1]) for i in range(-3, -1)]
            avg_velocity = np.mean(recent_velocities, axis=0)
            if np.linalg.norm(avg_velocity) < 1.0:
                self.motion_pattern = 'stationary'
            elif abs(avg_velocity[1]) > abs(avg_velocity[0]):
                self.motion_pattern = 'vertical_dispersion'
            else:
                self.motion_pattern = 'horizontal_dispersion'

    def _calculate_stability(self):
        """计算稳定性"""
        if len(self.confidence_history) > 0:
            self.stability_score = np.mean(self.confidence_history)
        if len(self.area_history) >= 5:
            area_changes = np.diff(self.area_history[-5:])
            self.shape_consistency = 1 - np.std(area_changes) / (np.mean(self.area_history[-5:]) + 1e-6)
        self.is_stable = self.stability_score > 0.7 and self.shape_consistency > 0.8 and self.consecutive_frames > 5

    def _apply_camera_motion_compensation(self, camera_motion: Dict):
        """应用相机运动补偿"""
        if 'translation' in camera_motion:
            dx, dy = camera_motion['translation']
            self.center = (self.center[0] - dx, self.center[1] - dy)
            self.camera_motion_compensated = True
            self.compensation_confidence = camera_motion.get('confidence', 0.8)

    def initialize_processing(self, video_path: str, model_path: str, output_path: str = None) -> Tuple:
        """
        初始化视频处理 - 使用FFmpeg合成方案

        Returns:
            (model, cap, temp_frames_dir, total_frames, frame_width, frame_height, fps, duration_seconds, output_path, writer_type)
        """
        self.is_processing = True
        """初始化视频处理资源"""

        # 设置处理开始时间
        self.processing_start_time = time.time()
        logger.info(f"视频处理开始时间已设置: {self.processing_start_time}")

        # 重置气体追踪结果列表，确保每次处理新视频时从空列表开始
        self.gas_tracking_results = []
        logger.info("气体追踪结果列表已重置")

        # 设置内部模型路径，用于气体泄漏追踪
        self.model_path = model_path

        # 在initialize_processing中完成模型加载和配置设置
        # 1. 根据设备类型加载模型
        model = YOLO(model_path)
        if self.device == 'cuda' and torch.cuda.is_available():
            model = model.cuda()
            logger.info(f"模型已加载到GPU设备: {torch.cuda.get_device_name(0)}")
        else:
            model = model.cpu()
            logger.info("模型已加载到CPU设备")

        # 2. 加载ByteTrack配置文件（默认启用气体泄漏追踪模式）
        try:
            # 构建配置文件的绝对路径
            config_file = "configs/gas_leakage_bytetrack.yaml"
            # 获取项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir)) # 向上两级到predict-service根目录
            tracker_config_path = os.path.join(project_root, config_file)

            # 检查配置文件是否存在
            if os.path.exists(tracker_config_path):
                logger.info(f"ByteTrack配置文件已找到: {tracker_config_path}")

                # 存储配置路径供后续track()方法使用
                self.tracker_config_path = tracker_config_path

                # 验证配置文件内容
                try:
                    with open(tracker_config_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)

                    # 检查是否包含必要的ByteTrack参数
                    required_params = ['tracker_type', 'track_buffer', 'track_high_thresh']
                    missing_params = [param for param in required_params if param not in config_data]

                    if not missing_params:
                        logger.info(f"ByteTrack气体泄漏配置验证成功，tracker_type: {config_data.get('tracker_type')}")
                    else:
                        logger.warning(f"配置文件缺少必要参数: {missing_params}")

                except Exception as yaml_e:
                    logger.warning(f"配置文件格式验证失败: {yaml_e}，但仍将使用该文件")

            else:
                logger.warning(f"ByteTrack配置文件未找到: {tracker_config_path}，使用默认配置")
                self.tracker_config_path = None

        except Exception as e:
            logger.error(f"加载ByteTrack配置失败: {e}，使用默认配置")
            self.tracker_config_path = None

        # 3. 初始化内部模型（用于气体泄漏追踪）
        self.model = model # 直接使用已加载的模型
        logger.info(f"气体泄漏追踪模型初始化完成: {model_path}")

        # 打开视频并获取视频信息
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video: {video_path}")

        # 获取视频信息
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration_seconds = total_frames / fps if fps > 0 else 0

        # 4. 初始化增强型目标追踪器（融合ByteTracker + 镜头运动补偿）
        # 修复：在获取fps后初始化追踪器
        try:
            self.enhanced_tracker = EnhancedObjectTracker(
                track_high_thresh=0.6,
                track_low_thresh=0.1,
                new_track_thresh=0.7,
                match_thresh=0.8,
                track_buffer=30,
                frame_rate=int(fps) if fps > 0 else 30,
                use_kalman=self._cuda_enabled # 根据CUDA设置决定是否使用卡尔曼滤波
            )
            logger.info(f"增强型目标追踪器初始化完成 (CUDA: {self._cuda_enabled})")
        except Exception as e:
            logger.error(f"增强型目标追踪器初始化失败: {e}")
            self.enhanced_tracker = None

        logger.info(
            f" Video info: {frame_width}x{frame_height}, {total_frames} frames, {fps:.2f} fps, {duration_seconds:.2f}s")

        # 设置输出路径
        if output_path is None:
            timestamp = int(time.time())
            prefix = "gas_leakage_" if self.gas_leakage_mode else "processed_"
            output_path = f"/app/video_cache/{prefix}video_{timestamp}.mp4"

        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 创建临时帧存储目录
        self.temp_frames_dir = tempfile.mkdtemp(prefix="video_frames_", dir="/app/video_cache")
        logger.info(f" Created temp frames directory: {self.temp_frames_dir}")

        # 重置增强型追踪器状态（如果存在）
        if hasattr(self, 'enhanced_tracker') and self.enhanced_tracker is not None:
            self.enhanced_tracker.reset()
            logger.info("增强型目标追踪器状态已重置")

        # 重置帧计数器和追踪结果
        self.frame_count = 0
        self.gas_tracking_results = []

        logger.info(f" Using FFmpeg for video synthesis: {output_path}")
        logger.info(f" Model type: {type(model).__name__}")
        logger.info(f" Gas leakage mode: {self.gas_leakage_mode}")

        return model, cap, self.temp_frames_dir, total_frames, frame_width, frame_height, fps, duration_seconds, output_path, 'ffmpeg'

    def create_combined_frame(self, frame, optical_flow_result, confidence_result,
                              detection_result, frame_height,
                              detections=None, gas_tracking_results=None):
        """创建组合帧 - 增强版，支持气体泄漏追踪可视化"""
        try:

            # 获取原始帧尺寸
            original_height, original_width = frame.shape[:2]

            # 确保所有帧都有完全相同的尺寸和格式
            def standardize_frame(img, target_height, target_width):
                """标准化帧格式和尺寸"""
                # 检查输入类型
                if not isinstance(img, np.ndarray):
                    logger.warning(f"standardize_frame接收到非numpy数组: {type(img)}, 创建默认图像")
                    img = np.zeros((target_height, target_width, 3), dtype=np.uint8)
                    return img

                # 确保是3通道BGR格式
                if len(img.shape) == 2:
                    img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                elif len(img.shape) == 3 and img.shape[2] == 1:
                    img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                elif len(img.shape) == 3 and img.shape[2] == 4:
                    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

                # 确保尺寸完全匹配
                if img.shape[:2] != (target_height, target_width):
                    img = cv2.resize(img, (target_width, target_height))

                # 确保是uint8类型
                if img.dtype != np.uint8:
                    if img.max() <= 1.0:
                        img = (img * 255).astype(np.uint8)
                    else:
                        img = img.astype(np.uint8)

                # 确保内存连续
                img = np.ascontiguousarray(img)
                return img

            # 如果是气体泄漏模式且有追踪结果，使用追踪可视化
            if gas_tracking_results:
                # 整合后的绘制逻辑
                annotated_frame = self.draw_tracking_results(frame, gas_tracking_results)
                annotated_frame = standardize_frame(annotated_frame, original_height, original_width)

                # 在左上角添加气体追踪信息（这些信息将在四宫格的检测结果区域显示）
                info_lines = [
                    f"GAS TRACKING MODE",
                    f"Active Leaks: {gas_tracking_results.get('gas_dispersion_analysis', {}).get('active_leakage_count', 0)}",
                    f"Risk Level: {gas_tracking_results.get('gas_dispersion_analysis', {}).get('risk_level', 'unknown')}",
                    f"Camera Compensation: {'ON' if gas_tracking_results.get('camera_motion', {}).get('confidence', 0) > 0.3 else 'OFF'}"
                ]

                y_offset = 30
                for line in info_lines:
                    # 绘制背景
                    (text_width, text_height), _ = cv2.getTextSize(line, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)
                    cv2.rectangle(annotated_frame, (10, y_offset - text_height - 5),
                                  (10 + text_width + 10, y_offset + 5), (0, 0, 0), -1)
                    # 绘制文字
                    cv2.putText(annotated_frame, line, (15, y_offset),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    y_offset += 30

                # 将气体追踪信息添加到检测结果帧中，然后继续四宫格布局
                detection_result = annotated_frame

            # 标准化所有帧
            frame = standardize_frame(frame, original_height, original_width)
            optical_flow_result = standardize_frame(optical_flow_result, original_height, original_width)
            confidence_result = standardize_frame(confidence_result, original_height, original_width)
            detection_result = standardize_frame(detection_result, original_height, original_width)

            # 计算组合帧的最终尺寸
            combined_width = original_width * 2
            combined_height = original_height * 2

            # 创建组合帧画布
            combined_frame = np.zeros((combined_height, combined_width, 3), dtype=np.uint8)

            # 使用精确的坐标放置各个子图
            # 左上：原始检测结果
            combined_frame[0:original_height, 0:original_width] = detection_result

            # 右上：光流结果
            combined_frame[0:original_height, original_width:combined_width] = optical_flow_result

            # 左下：置信度热力图
            combined_frame[original_height:combined_height, 0:original_width] = confidence_result

            # 右下：原始帧（带标注）- 优先显示气体追踪结果
            if gas_tracking_results and gas_tracking_results.get('tracked_objects'):
                # 修复：优先在右下角绘制气体追踪结果
                annotated_original = frame.copy()
                for obj_data in gas_tracking_results.get('tracked_objects', []):
                    detection = obj_data.get('detection', {})
                    track_id = obj_data.get('track_id', -1)
                    confidence = detection.get('confidence', 0)
                    class_name = detection.get('class_name', 'Gas')

                    # 选择颜色
                    color = self.colors[track_id % len(self.colors)] if self.colors and track_id >= 0 else (0, 255, 255)

                    # 绘制轮廓（如果存在）
                    if detection.get('contour') is not None:
                        contour = detection['contour']
                        if isinstance(contour, np.ndarray) and len(contour) > 0:
                            # 绘制轮廓
                            cv2.drawContours(annotated_original, [contour], -1, color, 2)

                            # 填充半透明区域
                            overlay = annotated_original.copy()
                            cv2.fillPoly(overlay, [contour], color)
                            annotated_original = cv2.addWeighted(annotated_original, 0.8, overlay, 0.2, 0)

                            # 计算轮廓中心点
                            M = cv2.moments(contour)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])
                            else:
                                cx, cy = int(contour[:, 0].mean()), int(contour[:, 1].mean())

                            # 绘制追踪ID和标签
                            label = f"Gas-{track_id}: {confidence:.2f}"
                            (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                            cv2.rectangle(annotated_original, (cx - label_width // 2, cy - label_height - 10),
                                          (cx + label_width // 2, cy), (0, 0, 0), -1)
                            cv2.putText(annotated_original, label, (cx - label_width // 2, cy - 5),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                    # 如果没有轮廓，使用边界框
                    elif detection.get('bbox'):
                        bbox = detection['bbox']
                        if len(bbox) >= 4:
                            x1, y1, x2, y2 = map(int, bbox[:4])
                            cv2.rectangle(annotated_original, (x1, y1), (x2, y2), color, 2)

                            # 绘制标签
                            label = f"Gas-{track_id}: {confidence:.2f}"
                            (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                            cv2.rectangle(annotated_original, (x1, y1 - label_height - 10),
                                          (x1 + label_width, y1), (0, 0, 0), -1)
                            cv2.putText(annotated_original, label, (x1, y1 - 5),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                combined_frame[original_height:combined_height, original_width:combined_width] = annotated_original
            elif hasattr(self, '_current_gas_enhanced_original') and self._current_gas_enhanced_original is not None:
                # 使用气体追踪增强的原始帧
                gas_enhanced_frame = standardize_frame(self._current_gas_enhanced_original, original_height,
                                                       original_width)
                combined_frame[original_height:combined_height, original_width:combined_width] = gas_enhanced_frame
            elif detections and len(detections) > 0:
                # 在原始帧上绘制检测结果
                annotated_original = frame.copy()
                for det in detections:
                    if isinstance(det, dict):
                        confidence = det.get('confidence', 0)
                        class_name = det.get('class_name', 'Unknown')

                        # 优先绘制轮廓（不规则形状）
                        if 'contour' in det and det['contour'] is not None:
                            contour = det['contour']
                            if isinstance(contour, np.ndarray) and len(contour) > 0:
                                # 绘制轮廓
                                cv2.drawContours(annotated_original, [contour], -1, (0, 255, 255), 2)

                                # 填充半透明区域
                                overlay = annotated_original.copy()
                                cv2.fillPoly(overlay, [contour], (0, 255, 255))
                                annotated_original = cv2.addWeighted(annotated_original, 0.8, overlay, 0.2, 0)

                                # 计算轮廓中心点用于标签位置
                                M = cv2.moments(contour)
                                if M["m00"] != 0:
                                    cx = int(M["m10"] / M["m00"])
                                    cy = int(M["m01"] / M["m00"])
                                else:
                                    cx, cy = int(contour[:, 0].mean()), int(contour[:, 1].mean())

                                # 绘制中心点
                                cv2.circle(annotated_original, (cx, cy), 3, (255, 0, 0), -1)

                                # 绘制标签（在轮廓中心）
                                label = f"{class_name}: {confidence:.2f}"
                                (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                                                                 1)
                                cv2.rectangle(annotated_original, (cx - label_width // 2, cy - label_height - 10),
                                              (cx + label_width // 2, cy), (0, 0, 0), -1)
                                cv2.putText(annotated_original, label, (cx - label_width // 2, cy - 5),
                                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                        # 如果没有轮廓，回退到边界框绘制
                        elif 'bbox' in det:
                            bbox = det['bbox']
                            if len(bbox) >= 4:
                                x1, y1, x2, y2 = map(int, bbox[:4])

                                # 绘制边界框
                                cv2.rectangle(annotated_original, (x1, y1), (x2, y2), (0, 255, 0), 2)

                                # 绘制标签
                                label = f"{class_name}: {confidence:.2f}"
                                (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                                                                 1)
                                cv2.rectangle(annotated_original, (x1, y1 - label_height - 10),
                                              (x1 + label_width, y1), (0, 255, 0), -1)
                                cv2.putText(annotated_original, label, (x1, y1 - 5),
                                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                combined_frame[original_height:combined_height, original_width:combined_width] = annotated_original
            else:
                combined_frame[original_height:combined_height, original_width:combined_width] = frame

            # 添加分隔线
            # 垂直分隔线
            cv2.line(combined_frame, (original_width, 0), (original_width, combined_height), (255, 255, 255), 2)
            # 水平分隔线
            cv2.line(combined_frame, (0, original_height), (combined_width, original_height), (255, 255, 255), 2)

            # 添加子图标题
            titles = ['Detection Result', 'Optical Flow', 'Confidence Map', 'Original + Annotations']
            positions = [(10, 25), (original_width + 10, 25), (10, original_height + 25),
                         (original_width + 10, original_height + 25)]

            for title, (x, y) in zip(titles, positions):
                # 背景
                (title_width, title_height), _ = cv2.getTextSize(title, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
                cv2.rectangle(combined_frame, (x, y - title_height - 5), (x + title_width, y), (0, 0, 0), -1)
                # 文字
                cv2.putText(combined_frame, title, (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 添加四宫格角落水印
            self._add_corner_watermarks(combined_frame, original_width, original_height)

            logger.debug(f" Combined frame created: {combined_frame.shape}")
            return combined_frame

        except Exception as e:
            logger.error(f" Error creating combined frame: {e}")
            logger.exception("Combined frame creation error details:")
            # 返回原始帧作为fallback
            return self.ensure_image_format(frame)

    def _add_corner_watermarks(self, combined_frame, original_width, original_height):
        """
        在四宫格的中心区域添加四个紧密连接的浅绿色渐变正方形水印

        Args:
            combined_frame: 组合帧图像
            original_width: 原始帧宽度
            original_height: 原始帧高度
        """
        # 正方形水印配置
        square_size = 60 # 正方形边长（增大到60像素）
        alpha = 0.3 # 透明度

        # 计算四宫格的中心位置
        center_x = original_width # 四宫格的水平中心
        center_y = original_height # 四宫格的垂直中心

        # 四个正方形的位置和渐变方向配置
        watermark_configs = [
            # 左上正方形 - 向右下渐变
            {
                'position': (center_x - square_size, center_y - square_size),
                'direction': 'bottom_right'
            },
            # 右上正方形 - 向左下渐变
            {
                'position': (center_x, center_y - square_size),
                'direction': 'bottom_left'
            },
            # 左下正方形 - 向右上渐变
            {
                'position': (center_x - square_size, center_y),
                'direction': 'top_right'
            },
            # 右下正方形 - 向左上渐变
            {
                'position': (center_x, center_y),
                'direction': 'top_left'
            }
        ]

        # 为每个位置创建不同方向的浅绿色渐变正方形
        for config in watermark_configs:
            x, y = config['position']
            direction = config['direction']

            # 确保不超出边界
            if (x >= 0 and y >= 0 and
                x + square_size <= combined_frame.shape[1] and
                y + square_size <= combined_frame.shape[0]):

                # 创建浅绿色渐变正方形
                square = self._create_green_gradient_square(square_size, direction)

                # 混合到原图像
                roi = combined_frame[y:y+square_size, x:x+square_size]
                blended = cv2.addWeighted(roi, 1-alpha, square, alpha, 0)
                combined_frame[y:y+square_size, x:x+square_size] = blended

    def _create_green_gradient_square(self, size, direction):
        """
        创建浅绿色线性渐变的正方形

        Args:
            size: 正方形边长
            direction: 渐变方向 ('top_left', 'top_right', 'bottom_left', 'bottom_right')

        Returns:
            浅绿色渐变正方形图像
        """
        # 创建空白正方形
        square = np.zeros((size, size, 3), dtype=np.uint8)

        # 定义浅绿色范围 (HSV)
        # 浅绿色色相范围: 60-80 (对应OpenCV的30-40)
        # 饱和度范围: 100-200 (浅色到中等饱和度)
        # 亮度范围: 180-255 (较亮)

        for y in range(size):
            for x in range(size):
                # 根据方向计算渐变进度 (0.0 到 1.0)
                if direction == 'bottom_right': # 左上到右下
                    progress = (x + y) / (2 * (size - 1))
                elif direction == 'bottom_left': # 右上到左下
                    progress = ((size - 1 - x) + y) / (2 * (size - 1))
                elif direction == 'top_right': # 左下到右上
                    progress = (x + (size - 1 - y)) / (2 * (size - 1))
                elif direction == 'top_left': # 右下到左上
                    progress = ((size - 1 - x) + (size - 1 - y)) / (2 * (size - 1))
                else:
                    progress = 0.5 # 默认中间值

                # 限制进度范围
                progress = max(0.0, min(1.0, progress))

                # 计算浅绿色HSV值
                # 色相: 固定在绿色范围 (OpenCV: 35 对应70度)
                hue = 35

                # 饱和度: 从浅到深 (120-180)
                saturation = int(120 + progress * 60)

                # 亮度: 从亮到稍暗 (220-180)
                value = int(220 - progress * 40)

                # 创建HSV颜色并转换为BGR
                hsv_color = np.array([[[hue, saturation, value]]], dtype=np.uint8)
                bgr_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2BGR)[0, 0]

                square[y, x] = bgr_color

        return square

    def _create_gradient_square(self, size, start_hue=0):
        """
        创建径向渐变的彩色正方形 (保留原方法以兼容其他代码)

        Args:
            size: 正方形边长
            start_hue: 起始色相值 (0-360度)

        Returns:
            渐变色正方形图像
        """
        # 创建空白正方形
        square = np.zeros((size, size, 3), dtype=np.uint8)

        # 计算中心点
        center = size // 2
        max_distance = center * 1.414 # 对角线距离

        for y in range(size):
            for x in range(size):
                # 计算到中心的距离
                distance = np.sqrt((x - center) ** 2 + (y - center) ** 2)

                # 根据距离计算色相偏移
                hue_offset = int((distance / max_distance) * 60) # 60度色相范围
                hue = (start_hue + hue_offset) % 360

                # 转换为OpenCV的色相范围 (0-179)
                cv_hue = int(hue / 2)

                # 创建HSV颜色
                hsv_color = np.array([[[cv_hue, 255, 255]]], dtype=np.uint8)
                bgr_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2BGR)[0, 0]

                square[y, x] = bgr_color

        return square

    def save_frame_to_temp(self, combined_frame: np.ndarray) -> bool:
        """
        保存帧到临时目录，供FFmpeg后续合成

        Args:
            combined_frame: 组合后的帧

        Returns:
            是否成功保存
        """
        try:
            if self.temp_frames_dir is None:
                logger.error(" Temp frames directory not initialized")
                return False

            # 生成帧文件名（确保排序正确）
            frame_filename = f"frame_{self.frame_count:08d}.png"
            frame_path = os.path.join(self.temp_frames_dir, frame_filename)

            # 保存帧
            success = cv2.imwrite(frame_path, combined_frame)

            if success:
                self.frame_count += 1
                logger.debug(f" Saved frame {self.frame_count}: {frame_path}")
                return True
            else:
                logger.error(f" Failed to save frame: {frame_path}")
                return False

        except Exception as e:
            logger.error(f" Error saving frame to temp: {e}")
            return False

    def synthesize_video_with_ffmpeg(self, output_path: str, fps: float) -> bool:
        """
        使用FFmpeg合成视频

        Args:
            output_path: 输出视频路径
            fps: 帧率

        Returns:
            是否成功合成
        """
        try:
            if self.temp_frames_dir is None or self.frame_count == 0:
                logger.error(" No frames to synthesize")
                return False

            logger.info(f" Starting FFmpeg video synthesis...")
            logger.info(f" - Input frames: {self.frame_count} frames in {self.temp_frames_dir}")
            logger.info(f" - Output: {output_path}")
            logger.info(f" - FPS: {fps}")

            # 构建FFmpeg命令
            input_pattern = os.path.join(self.temp_frames_dir, "frame_%08d.png")

            ffmpeg_cmd = [
                'ffmpeg',
                '-y', # 覆盖输出文件
                '-framerate', str(fps), # 输入帧率
                '-i', input_pattern, # 输入文件模式
                '-c:v', 'libx264', # 使用H.264编码器
                '-preset', 'fast', # 编码速度
                '-crf', '23', # 质量参数
                '-pix_fmt', 'yuv420p', # 像素格式
                '-movflags', '+faststart', # 优化流媒体
                output_path
            ]

            logger.info(f" FFmpeg command: {' '.join(ffmpeg_cmd)}")

            # 执行FFmpeg命令
            start_time = time.time()
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=300 # 5分钟超时
            )

            end_time = time.time()
            synthesis_time = end_time - start_time

            if result.returncode == 0:
                # 检查输出文件
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    file_size = os.path.getsize(output_path) / (1024 * 1024) # MB
                    logger.info(f" Video synthesis successful!")
                    logger.info(f" - Time: {synthesis_time:.2f}s")
                    logger.info(f" - Output size: {file_size:.2f}MB")
                    logger.info(f" - Frames processed: {self.frame_count}")
                    return True
                else:
                    logger.error(" FFmpeg completed but output file is missing or empty")
                    return False
            else:
                logger.error(f"FFmpeg failed with return code: {result.returncode}")
                logger.error(f"FFmpeg stderr: {result.stderr}")
                if result.stdout:
                    logger.info(f"FFmpeg stdout: {result.stdout}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(" FFmpeg synthesis timed out")
            return False
        except Exception as e:
            logger.error(f" Error during FFmpeg synthesis: {e}")
            return False

    def validate_video_duration(self, video_path: str, max_duration: int = 1200) -> None:
        """验证视频时长"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f'Failed to open video: {video_path}')

        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            duration = total_frames / fps

            if duration > max_duration:
                raise ValueError(
                    f'Video duration exceeds {max_duration / 60:.0f} minutes: {duration / 60:.2f} minutes'
                )
        finally:
            cap.release()

    def process_frame(self, frame, model, confidence_threshold, prev_frame_gray,
                      frame_gray, scale, leak_area, fps, task_id=None, label_service=None):
        """
        处理单帧图像（整合YOLOv8 MOT功能）

        Args:
            frame: 输入图像帧
            model: YOLO模型（在气体模式下会被忽略，使用内置模型）
            confidence_threshold: 置信度阈值
            其他参数: 兼容性参数

        Returns:
            包含检测和追踪结果的字典
        """
        start_time = time.time()

        # 统一处理模式（整合气体泄漏和标准模式）
        # 1. 首先执行标准模式处理（光流、置信度等）
        standard_result = self.frame_processor.process_frame(
            frame, model, confidence_threshold, prev_frame_gray,
            frame_gray, scale, leak_area, fps, task_id, label_service
        )

        # 2. 如果启用气体泄漏模式，进行额外的气体追踪处理

        try:
            # 更新帧计数
            self.frame_count += 1
            # 使用传入的置信度阈值
            conf_thresh = confidence_threshold if confidence_threshold is not None else 0.5

            # 使用YOLOv8内置的track()方法进行检测和追踪（带错误处理）
            results, camera_motion = self._safe_model_inference(
                frame, conf_thresh
            )

            # 处理追踪结果 - 检查results是否为空
            if results and len(results) > 0:
                tracking_result = self._process_tracking_results(results[0], frame)
            else:
                # 如果没有检测结果，创建空的追踪结果
                tracking_result = {
                    'detections': [],
                    'tracked_objects': [],
                    'frame_analysis': {
                        'total_detections': 0,
                        'confidence_stats': {'mean': 0, 'max': 0, 'min': 0}
                    }
                }
            
            # 将camera_motion信息添加到tracking_result中
            if camera_motion is not None:
                tracking_result['camera_motion'] = {
                    'translation': camera_motion.translation,
                    'rotation': camera_motion.rotation,
                    'scale': camera_motion.scale,
                    'confidence': camera_motion.confidence
                }
            else:
                tracking_result['camera_motion'] = None

            # 进行气体扩散分析
            dispersion_analysis = self._analyze_gas_dispersion()

            # 优化：在原始帧上绘制气体追踪结果，保护四宫格渲染
            # 创建气体追踪增强的原始帧，而不是覆盖检测结果
            gas_enhanced_original = self.draw_tracking_results(frame.copy(), tracking_result)

            # 存储气体追踪增强帧，供create_combined_frame使用
            self._current_gas_enhanced_original = gas_enhanced_original

            # 计算处理时间
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)

            # 3. 优化：整合标准模式和气体追踪结果，保护四宫格渲染
            if isinstance(standard_result, dict):
                # 保留标准模式的光流和置信度结果，只增强原始帧部分
                gas_tracking_data = {
                    'frame_number': self.frame_count,
                    'timestamp': time.time() - self.processing_start_time if self.processing_start_time else 0.0,
                    'detections': tracking_result['detections'],
                    'tracked_objects': tracking_result['tracked_objects'],
                    'tracking_statistics': self._get_tracking_statistics(),
                    'leakage_summary': self._calculate_leakage_summary(fps or self.fps),
                    'dispersion_analysis': dispersion_analysis,
                    'processing_time': processing_time,
                    'total_objects_tracked': len(self.tracked_objects)
                }
                
                # 将气体追踪结果保存到列表中
                self.gas_tracking_results.append(gas_tracking_data)
                
                integrated_result = {
                    'optical_flow_result': standard_result.get('optical_flow', frame),
                    'confidence_result': standard_result.get('confidence', frame),
                    'detection_result': standard_result.get('detection', frame), # 保持原有检测结果
                    'optical_flow': standard_result.get('optical_flow', frame),
                    'confidence': standard_result.get('confidence', frame),
                    'detection': standard_result.get('detection', frame),
                    'gas_mode': True,
                    'standard_mode_data': standard_result, # 保留原始标准模式数据
                    # 新增：气体追踪增强的原始帧，用于四宫格右下角显示
                    'gas_enhanced_original': gas_enhanced_original,
                    'gas_tracking_results': gas_tracking_data
                }
                return integrated_result
            else:
                # 如果标准模式返回异常，仅返回气体追踪结果
                gas_tracking_data = {
                    'frame_number': self.frame_count,
                    'timestamp': time.time() - self.processing_start_time if self.processing_start_time else 0.0,
                    'detections': tracking_result['detections'],
                    'tracked_objects': tracking_result['tracked_objects'],
                    'tracking_statistics': self._get_tracking_statistics(),
                    'leakage_summary': self._calculate_leakage_summary(fps or self.fps),
                    'dispersion_analysis': dispersion_analysis,
                    'processing_time': processing_time,
                    'total_objects_tracked': len(self.tracked_objects)
                }
                
                # 将气体追踪结果保存到列表中
                self.gas_tracking_results.append(gas_tracking_data)
                
                return {
                    'optical_flow_result': gas_enhanced_original,
                    'confidence_result': gas_enhanced_original,
                    'detection_result': gas_enhanced_original,
                    'optical_flow': gas_enhanced_original,
                    'confidence': gas_enhanced_original,
                    'detection': gas_enhanced_original,
                    'gas_mode': True,
                    'gas_enhanced_original': gas_enhanced_original,
                    'gas_tracking_results': gas_tracking_data
                }
        except Exception as e:
            import traceback
            logger.error(f"气体追踪处理失败: {e}")
            logger.error(f"异常栈堆信息: {traceback.format_exc()}")
            # 清除气体追踪增强帧
            self._current_gas_enhanced_original = None
            # 气体追踪失败时，返回标准模式结果
            if isinstance(standard_result, dict):
                standard_result['gas_mode'] = False
                # 添加兼容性键名
                if 'optical_flow_result' in standard_result:
                    standard_result['optical_flow'] = standard_result['optical_flow_result']
                if 'confidence_result' in standard_result:
                    standard_result['confidence'] = standard_result['confidence_result']
                if 'detection_result' in standard_result:
                    standard_result['detection'] = standard_result['detection_result']
            return standard_result

    def cleanup_resources(self, model=None, cap=None, temp_frames_dir=None):
        """清理视频处理资源"""
        if cap:
            cap.release()

        # 清理临时帧目录
        if temp_frames_dir and os.path.exists(temp_frames_dir):
            try:
                shutil.rmtree(temp_frames_dir)
                logger.info(f" Cleaned up temp frames directory: {temp_frames_dir}")
            except Exception as e:
                logger.warning(f"Failed to clean up temp frames directory: {e}")

        # 清理gas_tracker资源
        if hasattr(self, 'gas_tracker') and self.gas_tracker is not None:
            try:
                if hasattr(self.gas_tracker, 'cleanup'):
                    self.gas_tracker.cleanup()
                    logger.info(" Gas tracker resources cleaned up")
            except Exception as e:
                logger.warning(f"Failed to clean up gas tracker: {e}")

        if model and hasattr(model, 'cpu'):
            # 不删除检测处理器的模型，只清理GPU缓存
            try:
                import torch
                if torch.cuda.is_available():
                    self._safe_cuda_empty_cache()
                import gc
                gc.collect()
            except Exception as e:
                logger.debug(f"Error during cleanup: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            self.is_processing = False

            # 清理临时帧目录
            if self.temp_frames_dir and os.path.exists(self.temp_frames_dir):
                shutil.rmtree(self.temp_frames_dir)
                logger.info(f" Cleaned up temp frames directory: {self.temp_frames_dir}")

            # 清理gas_tracker资源
            if hasattr(self, 'gas_tracker') and self.gas_tracker is not None:
                try:
                    if hasattr(self.gas_tracker, 'cleanup'):
                        self.gas_tracker.cleanup()
                        logger.info(" Gas tracker resources cleaned up")
                    self.gas_tracker = None
                except Exception as e:
                    logger.warning(f"Failed to clean up gas tracker: {e}")

            logger.info("Video processor cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up video processor: {e}")

    def get_gas_leakage_analysis_summary(self) -> Dict[str, Any]:
        """
        生成气体泄漏追踪分析汇总

        Returns:
            Dict: 气体泄漏分析结果
        """
        if not self.gas_leakage_mode or not self.gas_tracking_results:
            return {"status": "no_gas_tracking_data"}

        try:
            # 收集所有追踪对象
            all_tracked_objects = []
            active_leakage_events = []
            dispersion_data = []

            for frame_result in self.gas_tracking_results:
                tracked_objects = frame_result.get('tracked_objects', [])
                dispersion_analysis = frame_result.get('dispersion_analysis', {})

                all_tracked_objects.extend(tracked_objects)

                if dispersion_analysis.get('active_leakage_count', 0) > 0:
                    active_leakage_events.append({
                        'frame_number': frame_result['frame_number'],
                        'timestamp': frame_result['timestamp'],
                        'leakage_count': dispersion_analysis['active_leakage_count'],
                        'total_area': dispersion_analysis.get('total_leakage_area', 0),
                        'risk_level': dispersion_analysis.get('risk_level', 'unknown')
                    })

                dispersion_data.append(dispersion_analysis)

            # 统计分析
            unique_track_ids = set()
            max_simultaneous_leakages = 0
            total_leakage_duration = 0
            risk_level_distribution = {'low': 0, 'medium': 0, 'high': 0}

            # 新增：构建最终追踪泄漏点列表（去重和汇总）
            final_tracked_leakage_points = []
            track_id_summary = {}

            for obj in all_tracked_objects:
                track_id = obj.get('track_id')
                if track_id is None:
                    continue

                unique_track_ids.add(track_id)

                # 汇总同一追踪ID的信息
                if track_id not in track_id_summary:
                    track_id_summary[track_id] = {
                        'track_id': track_id,
                        'class_name': obj.get('class_name', 'gas_leak'),
                        'first_detected_frame': obj.get('first_detected_frame', 0),
                        'last_seen_frame': obj.get('last_seen_frame', 0),
                        'total_lifetime': obj.get('total_lifetime', 0),
                        'average_confidence': obj.get('confidence', 0),
                        'max_confidence': obj.get('confidence', 0),
                        'min_confidence': obj.get('confidence', 0),
                        'confidence_count': 1,
                        'confidence_sum': obj.get('confidence', 0),
                        'positions': [obj.get('center', [0, 0])],
                        'areas': [obj.get('area', 0)],
                        'is_stable': obj.get('is_stable', False),
                        'motion_pattern': obj.get('motion_pattern', 'unknown'),
                        'velocity': obj.get('velocity', [0, 0]),
                        'total_frames': 1
                    }
                else:
                    # 更新汇总信息
                    summary = track_id_summary[track_id]
                    summary['last_seen_frame'] = max(summary['last_seen_frame'], obj.get('last_seen_frame', 0))
                    summary['total_lifetime'] = max(summary['total_lifetime'], obj.get('total_lifetime', 0))

                    confidence = obj.get('confidence', 0)
                    summary['max_confidence'] = max(summary['max_confidence'], confidence)
                    summary['min_confidence'] = min(summary['min_confidence'], confidence)
                    summary['confidence_sum'] += confidence
                    summary['confidence_count'] += 1
                    summary['average_confidence'] = summary['confidence_sum'] / summary['confidence_count']

                    summary['positions'].append(obj.get('center', [0, 0]))
                    summary['areas'].append(obj.get('area', 0))
                    summary['total_frames'] += 1

                    # 更新稳定性（如果任一帧标记为稳定，则认为是稳定的）
                    if obj.get('is_stable', False):
                        summary['is_stable'] = True

            # 生成最终追踪点列表
            for track_id, summary in track_id_summary.items():
                # 计算平均位置
                positions = summary['positions']
                avg_x = sum(pos[0] for pos in positions) / len(positions) if positions else 0
                avg_y = sum(pos[1] for pos in positions) / len(positions) if positions else 0

                # 计算平均面积
                areas = summary['areas']
                avg_area = sum(areas) / len(areas) if areas else 0

                # 计算持续时间（假设30fps）
                duration_seconds = (summary['last_seen_frame'] - summary['first_detected_frame']) / 30.0

                final_point = {
                    'track_id': track_id,
                    'class_name': summary['class_name'],
                    'first_detected_frame': summary['first_detected_frame'],
                    'last_seen_frame': summary['last_seen_frame'],
                    'duration_seconds': duration_seconds,
                    'total_frames': summary['total_frames'],
                    'average_confidence': round(summary['average_confidence'], 3),
                    'max_confidence': round(summary['max_confidence'], 3),
                    'min_confidence': round(summary['min_confidence'], 3),
                    'average_position': [round(avg_x, 2), round(avg_y, 2)],
                    'average_area': round(avg_area, 2),
                    'is_stable': summary['is_stable'],
                    'motion_pattern': summary['motion_pattern'],
                    'velocity': summary['velocity'],
                    'trajectory_start': positions[0] if positions else [0, 0],
                    'trajectory_end': positions[-1] if positions else [0, 0],
                    # 添加历史数据用于扩散分析
                    'positions_history': positions,
                    'areas_history': areas,
                    'confidence_history': [summary['average_confidence']] * len(positions),  # 简化的置信度历史
                    'frame_numbers': list(range(summary['first_detected_frame'], summary['last_seen_frame'] + 1))[:len(positions)]
                }
                final_tracked_leakage_points.append(final_point)

            # 按帧分析
            for dispersion in dispersion_data:
                current_count = dispersion.get('active_leakage_count', 0)
                max_simultaneous_leakages = max(max_simultaneous_leakages, current_count)

                if current_count > 0:
                    total_leakage_duration += 1

                risk_level = dispersion.get('risk_level', 'low')
                if risk_level in risk_level_distribution:
                    risk_level_distribution[risk_level] += 1

            # 计算平均值
            total_frames = len(self.gas_tracking_results)
            leakage_frequency = total_leakage_duration / total_frames if total_frames > 0 else 0

            # 分析运动模式
            motion_patterns = {}
            for obj in all_tracked_objects:
                pattern = obj.get('motion_pattern', 'unknown')
                motion_patterns[pattern] = motion_patterns.get(pattern, 0) + 1

            # 评估整体风险等级
            overall_risk_level = self._assess_overall_risk_level(
                len(unique_track_ids), max_simultaneous_leakages, leakage_frequency, risk_level_distribution
            )

            # 镜头稳定性评估
            camera_motion_stats = self._analyze_camera_motion_from_results()

            return {
                'status': 'success',
                'total_frames_analyzed': total_frames,
                'unique_leakage_sources': len(unique_track_ids),
                'max_simultaneous_leakages': max_simultaneous_leakages,
                'leakage_frequency': float(leakage_frequency),
                'total_leakage_events': len(active_leakage_events),
                'risk_level_distribution': risk_level_distribution,
                'motion_pattern_distribution': motion_patterns,
                'active_leakage_events': active_leakage_events[:50], # 限制数量

                # 新增：最终追踪泄漏点列表（用于数据库存储）
                'final_tracked_leakage_points': final_tracked_leakage_points,

                'summary_statistics': {
                    'unique_leakage_sources': len(unique_track_ids),
                    'total_tracking_duration': total_leakage_duration,
                    'max_simultaneous_leakages': max_simultaneous_leakages,
                    'overall_risk_level': overall_risk_level,
                    'camera_stability_assessment': camera_motion_stats.get('stability_assessment', 'unknown'),
                    'avg_confidence': float(
                        np.mean(
                            [obj.get('confidence', 0) for obj in all_tracked_objects])) if all_tracked_objects else 0,
                    'avg_stability_score': float(np.mean(
                        [obj.get('stability_score', 0) for obj in all_tracked_objects])) if all_tracked_objects else 0,
                    'total_tracked_objects': len(all_tracked_objects),
                    'confirmed_stable_leaks': len([p for p in final_tracked_leakage_points if p['is_stable']])
                },
                'camera_motion_statistics': camera_motion_stats
            }

        except Exception as e:
            logger.error(f"生成气体泄漏分析汇总失败: {e}")

            import traceback
            logger.error(f"生成气体泄漏分析汇总失败: {e}")
            logger.error(f"异常栈堆信息: {traceback.format_exc()}")

            return {"status": "error", "error": str(e)}

    def _analyze_camera_motion_from_results(self) -> Dict[str, Any]:
        """从追踪结果中分析镜头运动统计"""
        motion_data = []
        compensation_successful = 0

        for frame_result in self.gas_tracking_results:
            camera_motion = frame_result.get('camera_motion')
            if camera_motion:
                motion_data.append(camera_motion)
                if camera_motion.get('confidence', 0) > 0.3:
                    compensation_successful += 1

        if not motion_data:
            return {"status": "no_motion_data"}

        # 计算统计
        translations = [motion.get('translation', (0, 0)) for motion in motion_data]
        motion_magnitudes = [np.sqrt(t[0] ** 2 + t[1] ** 2) for t in translations]
        confidences = [motion.get('confidence', 0) for motion in motion_data]

        motion_types = [motion.get('motion_type', 'unknown') for motion in motion_data]
        type_distribution = {t: motion_types.count(t) for t in set(motion_types)}

        return {
            'total_frames_with_motion': len(motion_data),
            'successful_compensation_rate': compensation_successful / len(motion_data) if motion_data else 0,
            'avg_motion_magnitude': float(np.mean(motion_magnitudes)) if motion_magnitudes else 0,
            'max_motion_magnitude': float(np.max(motion_magnitudes)) if motion_magnitudes else 0,
            'avg_confidence': float(np.mean(confidences)) if confidences else 0,
            'motion_type_distribution': type_distribution,
            'stability_assessment': self._assess_camera_stability(motion_magnitudes)
        }

    def _assess_overall_risk_level(self, unique_sources, max_simultaneous, frequency, risk_distribution) -> str:
        """评估整体风险等级"""
        # 基于多个因子评估
        risk_score = 0

        # 泄漏源数量影响
        if unique_sources >= 5:
            risk_score += 30
        elif unique_sources >= 3:
            risk_score += 20
        elif unique_sources >= 1:
            risk_score += 10

        # 同时泄漏数量影响
        if max_simultaneous >= 3:
            risk_score += 25
        elif max_simultaneous >= 2:
            risk_score += 15
        elif max_simultaneous >= 1:
            risk_score += 5

        # 泄漏频率影响
        if frequency >= 0.5: # 超过50%的帧有泄漏
            risk_score += 20
        elif frequency >= 0.2: # 超过20%的帧有泄漏
            risk_score += 10
        elif frequency > 0:
            risk_score += 5

        # 风险等级分布影响
        high_risk_frames = risk_distribution.get('high', 0)
        medium_risk_frames = risk_distribution.get('medium', 0)

        if high_risk_frames > 0:
            risk_score += 25
        elif medium_risk_frames > 0:
            risk_score += 10

        # 确定最终风险等级
        if risk_score >= 70:
            return 'critical'
        elif risk_score >= 50:
            return 'high'
        elif risk_score >= 30:
            return 'medium'
        elif risk_score > 0:
            return 'low'
        else:
            return 'none'

    def _assess_camera_stability(self, motion_magnitudes) -> str:
        """评估镜头稳定性"""
        if not motion_magnitudes:
            return "unknown"

        avg_magnitude = np.mean(motion_magnitudes)
        motion_variance = np.var(motion_magnitudes)

        if avg_magnitude < 2 and motion_variance < 4:
            return "very_stable"
        elif avg_magnitude < 5 and motion_variance < 16:
            return "stable"
        elif avg_magnitude < 10 and motion_variance < 64:
            return "moderately_stable"
        else:
            return "unstable"

    def get_status(self) -> Dict:
        """
        获取处理器状态

        Returns:
            Dict: 状态信息
        """
        status = {
            'is_processing': self.is_processing,
            'ocr_available': self.ocr_processor is not None,
            'temp_frames_dir': self.temp_frames_dir,
            'frame_count': self.frame_count,
            'gas_leakage_mode': self.gas_leakage_mode,
            'gas_tracker_available': hasattr(self, 'gas_tracker') and self.gas_tracker is not None
        }

        # 如果是气体泄漏模式，添加追踪统计
        if self.gas_leakage_mode and self.gas_tracker:
            status['gas_tracking_statistics'] = self.gas_tracker._get_tracking_statistics()

        return status

    def _get_video_info(self) -> Dict[str, Any]:
        """获取视频信息"""
        if self.video_info is not None:
            return self.video_info

        cap = cv2.VideoCapture(str(self.input_path))
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {self.input_path}")

        try:
            self.total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.frame_size = (width, height)

            self.video_info = {
                'total_frames': self.total_frames,
                'fps': self.fps,
                'width': width,
                'height': height,
                'duration': self.total_frames / self.fps if self.fps > 0 else 0
            }

            logger.info(f"Video info: {self.video_info}")
            return self.video_info

        finally:
            cap.release()

    def collect_ocr_data(self, frame_number: int, extracted_info: dict, fps: float):
        """收集OCR数据用于后续汇总分析"""
        try:
            timestamp_seconds = frame_number / fps if fps > 0 else 0

            # 区分OCR尝试和OCR成功
            # extracted_info为None表示该帧没有执行OCR
            # extracted_info为空dict表示执行了OCR但没有识别到内容

            if extracted_info is not None:
                # 执行了OCR的帧，更新统计信息
                self.ocr_statistics['ocr_attempts'] = self.ocr_statistics.get('ocr_attempts', 0) + 1

                has_gps = bool(extracted_info.get('Lat') and extracted_info.get('Lon'))
                has_timestamp = bool(extracted_info.get('UTC'))

                if has_gps:
                    self.ocr_statistics['frames_with_gps'] += 1
                if has_timestamp:
                    self.ocr_statistics['frames_with_timestamp'] += 1
                if has_gps and has_timestamp:
                    self.ocr_statistics['frames_with_complete_data'] += 1

                # 保存有效的OCR数据
                if has_gps or has_timestamp:
                    ocr_data = {
                        'frame_number': frame_number,
                        'timestamp_seconds': timestamp_seconds,
                        'utc_time': extracted_info.get('UTC'),
                        'latitude': extracted_info.get('Lat'),
                        'longitude': extracted_info.get('Lon'),
                        'has_complete_data': has_gps and has_timestamp,
                        'ocr_attempted': True
                    }

                    # 有完整数据的必须保存，其他的采样保存
                    if has_gps and has_timestamp:
                        self.temporal_gps_data.append(ocr_data)
                    elif frame_number % 20 == 0: # 降低采样频率，因为OCR本身就是10帧一次
                        self.temporal_gps_data.append(ocr_data)

        except Exception as e:
            logger.error(f"收集OCR数据失败: {e}")

        # 总是更新总帧数（无论是否执行OCR）
        # 注意：这里可能会被多次调用，需要确保不重复计数
        if not hasattr(self, '_last_counted_frame') or self._last_counted_frame != frame_number:
            self.ocr_statistics['total_frames_processed'] += 1
            self._last_counted_frame = frame_number

    def get_temporal_gps_summary(self) -> dict:
        """获取时间戳和GPS信息汇总"""
        try:
            if not self.temporal_gps_data:
                return {
                    "status": "no_ocr_data",
                    "statistics": self.ocr_statistics
                }

            # 分析GPS轨迹
            valid_gps_points = [
                data for data in self.temporal_gps_data
                if data.get('latitude') and data.get('longitude')
            ]

            # 分析时间戳序列
            valid_timestamps = [
                data for data in self.temporal_gps_data
                if data.get('utc_time')
            ]

            # 计算OCR采样和成功率
            ocr_attempts = self.ocr_statistics.get('ocr_attempts', 0)
            total_frames = self.ocr_statistics['total_frames_processed']

            summary = {
                "status": "success",
                "statistics": self.ocr_statistics,
                "total_collected_points": len(self.temporal_gps_data),
                "valid_gps_points": len(valid_gps_points),
                "valid_timestamps": len(valid_timestamps),
                "data_quality": {
                    "ocr_sampling_rate": ocr_attempts / max(total_frames, 1), # OCR采样率
                    "gps_success_rate": self.ocr_statistics['frames_with_gps'] / max(ocr_attempts, 1),
                    # GPS成功率（基于OCR尝试）
                    "timestamp_success_rate": self.ocr_statistics['frames_with_timestamp'] / max(ocr_attempts, 1),
                    # 时间戳成功率
                    "complete_data_success_rate": self.ocr_statistics['frames_with_complete_data'] / max(ocr_attempts,
                                                                                                         1),
                    # 完整数据成功率
                    "gps_coverage": self.ocr_statistics['frames_with_gps'] / max(total_frames, 1), # GPS覆盖率（基于总帧数）
                    "timestamp_coverage": self.ocr_statistics['frames_with_timestamp'] / max(total_frames, 1), # 时间戳覆盖率
                    "complete_data_coverage": self.ocr_statistics['frames_with_complete_data'] / max(total_frames, 1)
                    # 完整数据覆盖率
                }
            }

            # GPS轨迹分析
            if valid_gps_points:
                latitudes = [float(p['latitude']) for p in valid_gps_points if p['latitude']]
                longitudes = [float(p['longitude']) for p in valid_gps_points if p['longitude']]

                if latitudes and longitudes:
                    summary["gps_analysis"] = {
                        "start_position": {
                            "lat": latitudes[0],
                            "lon": longitudes[0],
                            "frame": valid_gps_points[0]['frame_number']
                        },
                        "end_position": {
                            "lat": latitudes[-1],
                            "lon": longitudes[-1],
                            "frame": valid_gps_points[-1]['frame_number']
                        },
                        "bounds": {
                            "north": max(latitudes),
                            "south": min(latitudes),
                            "east": max(longitudes),
                            "west": min(longitudes)
                        },
                        "center": {
                            "lat": sum(latitudes) / len(latitudes),
                            "lon": sum(longitudes) / len(longitudes)
                        }
                    }

            # 时间戳分析
            if valid_timestamps:
                summary["temporal_analysis"] = {
                    "start_time": valid_timestamps[0]['utc_time'],
                    "end_time": valid_timestamps[-1]['utc_time'],
                    "start_frame": valid_timestamps[0]['frame_number'],
                    "end_frame": valid_timestamps[-1]['frame_number']
                }

            # 修正水印检测统计（基于OCR尝试次数，而非总帧数）
            ocr_attempts = self.ocr_statistics.get('ocr_attempts', 0)
            if ocr_attempts > 0:
                # 基于实际OCR尝试计算成功率
                gps_success_rate = self.ocr_statistics['frames_with_gps'] / ocr_attempts
                timestamp_success_rate = self.ocr_statistics['frames_with_timestamp'] / ocr_attempts
                overall_success_rate = (gps_success_rate + timestamp_success_rate) / 2

                # 采样覆盖率
                sampling_coverage = ocr_attempts / max(total_frames, 1)

                summary["watermark_analysis"] = {
                    "ocr_attempts": ocr_attempts,
                    "total_frames": total_frames,
                    "sampling_coverage": sampling_coverage,
                    "gps_success_rate": gps_success_rate,
                    "timestamp_success_rate": timestamp_success_rate,
                    "overall_success_rate": overall_success_rate,
                    "detection_rate": overall_success_rate, # 向后兼容
                    "quality_assessment": "good" if overall_success_rate > 0.7 else "moderate" if overall_success_rate > 0.3 else "poor",
                    "recommended_improvement": "增强OCR识别算法" if overall_success_rate < 0.5 else "当前识别质量良好",
                    "sampling_efficiency": f"每{int(total_frames / max(ocr_attempts, 1))}帧采样一次" if ocr_attempts > 0 else "未执行OCR"
                }
            else:
                summary["watermark_analysis"] = {
                    "detection_rate": 0,
                    "quality_assessment": "no_ocr_data",
                    "recommended_improvement": "未执行OCR识别"
                }

            # 采样数据（用于报告显示，限制数量）
            summary["sample_data"] = self.temporal_gps_data[:20] # 只返回前20个采样点

            return summary

        except Exception as e:
            logger.error(f"生成时间戳GPS汇总失败: {e}")
            return {"status": "error", "error": str(e)}

    def get_ocr_data_summary(self):
        pass
